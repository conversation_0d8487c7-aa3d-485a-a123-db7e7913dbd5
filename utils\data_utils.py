
import segyio
import numpy as np
import pandas as pd
from tqdm import tqdm

# Import tkinter conditionally
try:
    import tkinter as tk
    # --- Ensure a global Tk root exists for dialogs ---
    try:
        root = tk.Tk()
        root.withdraw()
    except Exception as e:
        print(f"Warning: Could not initialize tkinter root: {e}")
except ImportError:
    print("Warning: tkinter not available. GUI dialogs will not work.")

# SEG-Y File Merging Utility
def merge_segy_batch_files(batch_files, output_file):
    """
    Merge multiple SEG-Y batch files into a single SEG-Y file.

    This function combines multiple SEG-Y files (typically from batch processing)
    into a single consolidated SEG-Y file while preserving header information.
    It verifies compatibility between files and handles trace concatenation.

    Args:
        batch_files: List of batch SEG-Y file paths to merge
        output_file: Path to the output merged SEG-Y file

    Returns:
        bool: True if merge was successful, False otherwise
    """
    if not batch_files:
        print("No batch files provided for merging.")
        return False

    print(f"Merging {len(batch_files)} batch files into {output_file}...")

    # Get total trace count and ensure all files have compatible specifications
    total_traces = 0
    specs = None

    for batch_file in batch_files:
        try:
            with segyio.open(batch_file, 'r', ignore_geometry=True) as f:
                if specs is None:
                    # Store specifications from first file
                    specs = segyio.tools.metadata(f)
                else:
                    # Verify compatibility with first file
                    new_specs = segyio.tools.metadata(f)
                    if (len(new_specs.samples) != len(specs.samples) or
                        not np.array_equal(new_specs.samples, specs.samples)):
                        print(f"Error: Incompatible sample specifications in {batch_file}")
                        return False

                # Add trace count from this file
                file_traces = f.tracecount
                total_traces += file_traces
        except Exception as e:
            print(f"Error reading batch file {batch_file}: {e}")
            return False

    if specs is None:
        print("Could not obtain specifications from batch files.")
        return False

    # Set the trace count for the merged file
    specs.tracecount = total_traces

    try:
        with segyio.create(output_file, specs) as dst:
            # Copy binary header from first file
            with segyio.open(batch_files[0], 'r', ignore_geometry=True) as src:
                dst.bin = src.bin

                # Copy text header if available
                try:
                    dst.text[0] = src.text[0]
                except:
                    pass

            # Copy traces and headers from all batch files
            dst_trace_index = 0
            for batch_file in tqdm(batch_files, desc=f"Merging batch files"):
                with segyio.open(batch_file, 'r', ignore_geometry=True) as src:
                    for src_trace_index in range(src.tracecount):
                        dst.header[dst_trace_index] = src.header[src_trace_index]
                        dst.trace[dst_trace_index] = src.trace[src_trace_index]
                        dst_trace_index += 1

        print(f"Successfully merged {len(batch_files)} batch files into {output_file} with {total_traces} traces.")
        return True

    except Exception as e:
        print(f"Error during merge: {e}")
        return False

# SEG-Y Header Management
class SegyHeaderLoader:
    """
    Handles loading and processing of SEG-Y file headers.

    This class extracts header information from SEG-Y files, including inline/crossline
    numbers and coordinate data. It provides functionality to customize header byte
    locations and scaling factors.
    """
    def __init__(self, segy_path):
        """Initialize with path to SEG-Y file and load headers.

        Args:
            segy_path: Path to the SEG-Y file
        """
        self.segy_path = segy_path
        self.source_file_path = self.segy_path # Add alias for compatibility

    def load_headers(self, inline_byte, crossline_byte, x_coord_byte, y_coord_byte, scaler_byte, use_custom_scaler=False):
        """
        Load headers using provided byte positions.

        Args:
            inline_byte: Byte position for inline number
            crossline_byte: Byte position for crossline number
            x_coord_byte: Byte position for X coordinate
            y_coord_byte: Byte position for Y coordinate
            scaler_byte: Either the byte position for scaler or a custom scaler value
            use_custom_scaler: If True, scaler_byte is treated as a custom scaler value
                              If False, scaler_byte is treated as a byte position
        """
        self.inline_byte = inline_byte
        self.crossline_byte = crossline_byte
        self.x_coord_byte = x_coord_byte
        self.y_coord_byte = y_coord_byte
        self.scaler_byte = scaler_byte
        self.use_custom_scaler = use_custom_scaler

        with segyio.open(self.segy_path, 'r', ignore_geometry=True) as segyfile:
            print("\nLoading SEG-Y header information...")

            # Extract header values with progress bars
            self.inlines = np.array([h[self.inline_byte] for h in tqdm(segyfile.header, desc="Loading inlines")])
            self.crosslines = np.array([h[self.crossline_byte] for h in tqdm(segyfile.header, desc="Loading crosslines")])

            # Store the original inline/crossline ranges before filtering
            self.original_inline_min = int(np.min(self.inlines))
            self.original_inline_max = int(np.max(self.inlines))
            self.original_xline_min = int(np.min(self.crosslines))
            self.original_xline_max = int(np.max(self.crosslines))

            # Convert coordinates to float64 to avoid type casting issues during scaling
            self.x_coords = np.array([float(h[self.x_coord_byte]) for h in tqdm(segyfile.header, desc="Loading X coordinates")], dtype=np.float64)
            self.y_coords = np.array([float(h[self.y_coord_byte]) for h in tqdm(segyfile.header, desc="Loading Y coordinates")], dtype=np.float64)

            # Apply coordinate scaling factor
            try:
                if use_custom_scaler:
                    # Use the provided custom scaler value directly
                    custom_scaler = float(self.scaler_byte)
                    if custom_scaler >= 0:
                        self.x_coords *= custom_scaler
                        self.y_coords *= custom_scaler
                    else:
                        self.x_coords /= abs(custom_scaler)
                        self.y_coords /= abs(custom_scaler)
                    print(f"Applied custom coordinate scaler: {custom_scaler}")
                else:
                    # Read the scaler from the SEG-Y header at the specified byte position
                    scalers = np.array([h[self.scaler_byte] for h in tqdm(segyfile.header, desc="Loading coordinate scalers")])
                    # Use the first non-zero scaler value (or 1 if all are zero)
                    scaler_value = float(next((s for s in scalers if s != 0), 1))

                    if scaler_value >= 0:
                        self.x_coords *= scaler_value
                        self.y_coords *= scaler_value
                    else:
                        self.x_coords /= abs(scaler_value)
                        self.y_coords /= abs(scaler_value)
                    print(f"Applied coordinate scaler from byte {self.scaler_byte}: {scaler_value}")
            except Exception as e:
                print(f"Warning: Error applying coordinate scaling: {e}")
                print("Continuing with unscaled coordinates.")

        # Identify unique trace locations (handle duplicate coordinates)
        xy = np.column_stack((self.x_coords, self.y_coords))
        _, unique_idx = np.unique(xy, axis=0, return_index=True)
        self.unique_indices = unique_idx

        # Filter arrays to keep only unique trace locations
        self.x_coords = self.x_coords[unique_idx]
        self.y_coords = self.y_coords[unique_idx]
        self.inlines = self.inlines[unique_idx]
        self.crosslines = self.crosslines[unique_idx]

    def get_inline_crossline_range(self):
        """
        Get the full range of inline and crossline values from the SEG-Y file.

        Returns:
            dict: Dictionary containing min/max values for inlines and crosslines
        """
        try:
            if hasattr(self, 'original_inline_min'):
                # Return the original ranges stored during loading
                return {
                    'inline_min': self.original_inline_min,
                    'inline_max': self.original_inline_max,
                    'xline_min': self.original_xline_min,
                    'xline_max': self.original_xline_max
                }
            elif hasattr(self, 'inlines') and hasattr(self, 'crosslines'):
                # Fallback to the filtered ranges
                return {
                    'inline_min': int(np.min(self.inlines)) if len(self.inlines) > 0 else 0,
                    'inline_max': int(np.max(self.inlines)) if len(self.inlines) > 0 else 0,
                    'xline_min': int(np.min(self.crosslines)) if len(self.crosslines) > 0 else 0,
                    'xline_max': int(np.max(self.crosslines)) if len(self.crosslines) > 0 else 0
                }
            else:
                # If neither original nor filtered ranges are available, return zeros
                print("Warning: No inline/crossline data available in header loader")
                return {
                    'inline_min': 0,
                    'inline_max': 0,
                    'xline_min': 0,
                    'xline_max': 0
                }
        except Exception as e:
            # Catch any exceptions and return a valid dictionary with zeros
            print(f"Error retrieving inline/crossline ranges: {e}")
            return {
                'inline_min': 0,
                'inline_max': 0,
                'xline_min': 0,
                'xline_max': 0
            }

# Well Data Management Functions
def load_excel_data(excel_file_path):
    """
    Load well marker data from Excel file.

    This function loads well marker data from an Excel file and validates
    that it contains the required columns for well-seismic integration.

    Args:
        excel_file_path: Path to Excel file containing well marker data

    Returns:
        pandas.DataFrame: DataFrame containing well marker data

    Raises:
        ValueError: If required columns are missing from the Excel file
    """
    df = pd.read_excel(excel_file_path)
    expected_cols = ["X", "Y", "Z", "MD", "Surface", "Well"]
    for col in expected_cols:
        if col not in df.columns:
            raise ValueError(f"Missing expected column '{col}' in Excel file.")
    return df

def select_surfaces(df, title="Select Surfaces"):
    """
    Display a dialog for selecting geological surfaces from well data.

    This function creates a GUI dialog with a multi-select listbox allowing
    users to choose which geological surfaces to include in the analysis.

    Args:
        df: DataFrame containing well marker data
        title: Title for the selection window

    Returns:
        list: List of selected surface names
    """
    if 'tk' not in globals():
        raise ImportError("tkinter is not available. Cannot display selection dialog.")

    unique_surfaces = sorted(df["Surface"].unique().astype(str))

    if 'root' not in globals():
        # Create a temporary root if needed
        temp_root = tk.Tk()
        temp_root.withdraw()
        selection_window = tk.Toplevel(temp_root)
    else:
        selection_window = tk.Toplevel(root)

    selection_window.title(title)
    selection_window.geometry("300x400")
    tk.Label(selection_window, text="Select surface(s) to include:").pack(pady=5)
    frame = tk.Frame(selection_window)
    frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    scrollbar = tk.Scrollbar(frame, orient="vertical")
    listbox = tk.Listbox(frame, selectmode=tk.MULTIPLE, yscrollcommand=scrollbar.set, width=40, height=15)
    scrollbar.config(command=listbox.yview)
    scrollbar.pack(side="right", fill="y")
    listbox.pack(side="left", fill="both", expand=True)
    for surf in unique_surfaces:
        listbox.insert(tk.END, surf)
    selected_indices = []
    def on_ok():
        nonlocal selected_indices
        selected_indices = listbox.curselection()
        selection_window.destroy()
    tk.Button(selection_window, text="OK", command=on_ok).pack(pady=10)
    selection_window.wait_window()

    if 'temp_root' in locals():
        temp_root.destroy()

    return [unique_surfaces[i] for i in selected_indices]

def select_well_marker_pairs(df_wells):
    """
    Display a dialog for selecting well-marker pairs for sample trace analysis.

    Args:
        df_wells: DataFrame containing well marker data

    Returns:
        list: List of selected well-marker pairs as DataFrame rows
    """
    if 'tk' not in globals():
        raise ImportError("tkinter is not available. Cannot display selection dialog.")

    well_marker_list = [f"{row['Well']} - {row['Surface']}" for _, row in df_wells.iterrows()]

    if 'root' not in globals():
        # Create a temporary root if needed
        temp_root = tk.Tk()
        temp_root.withdraw()
        selection_window = tk.Toplevel(temp_root)
    else:
        selection_window = tk.Toplevel(root)

    selection_window.title("Select Well-Marker Pairs for Sample Trace")
    selection_window.geometry("400x450")  # Increased height to fit filter controls

    # Add a frame for the filter controls
    filter_frame = tk.Frame(selection_window)
    filter_frame.pack(fill=tk.X, padx=10, pady=5)

    tk.Label(filter_frame, text="Filter by Surface:").pack(side=tk.LEFT)
    filter_entry = tk.Entry(filter_frame)
    filter_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

    # Initialize displayed indices with all items
    displayed_indices = list(range(len(well_marker_list)))

    def apply_filter():
        nonlocal displayed_indices
        keyword = filter_entry.get().strip().lower()
        if keyword:
            # Filter by surface (second part after " - ")
            displayed_indices = [
                i for i, item in enumerate(well_marker_list)
                if keyword in item.split(" - ")[1].lower()
            ]
        else:
            # Show all items if keyword is empty
            displayed_indices = list(range(len(well_marker_list)))
        # Update listbox
        listbox.delete(0, tk.END)
        for idx in displayed_indices:
            listbox.insert(tk.END, well_marker_list[idx])

    def clear_filter():
        filter_entry.delete(0, tk.END)
        apply_filter()

    # Add filter and clear buttons
    tk.Button(filter_frame, text="Filter", command=apply_filter).pack(side=tk.LEFT, padx=5)
    tk.Button(filter_frame, text="Clear Filter", command=clear_filter).pack(side=tk.LEFT, padx=5)

    # Instruction label
    tk.Label(selection_window, text="Select well-marker pair(s) for sample trace:").pack(pady=5)

    # Frame for listbox and scrollbar
    frame = tk.Frame(selection_window)
    frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    scrollbar = tk.Scrollbar(frame, orient="vertical")
    listbox = tk.Listbox(frame, selectmode=tk.MULTIPLE, yscrollcommand=scrollbar.set, width=50, height=15)
    scrollbar.config(command=listbox.yview)
    scrollbar.pack(side="right", fill="y")
    listbox.pack(side="left", fill="both", expand=True)

    # Initially populate the listbox with all items
    for idx in displayed_indices:
        listbox.insert(tk.END, well_marker_list[idx])

    selected_indices = []
    def on_ok():
        nonlocal selected_indices
        selected_positions = [int(x) for x in listbox.curselection()]
        # Map listbox positions to original indices
        selected_indices = [displayed_indices[pos] for pos in selected_positions]
        selection_window.destroy()

    tk.Button(selection_window, text="OK", command=on_ok).pack(pady=10)
    selection_window.wait_window()

    if 'temp_root' in locals():
        temp_root.destroy()

    return [df_wells.iloc[i] for i in selected_indices]

def get_nearest_trace_index(header_loader, well_x, well_y):
    """
    Find the nearest seismic trace index to a given X,Y coordinate.

    This function calculates the Euclidean distance from a specified point
    to all trace locations and returns the index of the closest trace.
    Used for correlating well locations with seismic data.

    Args:
        header_loader: SegyHeaderLoader object containing trace coordinates
        well_x: X-coordinate of the point (typically a well location)
        well_y: Y-coordinate of the point

    Returns:
        int: Index of the nearest trace in the SEG-Y file
    """
    # Stack X and Y coordinates for vectorized distance calculation
    coords = np.column_stack((header_loader.x_coords, header_loader.y_coords))

    # Calculate Euclidean distances from the point to all trace locations
    distances = np.linalg.norm(coords - np.array([well_x, well_y]), axis=1)

    # Return the trace index with the minimum distance
    return header_loader.unique_indices[np.argmin(distances)]

def get_surfaces(df):
    """
    Get unique surfaces from well data.

    This function returns the unique surfaces from the DataFrame.

    Args:
        df: DataFrame containing well marker data

    Returns:
        list: List of unique surface names
    """
    return sorted(df["Surface"].unique().astype(str))

def get_well_marker_pairs(df_wells):
    """
    Get well-marker pairs from well data.

    This function returns the well-marker pairs from the DataFrame.

    Args:
        df_wells: DataFrame containing well marker data

    Returns:
        dict: Dictionary mapping display strings to DataFrame indices
    """
    well_marker_dict = {}
    for idx, row in df_wells.iterrows():
        display_str = f"{row['Well']} - {row['Surface']}"
        well_marker_dict[display_str] = idx
    return well_marker_dict

def load_trace_sample(segy_path, trace_index):
    """
    Load a single trace from a SEG-Y file.

    This function opens a SEG-Y file and extracts the amplitude data
    for a specific trace index.

    Args:
        segy_path: Path to the SEG-Y file
        trace_index: Index of the trace to load

    Returns:
        numpy.ndarray: Amplitude data for the requested trace
    """
    try:
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            # Check if trace_index is valid
            if trace_index < 0 or trace_index >= segyfile.tracecount:
                print(f"Warning: Trace index {trace_index} out of range (0-{segyfile.tracecount-1})")
                return np.zeros(len(segyfile.samples))

            try:
                # Try to get the trace directly
                trace_data = segyfile.trace[trace_index]

                # Check if we got a valid trace
                if hasattr(trace_data, 'shape') or hasattr(trace_data, '__len__'):
                    return trace_data
                else:
                    print(f"Warning: Trace data has unexpected type: {type(trace_data)}")
                    # Try to create a trace from samples
                    return np.zeros(len(segyfile.samples))
            except Exception as e:
                print(f"Error accessing trace {trace_index}: {e}")
                # Return zeros with the correct length
                return np.zeros(len(segyfile.samples))
    except Exception as e:
        print(f"Error opening SEG-Y file: {e}")
        # Return a default empty trace
        return np.zeros(1000)  # Default size if we can't determine

def get_sampling_interval(segy_path):
    """
    Extract the time sampling interval from a SEG-Y file.

    This function reads the binary header of a SEG-Y file to determine
    the time sampling interval (dt) in seconds. If the header cannot be read,
    it falls back to a default value of 4 ms.

    Args:
        segy_path: Path to the SEG-Y file

    Returns:
        float: Sampling interval in seconds
    """
    try:
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            # Convert microseconds to seconds
            dt = segyfile.bin[segyio.BinField.Interval] / 1_000_000
        print(f"Sampling interval (dt): {dt} seconds")
        return dt
    except Exception as e:
        print(f"Error retrieving sampling interval: {e}")
        # Fall back to default 4 ms sampling rate
        return 0.004

def get_trace_count(segy_path):
    """
    Get the actual number of traces in a SEG-Y file by identifying the array that is not time samples.

    This function uses the sampling rate information from the SEGY file to determine
    which dimension in the data represents traces vs. time samples.

    Args:
        segy_path: Path to the SEG-Y file

    Returns:
        int: The number of traces in the SEG-Y file
    """
    try:
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            # Get the trace count directly from segyio
            trace_count = segyfile.tracecount

            # Get the number of samples per trace
            num_samples = len(segyfile.samples)

            # Get the sampling interval in seconds
            dt = segyfile.bin[segyio.BinField.Interval] / 1_000_000

            # Get a sample trace to verify dimensions
            if trace_count > 0:
                try:
                    # Try to get the first trace as a numpy array
                    sample_trace = segyfile.trace[0]

                    # Check if sample_trace has a length
                    if hasattr(sample_trace, '__len__'):
                        sample_length = len(sample_trace)
                        if sample_length != num_samples:
                            print(f"Warning: Sample trace length ({sample_length}) doesn't match expected samples ({num_samples})")
                    else:
                        print(f"Warning: Sample trace doesn't have a length attribute. Type: {type(sample_trace)}")
                        sample_length = num_samples  # Assume it matches
                except Exception as trace_e:
                    print(f"Warning: Could not access first trace: {trace_e}")
                    sample_length = num_samples  # Assume it matches

                # Calculate expected trace duration based on sampling rate
                expected_duration = num_samples * dt
                print(f"Expected trace duration: {expected_duration:.3f} seconds")

                # Verify that the trace count is not the time samples array
                # by checking if the trace count is reasonable compared to the sampling rate
                if trace_count < num_samples and num_samples * dt > 0.1:  # Typical seismic trace is at least 0.1 seconds
                    print(f"Verified trace count ({trace_count}) is not the time samples array ({num_samples})")
                else:
                    print(f"Note: Trace count ({trace_count}) might be confused with time samples ({num_samples})")

            print(f"SEG-Y file contains {trace_count} traces with {num_samples} samples per trace")
            return trace_count
    except Exception as e:
        print(f"Error determining trace count: {e}")
        return 0
