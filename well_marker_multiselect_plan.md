# Plan: Enable Multiple Well-Marker Pair Selections

## 1. Goal

Modify the Streamlit application to allow users to select multiple well-marker pairs in "Step 3: Select Area" when the "By well markers" mode is chosen. This aligns the functionality with the original Tkinter application's capabilities.

## 2. Current Situation

-   **[`app.py`](app.py:199-250):** Contains comments guiding the implementation of multi-select for well-marker pairs using `st.multiselect` in [`pages/select_area_page.py`](pages/select_area_page.py).
-   **[`pages/select_area_page.py`](pages/select_area_page.py):**
    -   Currently uses `st.selectbox` (line [`108`](pages/select_area_page.py:108) and [`365`](pages/select_area_page.py:365)) to allow selection of only a *single* well-marker pair. This is stored in `st.session_state.selected_well_marker_pairs`.
    -   It also has a `st.multiselect` for "Select Well Markers" (lines [`96-100`](pages/select_area_page.py:96) and [`353-357`](pages/select_area_page.py:353)) which filters individual markers. This will be kept, and the well-marker pair selection will be filtered based on these selected markers.
-   **[`pages/precompute_qc_page.py`](pages/precompute_qc_page.py):**
    -   Consumes `st.session_state.selected_data_for_precompute` and `st.session_state.selected_indices`.
    -   Displays selected well markers using `st.session_state.selected_well_markers` (line [`72`](pages/precompute_qc_page.py:72)).
-   **Downstream Impact:** The structure of `st.session_state.selected_data_for_precompute` and how `st.session_state.selected_indices` are derived will need to be adjusted to handle multiple selected pairs.

## 3. Proposed Changes

### 3.1. [`pages/select_area_page.py`](pages/select_area_page.py)

#### 3.1.1. Modify Well-Marker Pair Selection UI:
-   **Replace `st.selectbox` with `st.multiselect`:**
    -   Change lines [`108-112`](pages/select_area_page.py:108) (and the equivalent block around line [`365`](pages/select_area_page.py:365) in the trace selection part of the `render` function) from `st.selectbox` to `st.multiselect`.
    -   The `st.session_state.selected_well_marker_pairs` will now store a *list* of selected pair strings (e.g., `["WellA - MarkerX", "WellB - MarkerY"]`) instead of a single string.
    -   Provide a sensible default, e.g., select the first available pair if any.

    ```python
    # Example modification around line 108
    if filtered_pairs:
        # Default to selecting the first pair if none are selected yet, or if the previous single selection is in the list
        default_selection = []
        if st.session_state.get('selected_well_marker_pairs'):
            # If it was a single string before, make it a list
            current_selection = st.session_state.selected_well_marker_pairs
            if isinstance(current_selection, str):
                current_selection = [current_selection]
            # Keep valid previous selections that are in the filtered_pairs
            default_selection = [s for s in current_selection if s in filtered_pairs]
        
        if not default_selection and filtered_pairs: # If still empty and pairs exist, pick the first one
            default_selection = [filtered_pairs[0]]

        st.session_state.selected_well_marker_pairs = st.multiselect(
            "Select Well-Marker Pair(s)", # Changed label
            options=filtered_pairs,
            default=default_selection, # Ensure default is a list
            key="well_marker_pair_multiselect" # New key might be good
        )
    else:
        st.session_state.selected_well_marker_pairs = [] # Ensure it's an empty list
        st.info("No well-marker pairs available for the selected markers.")
    ```

#### 3.1.2. Update Logic for Populating `selected_data_for_precompute` and `selected_indices`:
-   The logic that runs when the "Next: Select Traces" or "Proceed to Pre-computation & QC" button is clicked (around lines [`547-590`](pages/select_area_page.py:547) and [`734-772`](pages/select_area_page.py:734)) needs to iterate through `st.session_state.selected_well_marker_pairs` (which is now a list).
-   For each selected pair:
    -   Parse the well name and surface name.
    -   Find the corresponding data in `st.session_state.well_df`.
    -   Get the nearest trace index using `get_nearest_trace_index`.
    -   Append this trace index to `st.session_state.selected_indices`.
    -   Append the relevant well and marker data (e.g., the row from `well_df` or a dictionary of relevant info) to a list that will become `st.session_state.selected_data_for_precompute`.
-   Ensure `selected_indices` contains unique trace indices if multiple well-marker pairs might point to the same trace.

    ```python
    # Example modification within the "Proceed" button logic for "By well markers"
    if st.session_state.selection_mode == "By well markers":
        if st.session_state.selected_well_marker_pairs and isinstance(st.session_state.selected_well_marker_pairs, list):
            temp_selected_indices = []
            temp_selected_data_for_precompute = []
            well_df = st.session_state.well_df

            for pair_label in st.session_state.selected_well_marker_pairs:
                well_name, surface_name = pair_label.split(" - ")
                # Find the original data for this pair
                marker_data_row = well_df[
                    (well_df['Well'] == well_name) & (well_df['Surface'] == surface_name)
                ]
                if not marker_data_row.empty:
                    marker_info = marker_data_row.iloc[0].to_dict()
                    trace_idx = get_nearest_trace_index(
                        st.session_state.header_loader,
                        marker_info["X"],
                        marker_info["Y"]
                    )
                    if trace_idx is not None:
                        temp_selected_indices.append(trace_idx)
                        # Store relevant info for precomputation, e.g., the marker_info dict
                        # and the trace_idx itself.
                        temp_selected_data_for_precompute.append({
                            "well_name": well_name,
                            "surface_name": surface_name,
                            "x_coord": marker_info["X"],
                            "y_coord": marker_info["Y"],
                            "z_coord": marker_info.get("Z"), # Or TWT_sec if used
                            "trace_index": trace_idx,
                            "original_marker_data": marker_info
                        })
            
            # Remove duplicate trace indices if necessary, though for well markers,
            # different pairs usually mean different locations.
            st.session_state.selected_indices = list(set(temp_selected_indices))
            st.session_state.selected_data_for_precompute = temp_selected_data_for_precompute
            st.session_state.area_selected = bool(st.session_state.selected_indices)
            st.session_state.area_selected_details = {
                'type': 'well_markers',
                'count': len(st.session_state.selected_indices),
                'pairs_count': len(st.session_state.selected_well_marker_pairs)
            }
            if st.session_state.area_selected:
                st.success(f"{len(st.session_state.selected_well_marker_pairs)} well-marker pair(s) selected, resulting in {len(st.session_state.selected_indices)} unique traces.")
            else:
                st.warning("No valid traces found for the selected well-marker pairs.")
        else:
            st.warning("No well-marker pairs selected.")
            st.session_state.area_selected = False
            st.session_state.selected_data_for_precompute = None
            st.session_state.selected_indices = []
    ```

### 3.2. [`pages/precompute_qc_page.py`](pages/precompute_qc_page.py)

-   **Display Information:**
    -   Line [`72`](pages/precompute_qc_page.py:72) currently shows `st.session_state.selected_well_markers`. This is fine.
    -   We should add a line to display the newly selected `st.session_state.selected_well_marker_pairs` (which is now a list of strings).
        ```python
        # Example modification around line 72
        if st.session_state.selection_mode == "By well markers":
            st.write(f"Selected well markers: {', '.join(st.session_state.selected_well_markers)}")
            if st.session_state.get('selected_well_marker_pairs'):
                 st.write(f"Selected well-marker pairs: {', '.join(st.session_state.selected_well_marker_pairs)}") # New line
            st.write(f"Number of selected traces: {len(selected_indices)}")
        ```
-   **Consumption of `selected_data_for_precompute`:**
    -   The structure of `selected_data_for_precompute` will change from a single dictionary/row to a list of dictionaries/rows.
    -   The logic in `run_precomputation` (called around line [`187`](pages/precompute_qc_page.py:187)) and how `qc_indices` are determined (line [`167`](pages/precompute_qc_page.py:167)) should inherently handle a list of traces derived from `selected_indices`. The primary data for precomputation is `original_traces` which is built from `selected_indices`.
    -   The key is that `selected_indices` (derived from potentially multiple well-marker pairs) correctly feeds into `load_trace_sample` loop (lines [`170-177`](pages/precompute_qc_page.py:170)). This seems to be the case already.
    -   No direct changes seem necessary here for processing, as it operates on `selected_indices`.

### 3.3. [`pages/analyze_data_page.py`](pages/analyze_data_page.py) (and potentially `utils/processing.py`, `utils/visualization.py`)

-   Similar to `precompute_qc_page.py`, this page likely consumes `st.session_state.selected_indices` and the processed data from the pre-computation step (`st.session_state.precomputed_data_output` or similar).
-   If any part of the analysis or visualization logic specifically relied on `selected_data_for_precompute` being a single item (when in "By well markers" mode), it would need adjustment to handle a list.
-   The main analysis loop typically iterates over `selected_indices` or the processed traces. If multiple well-marker pairs result in a list of traces, existing loops should handle this.
-   **Display of Information:** If this page displays details about the selected area, it should be updated to show the list of selected well-marker pairs, similar to the change in `precompute_qc_page.py`.

## 4. Workflow Diagram (Mermaid)

```mermaid
graph TD
    A[app.py: User Navigates to Step 3] --> B{pages/select_area_page.py: render()};
    B -- Mode --> C{Selection Mode?};
    C -- "By well markers" --> D[UI: Select Well Markers (st.multiselect - st.session_state.selected_well_markers)];
    D --> E[UI: Select Well-Marker Pair(s) (st.multiselect - st.session_state.selected_well_marker_pairs)];
    E --> F[Button: "Next: Select Traces" / "Proceed to Pre-computation"];
    F --> G{Logic: Process Selections};
    G -- If "By well markers" --> H[Iterate st.session_state.selected_well_marker_pairs];
    H --> I[For each pair: Get well_name, surface_name];
    I --> J[Find data in well_df];
    J --> K[get_nearest_trace_index()];
    K --> L[Append to temp_selected_indices & temp_selected_data_for_precompute];
    L --> M[Set st.session_state.selected_indices (unique)];
    M --> N[Set st.session_state.selected_data_for_precompute (list of dicts)];
    N --> O[Set st.session_state.area_selected = True];
    O --> P[app.py: User Navigates to Step 3.5 Pre-computation];
    P --> Q[pages/precompute_qc_page.py: render()];
    Q --> R[Display: st.session_state.selected_well_marker_pairs (list)];
    R --> S[Uses st.session_state.selected_indices for loading traces for QC];
    S --> T[Button: "Next to Analyze Data"];
    T --> U[app.py: User Navigates to Step 4 Analysis];
    U --> V[pages/analyze_data_page.py: render()];
    V --> W[Potentially display selected pairs];
    W --> X[Uses st.session_state.selected_indices for analysis];

    C -- "Other Modes" --> Z[Other mode logic...];
```

## 5. Testing Considerations

-   Verify that multiple well-marker pairs can be selected.
-   Confirm that the `st.session_state.selected_well_marker_pairs` correctly stores a list of strings.
-   Ensure `st.session_state.selected_indices` correctly accumulates unique trace indices from all selected pairs.
-   Ensure `st.session_state.selected_data_for_precompute` is a list of dictionaries, with each dictionary corresponding to a selected pair and its trace.
-   Test that the "Pre-computation & QC" page correctly displays the list of selected pairs and processes all associated traces.
-   Test that the "Analyze Data" page correctly processes all traces derived from the multiple selected pairs.
-   Test edge cases:
    -   No well-marker pairs selected.
    -   Only one well-marker pair selected.
    -   Selected markers that result in no available pairs.
    -   Selected pairs that might point to the same trace index.

This plan provides a clear path to implementing the desired multi-select functionality for well-marker pairs.