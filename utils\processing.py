
import numpy as np
import tkinter as tk
from tkinter import ttk, simpledialog, messagebox
from tqdm import tqdm
import sys
import segyio
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import logging
import random # Added for sampling
import numpy as np # Ensure numpy is imported if not already (it is)
from utils.data_utils import load_trace_sample, get_trace_count
from utils.general_utils import get_suggested_batch_size
from utils.dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu as dlogst_spec_descriptor

# --- Inserted Functions Start ---

def calculate_woss(descriptor, plot_settings):
    """
    Calculates WOSS from descriptor components using parameters from plot_settings.
    Matches logic from original script's export section.

    Args:
        descriptor: Dictionary containing spectral descriptors (hfc, norm_fdom, mag_voice_slope)
        plot_settings: Dictionary containing calculation parameters (epsilon, fdom_exponent, hfc_p95)

    Returns:
        numpy.ndarray: Calculated WOSS values
    """
    epsilon = plot_settings.get('epsilon', 1e-4)  # Default epsilon for numerical stability
    fdom_exponent = plot_settings.get('fdom_exponent', 2.0)
    # Get the HFC percentile value (previously calculated based on user-configured percentile)
    hfc_p95 = plot_settings.get('hfc_p95', 1.0)  # HFC value at the specified percentile for normalization

    # Log the HFC p95 value being used for debugging
    logging.info(f"Using HFC p95 value for WOSS calculation: {hfc_p95}")

    # Check if required components exist
    if not all(k in descriptor for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
        print("Warning: Missing required components for WOSS calculation")
        return np.zeros_like(descriptor.get('hfc', np.array([])))

    # Get components
    hfc = descriptor['hfc']
    norm_fdom = descriptor['norm_fdom']
    mag_voice_slope = descriptor['mag_voice_slope']

    # Ensure all components have the same shape
    if not (hfc.shape == norm_fdom.shape == mag_voice_slope.shape):
        print("Warning: Component shapes don't match for WOSS calculation")
        return np.zeros_like(hfc)

    # Calculate WOSS
    # 1. Normalize HFC by its 95th percentile (or max if p95 is not provided)
    if hfc_p95 is None or hfc_p95 <= 0:
        hfc_max = np.max(np.abs(hfc))
        norm_hfc = hfc / (hfc_max + epsilon)
    else:
        norm_hfc = hfc / (hfc_p95 + epsilon)

    # 2. Apply exponent to normalized dominant frequency
    fdom_factor = np.power(norm_fdom + epsilon, fdom_exponent)

    # 3. Combine components
    woss = norm_hfc * fdom_factor * mag_voice_slope

    return woss

def process_trace_batch(segy_path, trace_indices, dt, plot_settings, batch_size=None, use_gpu=True):
    """
    Process a batch of traces from a SEG-Y file to calculate spectral descriptors.

    This function loads and processes multiple traces from a SEG-Y file in batches,
    calculating spectral descriptors for each trace. It supports both GPU and CPU
    processing modes.

    Args:
        segy_path: Path to the SEG-Y file
        trace_indices: List of trace indices to process
        dt: Time sampling interval in seconds
        plot_settings: Dictionary containing processing parameters
        batch_size: Number of traces to process in each batch (None for auto)
        use_gpu: Whether to use GPU acceleration

    Returns:
        list: List of dictionaries containing spectral descriptors for each trace
    """
    # Determine batch size if not specified
    if batch_size is None:
        if use_gpu:
            # Get suggested batch size based on GPU memory
            batch_size, _ = get_suggested_batch_size()
        else:
            # Default CPU batch size
            batch_size = 10

    # Initialize results list
    all_descriptors = []

    # Process traces in batches
    num_batches = (len(trace_indices) + batch_size - 1) // batch_size
    for batch_idx in range(num_batches):
        # Get indices for this batch
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, len(trace_indices))
        batch_indices = trace_indices[start_idx:end_idx]

        # Load traces for this batch
        batch_traces = []
        for trace_idx in batch_indices:
            trace_data = load_trace_sample(segy_path, trace_idx)
            batch_traces.append(trace_data)

        # Stack traces into a 2D array
        traces_array = np.vstack(batch_traces)

        # Process the batch
        print(f"Processing batch {batch_idx+1}/{num_batches} ({len(batch_indices)} traces)...")
        batch_descriptors = process_trace_array(traces_array, dt, plot_settings, use_gpu)

        # Add descriptors to results list
        all_descriptors.extend(batch_descriptors)

    return all_descriptors

def process_trace_array(traces_array, dt, plot_settings, use_gpu=True):
    """
    Process a 2D array of traces to calculate spectral descriptors using the corrected DLOGST function.

    Args:
        traces_array: 2D numpy array of traces (shape: [num_traces, num_samples])
        dt: Time sampling interval in seconds
        plot_settings: Dictionary containing processing parameters (shape, kmax, etc.)
        use_gpu: Whether to use GPU acceleration

    Returns:
        list: List of dictionaries containing primary spectral descriptors for each trace (WOSS is calculated later).
    """
    # Extract DLOGST parameters from plot_settings
    fmax = plot_settings.get('freq_max', None) # Use freq_max as fmax for DLOGST
    shape = plot_settings.get('shape', None)
    kmax = plot_settings.get('kmax', None)
    int_val = plot_settings.get('int_val', None)
    b1 = plot_settings.get('b1', None)
    b2 = plot_settings.get('b2', None)
    p_bandwidth = plot_settings.get('p_bandwidth', 2)
    roll_percent = plot_settings.get('roll_percent', 0.85)
    use_band_limited = plot_settings.get('use_band_limited', False)
    # Note: epsilon is handled within dlogst_spec_descriptor_gpu now
    # Note: window_length/overlap are not used by the DLOGST function

    logging.info(f"Processing trace array with DLOGST params: fmax={fmax}, shape={shape}, kmax={kmax}, int_val={int_val}, b1={b1}, b2={b2}, use_band_limited={use_band_limited}")

    # Process each trace
    descriptors_list = []
    for i in range(traces_array.shape[0]):
        trace_data = traces_array[i, :]

        # Calculate spectral descriptors using the corrected DLOGST function
        # Pass the extracted parameters directly
        descriptor = dlogst_spec_descriptor(
            trace_data,
            dt,
            fmax=fmax,
            shape=shape,
            kmax=kmax,
            int_val=int_val,
            b1=b1,
            b2=b2,
            p_bandwidth=p_bandwidth,
            roll_percent=roll_percent,
            use_band_limited=use_band_limited
            # use_gpu is implicitly handled by the imported function if available
        )

        # Add frequency vector if it exists in the result (it should)
        if 'freqst' in descriptor:
             descriptor['freq_vector'] = descriptor['freqst'] # Use 'freqst' as the common key

        # --- REMOVED WOSS CALCULATION FROM HERE ---
        # WOSS will be calculated later after primary statistics are computed.

        # Add to results list
        descriptors_list.append(descriptor)

    return descriptors_list

def _compute_descriptor_statistics(descriptors, time_vector, keys_to_compute=None):
    """
    Calculate statistics for specified spectral descriptors.

    Args:
        descriptors: List of dictionaries containing spectral descriptors.
        time_vector: Time vector corresponding to the traces.
        keys_to_compute: List of descriptor keys (strings) to compute statistics for.
                         If None, computes for a default set of primary keys.

    Returns:
        dict: Dictionary containing statistics (min, max, percentiles, etc.) for each requested descriptor.
    """
    # Initialize results dictionary
    stats_results = {} # Store stats directly under the key

    # Define default primary keys if none specified (excluding WOSS initially)
    if keys_to_compute is None:
        keys_to_compute = [
            'hfc',
            'norm_fdom',
            'mag_voice_slope', # Use the corrected slope from DLOGST
            'spec_centroid',
            'peak_freq',
            'spec_slope', # Original mag slope
            'voice_slope',
            'spec_decrease',
            'spec_bandwidth',
            'spec_rolloff',
            'mag', # Added for Magnitude Spectrogram
            'mag_voice' # Added for Magnitude*Voice Spectrogram
            # Add other relevant primary keys from dlogst_spec_descriptor output if needed
        ]

    # Define display names for each key
    display_names = {
        "hfc": "High Frequency Content",
        "norm_fdom": "Normalized Dominant Frequency",
        "mag_voice_slope": "Magnitude Voice Slope", # Corrected DLOGST version
        "WOSS": "Weighted Overlap Spectral Slope", # Will be computed separately
        "spec_centroid": "Spectral Centroid",
        "peak_freq": "Peak Frequency",
        "spec_slope": "Spectral Slope (Mag)", # Original FFT-based slope
        "voice_slope": "Voice Slope",
        "spec_decrease": "Spectral Decrease",
        "spec_bandwidth": "Spectral Bandwidth",
        "spec_rolloff": "Spectral Rolloff",
        "mag": "Magnitude Spectrogram", # Added display name
        "mag_voice": "Magnitude*Voice Spectrogram", # Added display name
        # Add others as needed
    }

    # Collect arrays for each requested descriptor type
    data_arrays = {}
    for key in keys_to_compute:
        arr_list = []
        for d in descriptors:
            # Check if key exists and is a non-empty numpy array
            if key in d and isinstance(d[key], np.ndarray) and d[key].size > 0:
                arr_list.append(d[key].flatten()) # Flatten each trace's descriptor array
            elif key == 'WOSS' and 'WOSS_calculated' in d and isinstance(d['WOSS_calculated'], np.ndarray) and d['WOSS_calculated'].size > 0:
                 # Special handling if computing stats for pre-calculated WOSS
                 arr_list.append(d['WOSS_calculated'].flatten())

        if arr_list:
            # Concatenate all flattened arrays for this key across all traces
            concatenated_array = np.concatenate(arr_list)
            data_arrays[key] = clean_array(concatenated_array, display_names.get(key, key))
        else:
            logging.warning(f"No valid data found for statistics calculation of '{key}'")


    # Calculate statistics for each descriptor
    for key, data in data_arrays.items():
        if data.size > 0:
            try:
                # Calculate percentiles
                p01 = np.percentile(data, 1)
                p05 = np.percentile(data, 5)
                p25 = np.percentile(data, 25)
                p50 = np.percentile(data, 50)  # median
                p75 = np.percentile(data, 75)
                p95 = np.percentile(data, 95)
                p99 = np.percentile(data, 99)

                # Calculate mean and standard deviation
                mean_val = np.mean(data)
                std_val = np.std(data)

                # Store statistics directly under the key
                stats_results[key] = {
                    'min': float(np.min(data)),
                    'max': float(np.max(data)),
                    'mean': float(mean_val),
                    'median': float(p50),
                    'std': float(std_val),
                    'p01': float(p01),
                    'p05': float(p05),
                    'p25': float(p25),
                    'p75': float(p75),
                    'p95': float(p95),
                    'p99': float(p99),
                    'display_name': display_names.get(key, key)
                }

                # Determine default display range (typically p05 to p95)
                stats_results[key]['default_min'] = float(p05)
                stats_results[key]['default_max'] = float(p95)
            except Exception as e:
                 logging.error(f"Error calculating statistics for key '{key}': {e}", exc_info=True)
                 stats_results[key] = {'error': str(e)} # Store error info

    # --- Information below is general, not per-descriptor ---
    general_stats = {}
    # Add frequency information if available from the first descriptor
    if descriptors and 'freq_vector' in descriptors[0]:
        general_stats['freq_range'] = [
            float(descriptors[0]['freq_vector'][0]),
            float(descriptors[0]['freq_vector'][-1])
        ]

    # Add trace length based on time_vector for convenience
    if time_vector.size > 0:
        general_stats['trace_length_seconds'] = float(time_vector[-1])
    else:
        general_stats['trace_length_seconds'] = 0.0

    # Add time range
    if time_vector.size > 0:
         general_stats['time_range'] = [float(time_vector[0]), float(time_vector[-1])]
    else:
         general_stats['time_range'] = [0.0, 0.0]


    # Add spectrogram stats if available (optional, might be large)
    # Consider calculating this only if explicitly needed later
    # ... (spectrogram stats calculation logic removed for brevity/performance)

    # Return descriptor stats and general stats separately
    return stats_results, general_stats


def calculate_stats_and_defaults(segy_path, header_loader, dt, sample_percent, max_traces_for_stats, **spectral_params):
    """
    Refactored function to:
    1. Sample traces.
    2. Calculate primary DLOGST descriptors for the sample.
    3. Compute statistics for primary descriptors.
    4. Calculate WOSS for the sample using derived hfc_p95.
    5. Compute statistics for WOSS.
    6. Combine results and return.

    Args:
        segy_path (str): Path to the SEG-Y file.
        header_loader (SegyHeaderLoader): Instance of the SEGY header loader.
        dt (float): Time sampling interval in seconds.
        sample_percent (float): Percentage of traces to sample for statistics.
        max_traces_for_stats (int): Maximum number of traces to use for statistics.
        **spectral_params (dict): Keyword arguments containing parameters for DLOGST calculation.

    Returns:
        dict or None: A dictionary containing 'stats' (combined per-descriptor statistics),
                      'defaults' (default plot limits for UI), and 'trace_length'.
                      Returns None if sampling or processing fails.
    """
    logging.info(f"Calculating stats and defaults with DLOGST params: {spectral_params}")

    # --- 1. Sampling ---
    num_total_traces = get_trace_count(segy_path)
    if not header_loader or num_total_traces == 0:
        logging.warning("Header loader not available or segy file has no traces.")
        return None

    num_to_sample = int(min(num_total_traces * (sample_percent / 100.0), max_traces_for_stats))
    if num_to_sample <= 0:
        logging.warning(f"Number of traces to sample is {num_to_sample}. Cannot calculate defaults.")
        return None

    logging.info(f"Sampling {num_to_sample} traces out of {num_total_traces} for statistics.")
    try:
        all_trace_indices = list(range(num_total_traces))
        sample_indices = random.sample(all_trace_indices, num_to_sample)
    except ValueError as e:
        logging.error(f"Error during random sampling: {e}. Falling back to first {num_to_sample} traces.")
        num_to_sample = min(num_to_sample, num_total_traces) # Ensure not exceeding total
        sample_indices = list(range(num_to_sample))

    sample_traces_data = []
    for idx in sample_indices:
        try:
            trace_data = load_trace_sample(segy_path, idx)
            if trace_data is not None and trace_data.size > 0:
                sample_traces_data.append(trace_data)
        except Exception as e:
            logging.warning(f"Could not load trace sample for index {idx}: {e}")
            continue

    if not sample_traces_data:
        logging.error("No sample traces could be loaded.")
        return None

    try:
        traces_array = np.vstack(sample_traces_data)
    except ValueError as e:
        logging.error(f"Error stacking sample traces (inconsistent lengths?): {e}. Using first trace only.")
        if sample_traces_data:
            traces_array = sample_traces_data[0].reshape(1, -1)
        else: return None # Should not happen if check above passed

    if traces_array.ndim == 1: traces_array = traces_array.reshape(1, -1)

    # --- 2. Calculate Primary Descriptors ---
    num_samples_in_trace = traces_array.shape[1]
    time_vector = np.arange(num_samples_in_trace) * dt
    use_gpu_processing = spectral_params.get('use_gpu', True) # Check if 'use_gpu' is in spectral_params

    logging.info(f"Calling process_trace_array (for primary descriptors) with dt={dt}, plot_settings={spectral_params}, use_gpu={use_gpu_processing}")
    # process_trace_array now uses the corrected dlogst_spec_descriptor
    primary_descriptors_list = process_trace_array(traces_array, dt, plot_settings=spectral_params, use_gpu=use_gpu_processing)

    if not primary_descriptors_list:
        logging.error("Failed to compute primary descriptors for sample traces.")
        return None

    # --- 3. Compute Statistics for Primary Descriptors ---
    logging.info("Computing statistics for primary descriptors...")
    primary_stats, general_stats = _compute_descriptor_statistics(primary_descriptors_list, time_vector) # Excludes WOSS by default

    if not primary_stats:
         logging.warning("Primary statistics calculation returned empty.")
         # Decide if we should continue or return None
         # For now, let's try to continue if possible, but WOSS calc might fail

    # --- 4. Calculate WOSS using derived hfc percentile ---
    logging.info("Calculating WOSS for sample traces using derived statistics...")

    # Get the user-configured HFC percentile (default to 95 if not specified)
    hfc_percentile = spectral_params.get('hfc_percentile', 95.0)
    logging.info(f"Using HFC percentile value of {hfc_percentile} for WOSS calculation")

    # Calculate the HFC value at the specified percentile
    hfc_percentile_value = primary_stats.get('hfc', {}).get(f'p{int(hfc_percentile)}', None)

    # If the exact percentile isn't available, calculate it directly from the data
    if hfc_percentile_value is None:
        # Collect all HFC values from the descriptors
        all_hfc = []
        for desc in primary_descriptors_list:
            if 'hfc' in desc and isinstance(desc['hfc'], np.ndarray) and desc['hfc'].size > 0:
                all_hfc.extend(desc['hfc'].flatten())

        if all_hfc:
            all_hfc = np.array(all_hfc)
            hfc_percentile_value = np.percentile(all_hfc, hfc_percentile)
            logging.info(f"Calculated HFC p{hfc_percentile} value: {hfc_percentile_value}")
        else:
            logging.warning(f"Could not calculate HFC p{hfc_percentile}. WOSS calculation might be inaccurate.")
            hfc_percentile_value = 0  # Let calculate_woss use its internal fallback

    # Add the derived hfc_p95 to the spectral_params for calculate_woss
    woss_calc_params = spectral_params.copy()
    woss_calc_params['hfc_p95'] = hfc_percentile_value

    all_woss_values = []
    for i, desc_dict in enumerate(primary_descriptors_list):
        try:
            # Calculate WOSS for this trace's descriptors
            woss_trace = calculate_woss(desc_dict, woss_calc_params)
            # Store the calculated WOSS back into the dictionary (or a separate list)
            # Storing it back might be easier for the subsequent stats calculation
            primary_descriptors_list[i]['WOSS_calculated'] = woss_trace
            all_woss_values.append(woss_trace.flatten()) # Collect for stats
        except Exception as e:
            logging.error(f"Error calculating WOSS for trace {i}: {e}")
            primary_descriptors_list[i]['WOSS_calculated'] = np.zeros_like(desc_dict.get('hfc', np.array([]))) # Add zeros on error
            all_woss_values.append(np.array([])) # Add empty array

    # --- 5. Compute Statistics for WOSS ---
    logging.info("Computing statistics for WOSS...")
    # We can reuse _compute_descriptor_statistics, telling it to only compute for 'WOSS'
    # It will look for the 'WOSS_calculated' key we added
    woss_stats, _ = _compute_descriptor_statistics(primary_descriptors_list, time_vector, keys_to_compute=['WOSS'])

    # --- 6. Combine Results ---
    logging.info("Combining statistics and preparing results...")
    combined_stats = primary_stats
    if 'WOSS' in woss_stats:
        combined_stats['WOSS'] = woss_stats['WOSS']
    else:
        logging.warning("WOSS statistics could not be computed.")
        # Add a placeholder?
        combined_stats['WOSS'] = {'error': 'Calculation failed', 'display_name': 'Weighted Overlap Spectral Slope'}


    # Prepare the 'defaults' dictionary for plot_settings updates based on combined stats
    final_plot_defaults = {}
    for desc_key, desc_values in combined_stats.items():
        if 'error' in desc_values: continue # Skip if stats failed for this key

        key_base = desc_key.lower().replace(' ', '_').replace('*', '').replace('/', '_')
        min_key_for_plot_settings = f"{key_base}_cmap_min"
        max_key_for_plot_settings = f"{key_base}_cmap_max"

        # Use p05 and p95 as default min/max for plot limits
        final_plot_defaults[min_key_for_plot_settings] = desc_values.get('p05', desc_values.get('min', 0))
        final_plot_defaults[max_key_for_plot_settings] = desc_values.get('p95', desc_values.get('max', 1))

    # Get trace length from general stats
    trace_length_seconds = general_stats.get('trace_length_seconds', 0.0)

    # Structure the final return dictionary
    result = {
        'stats': combined_stats,  # Combined stats for all descriptors
        'defaults': final_plot_defaults,
        'trace_length': trace_length_seconds,
        'time_range': general_stats.get('time_range'),
        'freq_range': general_stats.get('freq_range'),
        'hfc_p95': hfc_percentile_value,
        # 'spectrogram_stats': general_stats.get('spectrogram') # Excluded for now
    }
    logging.info(f"Successfully calculated combined stats and defaults. Result trace_length: {result['trace_length']}")
    return result

def clean_array(arr, name="array"):
    """
    Clean a numpy array by removing NaN, Inf values, and extreme outliers.

    Args:
        arr: Numpy array to clean
        name: Name of the array for logging purposes

    Returns:
        numpy.ndarray: Cleaned array
    """
    # Make a copy to avoid modifying the original
    cleaned = arr.copy()

    # Count initial elements
    initial_count = cleaned.size

    # Replace NaN and Inf with zeros
    nan_count = np.isnan(cleaned).sum()
    inf_count = np.isinf(cleaned).sum()

    if nan_count > 0 or inf_count > 0:
        print(f"Warning: Found {nan_count} NaN and {inf_count} Inf values in {name}")
        cleaned[~np.isfinite(cleaned)] = 0

    # Remove extreme outliers (values beyond 5 standard deviations)
    if cleaned.size > 10:  # Only if we have enough data points
        mean_val = np.mean(cleaned)
        std_val = np.std(cleaned)

        # Define outlier threshold (5 sigma)
        threshold = 5 * std_val

        # Count outliers
        outliers = np.abs(cleaned - mean_val) > threshold
        outlier_count = np.sum(outliers)

        if outlier_count > 0:
            print(f"Warning: Found {outlier_count} extreme outliers in {name}")

            # Replace outliers with the threshold value (clamping)
            cleaned[cleaned > mean_val + threshold] = mean_val + threshold
            cleaned[cleaned < mean_val - threshold] = mean_val - threshold

    return cleaned
