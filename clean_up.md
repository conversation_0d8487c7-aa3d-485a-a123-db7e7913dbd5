# Refactoring Plan to Resolve Pylance Warnings

The core issue identified is that functions previously named with a `_streamlit` suffix (e.g., `get_surfaces_streamlit`, `get_well_marker_pairs_streamlit`) have been renamed by removing this suffix. The corrected function names are `get_surfaces` and `get_well_marker_pairs`, both located in the [`utils/data_utils.py`](utils/data_utils.py) module.

This plan outlines the necessary modifications to update import statements and function calls in the affected files.

## Affected Files and Required Changes:

**1. File: [`app.py`](app.py)**
    *   **Pylance Warning**: `[Pylance Warning] 1476 | well_marker_dict = get_well_marker_pairs_streamlit(st.session_state.well_df) : "get_well_marker_pairs_streamlit" is not defined`
    *   **Required Changes**:
        1.  Ensure `get_well_marker_pairs` is imported from `utils.data_utils`. (This will involve checking existing imports and adding/modifying as needed).
        2.  Modify the function call on line 1476 from `get_well_marker_pairs_streamlit(st.session_state.well_df)` to `get_well_marker_pairs(st.session_state.well_df)`.

**2. File: [`pages/analyze_data_page.py`](pages/analyze_data_page.py)**
    *   **Pylance Warning**: `[Pylance Warning] 72 | well_marker_dict = get_well_marker_pairs_streamlit(st.session_state.well_df) : "get_well_marker_pairs_streamlit" is not defined`
    *   **Required Changes**:
        1.  Ensure `get_well_marker_pairs` is imported from `utils.data_utils`.
        2.  Modify the function call on line 72 from `get_well_marker_pairs_streamlit(st.session_state.well_df)` to `get_well_marker_pairs(st.session_state.well_df)`.

**3. File: [`pages/select_area_page.py`](pages/select_area_page.py)**
    *   **Pylance Warning**: `[Pylance Warning] 58 | available_markers = get_surfaces_streamlit(st.session_state.well_df) : "get_surfaces_streamlit" is not defined`
    *   **Required Changes**:
        1.  Ensure `get_surfaces` is imported from `utils.data_utils`.
        2.  Modify the function call on line 58 from `get_surfaces_streamlit(st.session_state.well_df)` to `get_surfaces(st.session_state.well_df)`.

**4. File: [`pages/configure_display_page.py`](pages/configure_display_page.py)**
    *   **Context**: This file has an existing import: `from utils.data_utils import get_surfaces_streamlit` (line 24) and calls this function: `available_surfaces = get_surfaces_streamlit(st.session_state.well_df)` (line 140).
    *   **Required Changes**:
        1.  Modify the import statement on line 24 from `from utils.data_utils import get_surfaces_streamlit` to `from utils.data_utils import get_surfaces`.
        2.  Modify the function call on line 140 from `get_surfaces_streamlit(st.session_state.well_df)` to `get_surfaces(st.session_state.well_df)`.

## Visual Plan (Mermaid Diagram):

```mermaid
graph TD
    subgraph Definitions in utils/data_utils.py
        DU_GET_SURFACES["get_surfaces(df)"]
        DU_GET_PAIRS["get_well_marker_pairs(df_wells)"]
    end

    subgraph Modifications
        direction LR
        subgraph app.py
            APP_IMPORT_CHECK["Check/Add: from utils.data_utils import get_well_marker_pairs"]
            APP_CALL_FIX["L1476: Change call to get_well_marker_pairs"]
            DU_GET_PAIRS -.-> APP_IMPORT_CHECK --> APP_CALL_FIX
        end

        subgraph pages/analyze_data_page.py
            ADP_IMPORT_CHECK["Check/Add: from utils.data_utils import get_well_marker_pairs"]
            ADP_CALL_FIX["L72: Change call to get_well_marker_pairs"]
            DU_GET_PAIRS -.-> ADP_IMPORT_CHECK --> ADP_CALL_FIX
        end

        subgraph pages/select_area_page.py
            SAP_IMPORT_CHECK["Check/Add: from utils.data_utils import get_surfaces"]
            SAP_CALL_FIX["L58: Change call to get_surfaces"]
            DU_GET_SURFACES -.-> SAP_IMPORT_CHECK --> SAP_CALL_FIX
        end

        subgraph pages/configure_display_page.py
            CDP_IMPORT_FIX["L24: Change import to get_surfaces"]
            CDP_CALL_FIX["L140: Change call to get_surfaces"]
            DU_GET_SURFACES -.-> CDP_IMPORT_FIX --> CDP_CALL_FIX
        end
    end

    style DU_GET_SURFACES fill:#lightgreen,stroke:#333
    style DU_GET_PAIRS fill:#lightgreen,stroke:#333
```

**Note:** The Pylance errors reported for the path `../../../../../../../response_ecd4f969-fa24-445f-961b-612d24213c4c/0` are being disregarded as this appears to be a temporary or non-project file. If these errors persist or are found to be relevant, they can be addressed separately.