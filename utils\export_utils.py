
import numpy as np

def select_export_attributes(exportable_attrs, first_descriptor, sample_length):
    """
    Filters feasible attributes for export, matching logic from original implementation.
    Returns a list of exportable attribute names.
    
    Args:
        exportable_attrs: List of desired attribute names to export
        first_descriptor: Dictionary containing spectral descriptors for the first trace
        sample_length: Expected length of each attribute array
        
    Returns:
        list: List of attribute names that can be exported
    """
    # Filter exportable attributes
    valid_attrs = [
        key for key in exportable_attrs
        if key in first_descriptor and isinstance(first_descriptor[key], np.ndarray) and len(first_descriptor[key]) == sample_length
    ]
    
    # Optionally add WOSS if possible
    if (
        'hfc' in first_descriptor and
        'norm_fdom' in first_descriptor and
        'mag_voice_slope' in first_descriptor and
        'WOSS' not in valid_attrs and
        'WOSS' in exportable_attrs
    ):
        valid_attrs.append('WOSS')
    
    return valid_attrs
