# Plan: GPU-Only Processing for "Option 1" (Well Markers)

This document outlines the plan to address unresponsiveness in the 'View Results' feature for 'Option 1: By well markers' by enforcing GPU-only processing and enhancing robustness.

## Core Problem:
The 'View Results' feature can hang or perform poorly for "Option 1" selections when precomputed data is unavailable. The previous design included a CPU fallback for descriptor calculation, which could be very slow, especially with certain trace data characteristics. The user has decided to remove this CPU fallback and rely exclusively on GPU processing for "Option 1".

## Confirmed Plan Details:

**I. `pages/analyze_data_page.py` Modifications:**
   (Within the "Calculate Descriptors" section for "By well markers" mode)

1.  **Remove CPU Fallback Logic:**
    *   Eliminate the `try-except` block that attempts CPU calculation after a GPU failure.
    *   The import of `dlogst_spec_descriptor_cpu` may become unnecessary if not used elsewhere for "Option 1".
2.  **Global GPU Availability Check:**
    *   At the start of the "Calculate Descriptors" process, if `st.session_state.GPU_AVAILABLE` is `False`, display an error: `"GPU processing is required for this analysis mode, but no GPU is available. Please check your system configuration or select a different analysis option if available."` Halt further processing for "Option 1" in this case.
3.  **Improved Progress Indication:**
    *   Utilize `st.progress()` for overall progress through traces.
    *   Update `st.text()` within the loop: `f"Processing trace {i+1}/{len(loaded_trace_data)} for {well_marker_name} using GPU..."`
4.  **Robust Per-Trace GPU Error Handling & Skipping:**
    *   For each trace, wrap the call to `dlogst_spec_descriptor_gpu` in a `try-except` block.
    *   If `dlogst_spec_descriptor_gpu` fails (or returns invalid/incomplete critical descriptors):
        *   Log the detailed error.
        *   Append a placeholder (e.g., `{'error': str(e), 'well_marker_name': well_marker_name, 'trace_idx': trace_data.get('trace_idx')}`) to the `calculated_descriptors` list.
        *   Display a non-blocking warning to the user: `st.warning(f"Could not process trace {well_marker_name} using GPU. Skipping. See logs for details.")`
        *   Increment a counter for skipped traces.
        *   Ensure the loop continues to the next trace.
5.  **Summary of Processing Issues:**
    *   After the loop, if any traces were skipped, display a summary message to the user (e.g., `st.warning(f"Finished processing. {num_skipped_traces} trace(s) could not be processed due to GPU errors. Please check logs for details. Results for other traces are available.")`).

**II. `utils/dlogst_spec_descriptor_gpu.py` Enhancements:**
    (Specifically for the 1D function `dlogst_spec_descriptor_gpu`)

1.  **Input Validation:**
    *   At the beginning of the function:
        *   Check if the input `data` (NumPy array) is empty. If so, log the issue and raise a `ValueError` to be caught by the calling function in `pages/analyze_data_page.py`.
        *   After converting `data` to a CuPy array (`data_gpu`):
            *   Check if `data_gpu` contains all `NaN` or `Inf` values. If so, log a warning and return a dictionary of default/`NaN` CuPy arrays (these will be converted to NumPy arrays with `NaN`s upon return). This allows the calling function to identify it as a "bad" trace.
            *   Consider strategy for traces with *some* `NaN`/`Inf` values: either return default/`NaN` arrays (safer if `NaN`/`Inf` can corrupt the algorithm) or replace `NaN`/`Inf` in `data_gpu` with `0.0` and log a warning (might allow some descriptors to be calculated but could affect accuracy). This decision can be refined during implementation, prioritizing stability.

**III. Mermaid Diagram of Revised Flow in `pages/analyze_data_page.py`:**

```mermaid
graph TD
    A[Start "Calculate Descriptors"] --> A0{GPU_AVAILABLE?};
    A0 -- No --> A1[Show Error: GPU Required but Unavailable];
    A1 --> X[End Processing];
    A0 -- Yes --> B(Initialize st.progress(0));
    B --> C{Loop through loaded_trace_data};
    C -- Each trace (idx, trace_data) --> D[Update st.progress & st.text status];
    D --> E{Try};
    E -- Try --> G[Call dlogst_spec_descriptor_gpu];
    G -- Success & Valid Descriptor --> M[Store descriptor];
    G -- GPU Error OR Invalid Descriptor --> J[Log GPU Error/Invalid Descriptor];
    J --> K[Store Error Placeholder for this trace];
    K --> N[Increment skipped_traces_count];
    N --> O{End Loop?};
    M --> O;
    E -- Except (e) --> P[Log Unrecoverable Error for trace];
    P --> K;
    O -- No --> C;
    O -- Yes --> Q{Display Skipped Traces Summary if any};
    Q --> R[Calculate & Display Statistics];
    R --> S[Show "View Results" button];
```

This plan aims to make "Option 1" processing reliant on the GPU, improving performance consistency by removing the slow CPU fallback, while adding necessary error handling and user feedback for cases where GPU processing might fail for individual traces or if the GPU is unavailable globally.