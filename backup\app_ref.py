import streamlit as st
# --- Constants ---
APP_TITLE = "WOSS Seismic Analysis Tool" # Define APP_TITLE here
st.set_page_config(page_title=APP_TITLE, layout="wide") # Now use it

import torch # Initialize cupy from torch
import numpy as np
import pandas as pd
import segyio
import os
import sys
from io import BytesIO
import tempfile
import zipfile
import shutil # Added for cleanup
import plotly.express as px # Added for colormap list
import plotly.colors as pcolors # Added for colormap list
# from tqdm import tqdm # Replaced by st.progress
from plotly.subplots import make_subplots
import logging
from scipy.signal import find_peaks # Keep for plotting dominant frequencies

# --- Modular Imports (assuming they are modified as planned) ---
from data_utils import (
    SegyHeaderLoader, merge_segy_batch_files, load_excel_data,
    get_nearest_trace_index, load_trace_sample, get_sampling_interval, get_trace_count,
    # Import Streamlit-compatible versions of functions
    get_surfaces_streamlit, get_well_marker_pairs_streamlit
)
from visualization import (
    plot_basemap_with_wells, plot_interactive_basemap, # Keep interactive for polyline reference if needed
    plot_spectral_descriptors, plot_multi_trace_section # Add new visualization functions
)
from processing import (
    calculate_stats_and_defaults, # New function replacing parts of get_..._settings
    add_output_to_subplot,  # Still needed for comparative plots
    calculate_woss  # Import WOSS calculation function from processing.py
)
from utils import (
    parse_polyline_string, distance_point_to_segment, find_traces_near_polyline,
    get_suggested_batch_size, custom_excepthook # Ensure custom_excepthook doesn't use Tkinter
)
# from export_utils import ( # Attribute selection will be handled directly in Streamlit UI
#     AttributeSelector, select_export_attributes
# )

# --- Configure Logging ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Import GPU functions directly ---
# Wrap in try-except to handle environments without GPU/cupy
try:
    from dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu, dlogst_spec_descriptor_gpu_2d_chunked, dlogst_spec_descriptor_gpu_2d_chunked_mag
    GPU_AVAILABLE = True
    st.sidebar.success("GPU functions loaded successfully.")
    logging.info("Successfully imported GPU spectral descriptor functions")
except ImportError as e:
    GPU_AVAILABLE = False
    st.sidebar.warning(f"GPU functions not available: {e}. Processing will be slower.")
    logging.warning(f"Could not import GPU functions: {e}")
    # Define dummy functions or raise errors if GPU is mandatory for certain modes
    def dlogst_spec_descriptor_gpu(*args, **kwargs):
        st.error("GPU function dlogst_spec_descriptor_gpu not available.")
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu not available.")
    def dlogst_spec_descriptor_gpu_2d_chunked(*args, **kwargs):
        st.error("GPU function dlogst_spec_descriptor_gpu_2d_chunked not available.")
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu_2d_chunked not available.")
    def dlogst_spec_descriptor_gpu_2d_chunked_mag(*args, **kwargs):
        st.error("GPU function dlogst_spec_descriptor_gpu_2d_chunked_mag not available.")
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu_2d_chunked_mag not available.")

# --- Set custom exception handler ---
# sys.excepthook = custom_excepthook # Use the custom hook from utils # Temporarily disable

# Outputs for single trace analysis (Mode 1, Plot 1)
AVAILABLE_OUTPUTS_SINGLE = [
    "Input Signal", "Magnitude Spectrogram", "Magnitude * Voice",
    "Normalized dominant frequencies", "Spectral Slope", "Spectral Bandwidth",
    "Spectral Rolloff", "Mag*Voice Slope", "Spectral Decrease", "HFC", "WOSS"
]
# Outputs for multi-trace/section analysis (Modes 1(Plot 2), 2, 3, 4(Plot), 5)
AVAILABLE_OUTPUTS_MULTI = [
    "Input Signal",
    "Magnitude Spectrogram", # Added for comparative plots
    "Magnitude * Voice",     # Added for comparative plots
    "Normalized dominant frequencies",
    "Spectral Slope", "Spectral Bandwidth",
    "Spectral Rolloff", "Mag*Voice Slope", "Spectral Decrease", "HFC", "WOSS"
]
# Outputs for section/polyline modes (Modes 2, 3, 4(Plot), 5) - excluding spectral images
AVAILABLE_OUTPUTS_SECTION = [
    "Input Signal",
    # "Magnitude Spectrogram", # Excluded for section modes
    # "Magnitude * Voice",     # Excluded for section modes
    "Normalized dominant frequencies",
    "Spectral Slope", "Spectral Bandwidth",
    "Spectral Rolloff", "Mag*Voice Slope", "Spectral Decrease", "HFC", "WOSS"
]
# Internal names used in descriptor dicts and for export keys
EXPORTABLE_ATTR_INTERNAL_NAMES = [
    "data", # Original seismic data
    "mag_voice_slope",
    "spec_decrease",
    "hfc",
    "spec_bandwidth",
    "spec_rolloff",
    "WOSS", # Calculated attribute
    "norm_fdom" # Note: This is frequency domain, export might need care
]
# Display names corresponding to EXPORTABLE_ATTR_INTERNAL_NAMES
EXPORTABLE_ATTR_DISPLAY_NAMES = [
    "Original Seismic Amplitude",
    "Magnitude*Voice Slope",
    "Spectral Decrease",
    "High Frequency Content (HFC)",
    "Spectral Bandwidth",
    "Spectral Rolloff",
    "WOSS (Weighted-Optimum Spectral Shape)",
    "Normalized Dominant Frequency" # Display name for export
]
# Mapping for easy lookup
ATTR_NAME_MAP = dict(zip(EXPORTABLE_ATTR_DISPLAY_NAMES, EXPORTABLE_ATTR_INTERNAL_NAMES))
REVERSE_ATTR_NAME_MAP = dict(zip(EXPORTABLE_ATTR_INTERNAL_NAMES, EXPORTABLE_ATTR_DISPLAY_NAMES))

# --- Descriptor Colormap/Amplitude Limits ---
DESCRIPTOR_LIMITS = {
    "Input Signal": {"min": None, "max": None},
    "HFC": {"min": None, "max": None},
    "Spectral Decrease": {"min": None, "max": None},
    "Spectral Slope": {"min": None, "max": None},
    "Mag*Voice Slope": {"min": None, "max": None},
    "Spectral Bandwidth": {"min": None, "max": None},
    "Spectral Rolloff": {"min": None, "max": None},
    "Magnitude Spectrogram": {"min": None, "max": None},
    "Magnitude * Voice": {"min": None, "max": None},
    "Normalized Dominant Frequency": {"min": None, "max": None},
    "WOSS": {"min": None, "max": None},
}

# --- Set custom exception handler ---
# sys.excepthook = custom_excepthook # Use the custom hook from utils # Temporarily disable


# --- Caching Functions ---
# Add caching for header loading
@st.cache_data(show_spinner="Loading SEG-Y headers...")
def load_segy_headers_cached(segy_file_path, inline_byte, xline_byte, x_byte, y_byte, scaler_mode, scaler_byte=None, custom_scaler=None):
    """Loads SEG-Y headers using SegyHeaderLoader from a given file path, designed for caching."""
    try:
        # SegyHeaderLoader now receives the path directly
        header_loader = SegyHeaderLoader(segy_file_path)
        # We've fixed the tkinter issues in data_utils.py, so no need to check for tkinter usage

        # Use either the scaler byte or custom scaler value based on the mode
        if scaler_mode == "Use Scaler Byte":
            header_loader.load_headers(
                inline_byte=inline_byte,
                crossline_byte=xline_byte,
                x_coord_byte=x_byte,
                y_coord_byte=y_byte,
                scaler_byte=scaler_byte,
                use_custom_scaler=False
            )
        else:  # Custom Scaler mode
            header_loader.load_headers(
                inline_byte=inline_byte,
                crossline_byte=xline_byte,
                x_coord_byte=x_byte,
                y_coord_byte=y_byte,
                scaler_byte=custom_scaler,  # Pass the custom scaler value
                use_custom_scaler=True
            )

        # Store the path within the loader object
        header_loader.source_file_path = segy_file_path # Renamed from temp_file_path for clarity
        return header_loader
    except Exception as e:
        logging.error(f"Error loading SEG-Y headers from {segy_file_path}: {e}", exc_info=True)
        st.error(f"Error loading SEG-Y headers: {e}")
        return None
    # Note: The temporary file is now created *outside* this cached function.

# Add caching for Excel loading
@st.cache_data(show_spinner="Loading well data...")
def load_excel_data_cached(uploaded_file_info):
    """Loads well data from Excel, designed for caching."""
    try:
        # load_excel_data expects a file path or buffer
        df = load_excel_data(uploaded_file_info['buffer'])
        return df
    except Exception as e:
        logging.error(f"Error loading Excel data: {e}", exc_info=True)
        st.error(f"Error loading Excel data: {e}")
        return None


# --- Initialize Session State ---
if 'segy_file_info' not in st.session_state:
    st.session_state.segy_file_info = None
if 'well_file_info' not in st.session_state:
    st.session_state.well_file_info = None
if 'polyline_file_info' not in st.session_state:
    st.session_state.polyline_file_info = None
if 'header_loader' not in st.session_state:
    st.session_state.header_loader = None
if 'well_df' not in st.session_state:
    st.session_state.well_df = None
if 'dt' not in st.session_state:
    st.session_state.dt = None
if 'trace_count' not in st.session_state:
    st.session_state.trace_count = None
if 'stats_defaults' not in st.session_state:
    st.session_state.stats_defaults = None
if 'current_step' not in st.session_state:
    st.session_state.current_step = "load_data"
if 'plot_settings' not in st.session_state:
    st.session_state.plot_settings = {}
if 'selection_mode' not in st.session_state:
    st.session_state.selection_mode = None
if 'selected_well_markers' not in st.session_state:
    st.session_state.selected_well_markers = []
if 'plot_twt' not in st.session_state:
    st.session_state.plot_twt = False
if 'plot_mode_wells' not in st.session_state:
    st.session_state.plot_mode_wells = 1
if 'selected_inline' not in st.session_state:
    st.session_state.selected_inline = None
if 'selected_crossline' not in st.session_state:
    st.session_state.selected_crossline = None
if 'aoi_inline_min' not in st.session_state:
    st.session_state.aoi_inline_min = None
if 'aoi_inline_max' not in st.session_state:
    st.session_state.aoi_inline_max = None
if 'aoi_xline_min' not in st.session_state:
    st.session_state.aoi_xline_min = None
if 'aoi_xline_max' not in st.session_state:
    st.session_state.aoi_xline_max = None
if 'aoi_processing_option' not in st.session_state:
    st.session_state.aoi_processing_option = "Full AOI"  # Default to Full AOI
if 'aoi_plot_section_type' not in st.session_state:
    st.session_state.aoi_plot_section_type = None
if 'aoi_plot_fixed_value' not in st.session_state:
    st.session_state.aoi_plot_fixed_value = None
if 'polyline_vertices' not in st.session_state:
    st.session_state.polyline_vertices = None
if 'polyline_tolerance' not in st.session_state:
    st.session_state.polyline_tolerance = 0.0
if 'selected_indices' not in st.session_state:
    st.session_state.selected_indices = []
if 'loaded_trace_data' not in st.session_state:
    st.session_state.loaded_trace_data = []
if 'calculated_descriptors' not in st.session_state:
    st.session_state.calculated_descriptors = []
if 'analysis_complete' not in st.session_state:
    st.session_state.analysis_complete = False
if 'descriptor_statistics' not in st.session_state:
    st.session_state.descriptor_statistics = {}
if 'selected_outputs' not in st.session_state:
    st.session_state.selected_outputs = []
if 'batch_size' not in st.session_state:
    st.session_state.batch_size = None
if 'export_attributes' not in st.session_state:
    st.session_state.export_attributes = []
if 'export_grouping' not in st.session_state:
    st.session_state.export_grouping = None
if 'export_batch_step' not in st.session_state:
    st.session_state.export_batch_step = 1
if 'export_in_progress' not in st.session_state:
    st.session_state.export_in_progress = False
if 'exported_files_info' not in st.session_state:
    st.session_state.exported_files_info = None
if 'export_output_dir' not in st.session_state:
    st.session_state.export_output_dir = None
if 'segy_temp_file_path' not in st.session_state: # Add state for temp file path
    st.session_state.segy_temp_file_path = None
if 'use_wells' not in st.session_state: # Add state for using well data
    st.session_state.use_wells = True
if 'scaler_mode' not in st.session_state: # Add state for coordinate scaler mode
    st.session_state.scaler_mode = "Use Scaler Byte"
if 'custom_scaler' not in st.session_state: # Add state for custom coordinate scaler value
    st.session_state.custom_scaler = 1.0
if 'show_basemap' not in st.session_state: # Add state for basemap display control
    st.session_state.show_basemap = False
if 'selected_surfaces_for_basemap' not in st.session_state: # Add state to store selected surfaces
    st.session_state.selected_surfaces_for_basemap = []
if 'basemap_figure' not in st.session_state: # Add state to store the basemap figure
    st.session_state.basemap_figure = None
if 'basemap_needs_update' not in st.session_state: # Add state to track if basemap needs updating
    st.session_state.basemap_needs_update = False
if 'basemap_detail_level' not in st.session_state: # Add state to store basemap detail level
    st.session_state.basemap_detail_level = 'low'
# Add keys for cached data if needed, although direct use is often fine
# if 'cached_header_loader' not in st.session_state:
#     st.session_state.cached_header_loader = None
# if 'cached_well_df' not in st.session_state:
#     st.session_state.cached_well_df = None

# --- Helper Functions ---
def slider_with_number_input(label, min_value, max_value, value, step, key_base, format=None):
    """
    Creates a slider with a number input directly above it for precise control.
    Returns the selected value.
    """
    # Create a unique key for the number input
    number_input_key = f"{key_base}_number_input"
    slider_key = f"{key_base}_slider"

    # Create columns for the number input and label
    col1, col2 = st.columns([3, 1])

    # Display the label in the first column
    col1.write(label)

    # Create the number input in the second column
    if format:
        number_value = col2.number_input("", min_value=min_value, max_value=max_value, value=value, step=step, key=number_input_key, format=format, label_visibility="collapsed")
    else:
        number_value = col2.number_input("", min_value=min_value, max_value=max_value, value=value, step=step, key=number_input_key, label_visibility="collapsed")

    # Create the slider with the same value
    slider_value = st.slider("", min_value=min_value, max_value=max_value, value=number_value, step=step, key=slider_key, label_visibility="collapsed")

    # Synchronize values if they differ (e.g., if one was changed by the user)
    if slider_value != number_value:
        # The slider was moved, update the number input on next rerun
        return slider_value

    return number_value

def get_suggested_batch_size():
    """
    Estimate a reasonable batch size for GPU processing based on available GPU memory.

    Returns:
        tuple: (suggested_batch_size, free_memory_mb)
    """
    try:
        # Try to get GPU memory info from torch
        if torch.cuda.is_available():
            # Get total and allocated memory
            total_memory = torch.cuda.get_device_properties(0).total_memory
            allocated_memory = torch.cuda.memory_allocated(0)
            free_memory = total_memory - allocated_memory

            # Convert to MB for easier reading
            free_memory_mb = free_memory / (1024 * 1024)

            # Estimate batch size based on free memory
            # Assume each trace needs about 10MB of GPU memory (very rough estimate)
            # and leave 20% of free memory as buffer
            suggested_batch = int((free_memory_mb * 0.8) / 10)

            # Clamp to reasonable range
            suggested_batch = max(10, min(suggested_batch, 1000))

            return suggested_batch, free_memory_mb
        else:
            # No GPU available
            return 100, 0
    except Exception as e:
        # If anything goes wrong, return a conservative default
        logging.warning(f"Error estimating GPU memory: {e}. Using default batch size.")
        return 100, 0

def get_suggested_batch_size_for_export(num_unique_groups):
    """
    Suggest a reasonable batch size for export based on the number of unique groups.

    Args:
        num_unique_groups: Number of unique groups (inlines, crosslines, etc.)

    Returns:
        int: Suggested batch size for export
    """
    # Simple heuristic: for small numbers, use the number itself
    if num_unique_groups <= 10:
        return num_unique_groups

    # For larger numbers, use a fraction to keep file sizes manageable
    elif num_unique_groups <= 100:
        return max(10, num_unique_groups // 5)
    elif num_unique_groups <= 1000:
        return max(20, num_unique_groups // 10)
    else:
        return max(50, num_unique_groups // 20)

def reset_state():
    """Resets the session state for a new analysis."""
    # 1. Clean up temporary SEG-Y file if it exists
    if 'segy_temp_file_path' in st.session_state and st.session_state.segy_temp_file_path:
        try:
            if os.path.exists(st.session_state.segy_temp_file_path):
                os.remove(st.session_state.segy_temp_file_path)
                logging.info(f"Removed temporary SEG-Y file: {st.session_state.segy_temp_file_path}")
        except OSError as e:
            logging.warning(f"Could not remove temporary SEG-Y file {st.session_state.segy_temp_file_path}: {e}")
        except Exception as e:
             logging.error(f"Error during temporary SEG-Y file cleanup: {e}", exc_info=True)

    # 2. Clean up export directory if it exists
    if 'export_output_dir' in st.session_state and st.session_state.export_output_dir:
        try:
            if os.path.exists(st.session_state.export_output_dir):
                shutil.rmtree(st.session_state.export_output_dir)
                logging.info(f"Removed export directory: {st.session_state.export_output_dir}")
        except Exception as e:
            logging.warning(f"Could not remove export directory {st.session_state.export_output_dir}: {e}")

    # 3. Clean up any temporary files in the system temp directory with our prefix
    try:
        temp_dir = tempfile.gettempdir()
        for item in os.listdir(temp_dir):
            if item.startswith("woss_") and os.path.isdir(os.path.join(temp_dir, item)):
                try:
                    shutil.rmtree(os.path.join(temp_dir, item))
                    logging.info(f"Removed temporary directory: {os.path.join(temp_dir, item)}")
                except Exception as e:
                    logging.warning(f"Could not remove temporary directory {item}: {e}")
    except Exception as e:
        logging.warning(f"Error while cleaning up temporary directories: {e}")

    # 4. Clean up well marker data and processing outputs
    if 'selected_well_markers' in st.session_state and st.session_state.selected_well_markers:
        st.session_state.selected_well_markers = []
        logging.info("Reset selected well markers")

    if 'loaded_trace_data' in st.session_state and st.session_state.loaded_trace_data:
        st.session_state.loaded_trace_data = []
        logging.info("Cleared loaded trace data")

    if 'calculated_descriptors' in st.session_state and st.session_state.calculated_descriptors:
        st.session_state.calculated_descriptors = []
        logging.info("Cleared calculated descriptors")

    # 5. Reset mode selection flags
    for flag in ['traces_loaded_inline', 'traces_loaded_crossline', 'traces_loaded_polyline']:
        if flag in st.session_state:
            st.session_state[flag] = False
            logging.info(f"Reset {flag} flag")

    # 6. Keep track of keys to reset
    keys_to_reset = [
        'segy_file_info', 'well_file_info', 'polyline_file_info',
        'header_loader', 'well_df', 'dt', 'trace_count', 'stats_defaults',
        'current_step', 'plot_settings', 'selection_mode',
        'selected_well_markers', 'plot_twt', 'plot_mode_wells',
        'selected_inline', 'selected_crossline',
        'aoi_inline_min', 'aoi_inline_max', 'aoi_xline_min', 'aoi_xline_max',
        'aoi_processing_option', 'aoi_plot_section_type', 'aoi_plot_fixed_value',
        'polyline_vertices', 'polyline_tolerance', 'selected_indices',
        'loaded_trace_data', 'calculated_descriptors', 'analysis_complete', 'descriptor_statistics',
        'selected_outputs', 'batch_size', 'export_attributes', 'export_grouping',
        'export_batch_step', 'export_in_progress', 'exported_files_info',
        'export_output_dir', 'segy_temp_file_path', 'use_wells',
        'scaler_mode', 'custom_scaler', 'show_basemap', 'selected_surfaces_for_basemap',
        'basemap_figure', 'basemap_needs_update', 'basemap_detail_level',
        'traces_loaded_inline', 'traces_loaded_crossline', 'traces_loaded_polyline'
    ]
    for key in keys_to_reset:
        if key in st.session_state:
            del st.session_state[key]

    # 7. Clear relevant cache too
    load_segy_headers_cached.clear()
    load_excel_data_cached.clear()
    # Only clear calculate_stats_and_defaults if it has a clear method (is cached)
    if hasattr(calculate_stats_and_defaults, 'clear'):
        calculate_stats_and_defaults.clear()

    # 8. Set the current step back to load_data
    st.session_state.current_step = "load_data"
    logging.info("Session state reset for new analysis.")

# calculate_woss function is now imported from processing.py

# --- Main App ---
st.title(APP_TITLE)

# Add "Start New Analysis" button to sidebar (visible on all pages)
st.sidebar.markdown("---")  # Add a separator
if st.sidebar.button("🔄 Start New Analysis", use_container_width=True):
    # The enhanced reset_state() function now handles all cleanup
    # including temporary files, export directories, well markers, and processing outputs
    reset_state()
    st.success("Starting new analysis. All temporary data has been cleared.")
    st.rerun()

# == STEP 1: Load Data ==
if st.session_state.current_step == "load_data":
    st.header("Step 1: Load Data")
    st.sidebar.header("Data Loading")

    # SEG-Y File Upload
    st.sidebar.subheader("Upload SEG-Y File")
    segy_file = st.sidebar.file_uploader("Choose a SEG-Y file", type=["sgy", "segy"], key="segy_file_uploader")
    if segy_file is not None:
        st.session_state.segy_file_info = {
            'name': segy_file.name,
            'buffer': segy_file
        }
        st.sidebar.success(f"Uploaded {segy_file.name}")

    # Well Data Upload (Optional)
    st.sidebar.subheader("Upload Well Data (Optional)")
    # Add checkbox to toggle using well data
    st.session_state.use_wells = st.sidebar.checkbox("Use Well Data", value=st.session_state.use_wells)

    well_file = st.sidebar.file_uploader("Choose an Excel file", type=["xls", "xlsx"], key="well_file_uploader", disabled=not st.session_state.use_wells)
    if well_file is not None:
        st.session_state.well_file_info = {
            'name': well_file.name,
            'buffer': well_file
        }
        st.sidebar.success(f"Uploaded {well_file.name}")



    # SEG-Y Header Bytes
    st.sidebar.subheader("SEG-Y Header Bytes")
    st.session_state.inline_byte = st.sidebar.number_input("Inline Byte", value=189, step=1)
    st.session_state.xline_byte = st.sidebar.number_input("Crossline Byte", value=193, step=1)
    st.session_state.x_byte = st.sidebar.number_input("X Coordinate Byte", value=73, step=1)
    st.session_state.y_byte = st.sidebar.number_input("Y Coordinate Byte", value=77, step=1)

    # Coordinate Scaler Options
    st.sidebar.subheader("Coordinate Scaler")
    st.session_state.scaler_mode = st.sidebar.radio(
        "Scaler Mode",
        options=["Use Scaler Byte", "Custom Scaler"],
        index=0 if st.session_state.scaler_mode == "Use Scaler Byte" else 1
    )

    if st.session_state.scaler_mode == "Use Scaler Byte":
        st.session_state.scaler_byte = st.sidebar.number_input("Scaler Byte", value=71, step=1)
    else:
        st.session_state.custom_scaler = st.sidebar.number_input(
            "Custom Scaler Value",
            value=st.session_state.custom_scaler,
            min_value=-1000.0,
            max_value=1000.0,
            step=0.1,
            help="Positive value: multiply coordinates by this value. Negative value: divide coordinates by the absolute value."
        )

    # Load Data Button
    if st.sidebar.button("Load Data", key="load_data_button"):
        if st.session_state.segy_file_info:
            tmp_file_path = None # Initialize path variable
            try:
                # Create temporary file *before* calling cached function
                with tempfile.NamedTemporaryFile(delete=False, suffix=".sgy") as tmp_file:
                    tmp_file.write(st.session_state.segy_file_info['buffer'].getvalue())
                    tmp_file_path = tmp_file.name
                st.session_state.segy_temp_file_path = tmp_file_path # Store path in session state
                logging.info(f"Created temporary SEG-Y file: {tmp_file_path}")

                # Use cached function for header loading, passing the path
                header_loader = load_segy_headers_cached(
                    tmp_file_path, # Pass the path directly
                    st.session_state.inline_byte,
                    st.session_state.xline_byte,
                    st.session_state.x_byte,
                    st.session_state.y_byte,
                    st.session_state.scaler_mode,
                    scaler_byte=st.session_state.scaler_byte if st.session_state.scaler_mode == "Use Scaler Byte" else None,
                    custom_scaler=st.session_state.custom_scaler if st.session_state.scaler_mode == "Custom Scaler" else None
                )

                if header_loader:
                    st.session_state.header_loader = header_loader
                    # Use the stored temporary path for interval calculation
                    st.session_state.dt = get_sampling_interval(st.session_state.segy_temp_file_path)

                    # Get the actual number of traces using our new function
                    trace_count = get_trace_count(st.session_state.segy_temp_file_path)
                    st.session_state.trace_count = trace_count

                    st.success(f"SEG-Y headers loaded. Sampling Interval: {st.session_state.dt*1000:.2f} ms, Traces: {trace_count}")
                    logging.info(f"SEG-Y headers loaded from {st.session_state.segy_file_info['name']} (temp: {tmp_file_path}). dt={st.session_state.dt}, trace_count={trace_count}")

                    # Load well data if provided
                    if st.session_state.use_wells and st.session_state.well_file_info:
                        # Use cached function for well data
                        well_df = load_excel_data_cached(st.session_state.well_file_info)
                        if well_df is not None:
                            st.session_state.well_df = well_df
                            st.success(f"Well data loaded from {st.session_state.well_file_info['name']}.")
                            logging.info(f"Well data loaded from {st.session_state.well_file_info['name']}.")
                        else:
                            st.warning("Well file was provided but failed to load. Proceeding without well data.")
                            st.session_state.well_df = None # Ensure it's None if loading failed
                    else:
                        st.session_state.well_df = None # Ensure it's None if not used/provided

                    st.session_state.current_step = "configure_display"
                    st.rerun() # Move to the next step
                else:
                    # Error handled within cached function
                    pass

            except Exception as e:
                st.error(f"An unexpected error occurred during data loading: {e}")
                logging.error(f"Data loading failed: {e}", exc_info=True)
                # Clean up temp file if created but loading failed afterwards
                if tmp_file_path and os.path.exists(tmp_file_path):
                    try:
                        os.remove(tmp_file_path)
                        logging.info(f"Cleaned up temp file {tmp_file_path} after loading error.")
                        st.session_state.segy_temp_file_path = None
                    except OSError as clean_e:
                         logging.warning(f"Could not clean up temp file {tmp_file_path} after error: {clean_e}")
            # except AttributeError: # Handle case where st.session_state.use_wells is not set # This except block seems duplicated, remove one
            #      st.error("Please ensure 'use_wells' is initialized in session state.")
            #      logging.error("'use_wells' not in session state during well data load attempt.")
        else:
            st.warning("Please upload a SEG-Y file.")


# == STEP 2: Configure Display and Parameters ==
elif st.session_state.current_step == "configure_display":
    st.header("Step 2: Configure Analysis Parameters")
    st.sidebar.header("Display Configuration")

    # --- Basemap Expander (Corrected Placement) ---
    with st.expander("Basemap & SEGY Info", expanded=True):
        st.subheader("SEGY Information")
        if st.session_state.header_loader:
            # --- Added: Get additional SEGY info ---
            time_range_str = "N/A"
            inline_range_str = "N/A"
            xline_range_str = "N/A"
            num_samples = 0 # Initialize num_samples

            try:
                # Get time range
                if st.session_state.dt and st.session_state.segy_temp_file_path:
                    with segyio.open(st.session_state.segy_temp_file_path, ignore_geometry=True) as f:
                        try:
                            # Try to get the number of samples from the first trace
                            if f.tracecount > 0:
                                # Get the first trace as a numpy array
                                first_trace = f.trace[0]
                                if hasattr(first_trace, 'shape'):
                                    num_samples = first_trace.shape[0]
                                else:
                                    # If first_trace doesn't have shape, try to get length
                                    num_samples = len(first_trace)
                            else:
                                # If no traces, try to get from samples attribute
                                num_samples = len(f.samples)

                            # Log the trace type for debugging
                            logging.info(f"First trace type: {type(f.trace)}, samples: {num_samples}")

                            if num_samples > 0:
                                time_max = (num_samples - 1) * st.session_state.dt
                                time_range_str = f"0.00 - {time_max:.3f} s"
                            else:
                                time_range_str = "Error reading samples"
                        except Exception as e:
                            # Handle any errors accessing trace data
                            logging.warning(f"Error getting trace samples: {e}", exc_info=True)
                            time_range_str = "Error reading samples"
                            # Try alternative method to get samples
                            try:
                                num_samples = len(f.samples)
                                logging.info(f"Got samples from f.samples: {num_samples}")
                                if num_samples > 0:
                                    time_max = (num_samples - 1) * st.session_state.dt
                                    time_range_str = f"0.00 - {time_max:.3f} s"
                            except Exception as e2:
                                logging.warning(f"Alternative method also failed: {e2}")
                                num_samples = 0

                # Get inline/crossline ranges
                try:
                    range_dict = st.session_state.header_loader.get_inline_crossline_range()

                    # --- Added Logging ---
                    logging.info(f"Returned from get_inline_crossline_range: Type={type(range_dict)}, Value={range_dict}")
                    # --- End Added Logging ---

                    # Verify that range_dict is a dictionary and has all required keys
                    if (range_dict and isinstance(range_dict, dict) and
                        all(key in range_dict for key in ['inline_min', 'inline_max', 'xline_min', 'xline_max'])):
                        inline_range_str = f"{range_dict['inline_min']} - {range_dict['inline_max']}"
                        xline_range_str = f"{range_dict['xline_min']} - {range_dict['xline_max']}"
                    else:
                        logging.warning("Invalid range dictionary returned from get_inline_crossline_range")
                        inline_range_str = "N/A"
                        xline_range_str = "N/A"
                except Exception as e:
                    logging.warning(f"Error processing inline/crossline ranges: {e}")
                    inline_range_str = "N/A"
                    xline_range_str = "N/A"

            except Exception as e:
                logging.warning(f"Could not retrieve full SEGY info: {e}", exc_info=True)
                st.warning(f"Could not retrieve full SEGY info: {e}")
                # Log more detailed information for debugging
                if 'range_dict' in locals():
                    logging.info(f"range_dict type: {type(range_dict)}, value: {range_dict}")
            # --- End Added ---

            # Display SEGY file information (Updated)
            st.info(f"""
            **SEGY File Information:**
            - Sampling Interval: {st.session_state.dt*1000:.2f} ms
            - Number of Samples: {num_samples if num_samples > 0 else 'N/A'}
            - Time Range: {time_range_str}
            - Number of Traces: {st.session_state.trace_count}
            - Inline Range: {inline_range_str}
            - Crossline Range: {xline_range_str}
            """)

            st.subheader("Basemap")
            # Use checkbox to show/hide basemap controls and plot
            show_basemap_controls = st.checkbox("Show Basemap Controls", value=st.session_state.show_basemap, key="show_basemap_controls_checkbox")
            if show_basemap_controls:
                # Surface selection for basemap
                well_df_basemap = None
                if st.session_state.well_df is not None and not st.session_state.well_df.empty:
                    available_surfaces = get_surfaces_streamlit(st.session_state.well_df)

                    # Use session state to store selected surfaces
                    previous_selection = st.session_state.selected_surfaces_for_basemap.copy() if st.session_state.selected_surfaces_for_basemap else []

                    st.session_state.selected_surfaces_for_basemap = st.multiselect(
                        "Select Surfaces for Basemap",
                        options=available_surfaces,
                        default=st.session_state.selected_surfaces_for_basemap or available_surfaces[:min(len(available_surfaces), 3)]
                    )

                    # Check if selection changed
                    if set(previous_selection) != set(st.session_state.selected_surfaces_for_basemap):
                        st.session_state.basemap_needs_update = True

                    # Add detail level selector for basemap performance
                    st.session_state.basemap_detail_level = st.select_slider(
                        "Basemap Detail Level",
                        options=["low", "medium", "high"],
                        value=st.session_state.get('basemap_detail_level', 'low'),
                        help="Lower detail uses less memory but shows fewer features"
                    )

                    # Add confirm button for basemap plotting
                    if st.button("Generate Basemap", key="generate_basemap_button"):
                        st.session_state.show_basemap = True
                        st.session_state.basemap_needs_update = True

                # Only plot basemap if show_basemap is True
                if st.session_state.show_basemap:
                    # Check if we need to regenerate the basemap
                    if st.session_state.basemap_needs_update or st.session_state.basemap_figure is None:
                        try:
                            with st.spinner("Generating basemap..."):
                                # Prepare well data if needed
                                if st.session_state.well_df is not None and not st.session_state.well_df.empty and st.session_state.selected_surfaces_for_basemap:
                                    well_df_basemap = st.session_state.well_df[
                                        st.session_state.well_df["Surface"].isin(st.session_state.selected_surfaces_for_basemap)
                                    ]

                                # Generate the basemap figure with low detail level for better performance
                                detail_level = st.session_state.get('basemap_detail_level', 'low')
                                st.session_state.basemap_figure = plot_basemap_with_wells(
                                    st.session_state.header_loader,
                                    well_df_basemap,
                                    detail_level=detail_level
                                )
                                st.session_state.basemap_needs_update = False
                        except Exception as e:
                            st.error(f"Could not generate basemap: {e}")
                            logging.error(f"Basemap generation failed: {e}", exc_info=True)
                            st.session_state.show_basemap = False
                            st.session_state.basemap_figure = None

                    # Display the basemap if it exists
                    if st.session_state.basemap_figure is not None:
                        st.plotly_chart(st.session_state.basemap_figure, use_container_width=True)

                        # Add a button to hide/clear the basemap
                        if st.button("Hide Basemap", key="hide_basemap_button"):
                            st.session_state.show_basemap = False
            else:
                # If controls are hidden, ensure basemap is also hidden
                st.session_state.show_basemap = False
        else:
            st.warning("SEG-Y headers not loaded yet.")
    # --- End Basemap Expander ---

    # --- Parameter Configuration Expander ---
    with st.expander("Calculate Defaults & Configure Display", expanded=False):
        st.subheader("Parameter Configuration")
        col1, col2 = st.columns([1, 1])

        with col1:
            # This column might be redundant now, but keep structure for now
            st.subheader("Statistics Settings") # Renamed from Basemap
            if st.session_state.header_loader:
                # Settings related to calculating defaults
                st.session_state.plot_settings['sample_percent'] = slider_with_number_input(
                    "Sample Percent for Statistics",
                    min_value=0.1,
                    max_value=100.0,
                    value=st.session_state.plot_settings.get('sample_percent', 1.0),
                    step=0.1,
                    key_base="sample_percent",
                    format="%.1f"
                )
                st.session_state.plot_settings['max_traces_for_stats'] = st.number_input(
                    "Max Traces for Statistics", min_value=1, max_value=10000, value=st.session_state.plot_settings.get('max_traces_for_stats', 50), step=1
                )
            # Correctly indented else block for the if st.session_state.header_loader: check
            else:
                st.warning("SEG-Y headers not loaded yet.")

        with col2:
            st.subheader("Spectral Parameters")
            # Calculate sampling frequency for parameter limits
            sampling_freq = 1.0 / st.session_state.dt if hasattr(st.session_state, 'dt') and st.session_state.dt > 0 else 250.0

            st.session_state.plot_settings['use_band_limited'] = st.checkbox(
                "Use Band-Limited Descriptors", value=st.session_state.plot_settings.get('use_band_limited', False),
                key="use_band_limited_main"
            )
            # Use the new slider_with_number_input function for all parameters
            st.session_state.plot_settings['shape'] = slider_with_number_input(
                "Shape Parameter",
                min_value=0.0,
                max_value=2.0,
                value=st.session_state.plot_settings.get('shape', 0.35),
                step=0.01,
                key_base="shape_main",
                format="%.2f"
            )

            st.session_state.plot_settings['kmax'] = slider_with_number_input(
                "Kmax Parameter",
                min_value=0.0,
                max_value=sampling_freq,
                value=st.session_state.plot_settings.get('kmax', 120.0),
                step=1.0,
                key_base="kmax_main",
                format="%.1f"
            )

            st.session_state.plot_settings['int_val'] = slider_with_number_input(
                "Int Val Parameter",
                min_value=0.0,
                max_value=60.0,
                value=st.session_state.plot_settings.get('int_val', 35.0),
                step=0.1,
                key_base="int_val_main",
                format="%.1f"
            )

            st.session_state.plot_settings['b1'] = slider_with_number_input(
                "B1 Parameter (Hz)",
                min_value=1.0,
                max_value=sampling_freq,
                value=st.session_state.plot_settings.get('b1', 5.0),
                step=0.1,
                key_base="b1_main",
                format="%.1f"
            )

            st.session_state.plot_settings['b2'] = slider_with_number_input(
                "B2 Parameter (Hz)",
                min_value=1.0,
                max_value=sampling_freq,
                value=st.session_state.plot_settings.get('b2', 40.0),
                step=0.1,
                key_base="b2_main",
                format="%.1f"
            )

            st.session_state.plot_settings['p_bandwidth'] = slider_with_number_input(
                "P Bandwidth Parameter",
                min_value=1.0,
                max_value=3.0,
                value=st.session_state.plot_settings.get('p_bandwidth', 2.0),
                step=0.01,
                key_base="p_bandwidth_main",
                format="%.2f"
            )

            st.session_state.plot_settings['roll_percent'] = slider_with_number_input(
                "Roll Percent Parameter",
                min_value=0.0,
                max_value=0.95,
                value=st.session_state.plot_settings.get('roll_percent', 0.80),
                step=0.01,
                key_base="roll_percent_main",
                format="%.2f"
            )

            # Add epsilon parameter for WOSS numerical stability
            st.session_state.plot_settings['epsilon'] = st.number_input(
                "Epsilon (WOSS Numerical Stability)",
                min_value=1e-10,
                max_value=1e-2,
                value=st.session_state.plot_settings.get('epsilon', 1e-4),
                format="%.1e",
                help="Small value added for numerical stability in WOSS calculation. Default: 1e-4"
            )

        # Calculate Defaults Button (moved outside columns)
        if st.button("Calculate Defaults & Configure Display", key="calc_defaults"):
            if st.session_state.header_loader:
                try:
                    # Use the actual path (could be temp) from the loader object
                    segy_path = st.session_state.header_loader.source_file_path # Use the stored path
                    # Use st.spinner for feedback during calculation
                    with st.spinner("Calculating statistics and default parameters..."):
                        # Prepare spectral parameters for descriptor calculation
                        spectral_params = {
                            'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                            'shape': st.session_state.plot_settings.get('shape', 0.35),
                            'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                            'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                            'b1': st.session_state.plot_settings.get('b1', 5.0),
                            'b2': st.session_state.plot_settings.get('b2', 40.0),
                            'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                            'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80),
                            # Include WOSS-specific parameters separately
                            'epsilon': st.session_state.plot_settings.get('epsilon', 1e-4),
                            'fdom_exponent': st.session_state.plot_settings.get('fdom_exponent', 2.0)
                        }

                        defaults = calculate_stats_and_defaults(
                            segy_path,
                            st.session_state.header_loader,
                            st.session_state.dt,
                            st.session_state.plot_settings.get('sample_percent', 1.0),
                            st.session_state.plot_settings.get('max_traces_for_stats', 500),
                            **spectral_params
                        )
                    if defaults:
                        st.session_state.stats_defaults = defaults
                        # Update plot_settings with calculated defaults (e.g., percentiles)
                        st.session_state.plot_settings.update(defaults.get('defaults', {}))
                        st.success("Default parameters calculated based on sample trace.")
                        logging.info("Calculated default parameters and statistics.")
                        # Rerun to display the parameter widgets with new defaults
                        st.rerun()
                    else:
                        st.error("Failed to calculate default parameters.")
                except Exception as e:
                    st.error(f"Error calculating defaults: {e}")
                    logging.error(f"Default calculation failed: {e}", exc_info=True)
            else:
                st.warning("Load SEG-Y data first to calculate defaults.")

        # Display widgets only after defaults are calculated
        if st.session_state.stats_defaults is not None:
            # Use tabs for better organization
            tab_general, tab_spectral, tab_display = st.tabs(["General", "Spectral Descriptors", "Statistic"])

            with tab_general:
                st.subheader("Plot Limits Settings")

                # Time (Y-axis) limits for plots
                st.markdown("### Time (Y-axis) Limits")
                col1, col2 = st.columns(2)

                with col1:
                    # Get the trace length in seconds based on dt
                    trace_length = 0
                    if hasattr(st.session_state, 'stats_defaults') and st.session_state.stats_defaults:
                        if 'trace_length' in st.session_state.stats_defaults:
                            trace_length = st.session_state.stats_defaults['trace_length']
                        elif 'Input Signal' in st.session_state.stats_defaults.get('stats', {}):
                            # Estimate from input signal length
                            signal_stats = st.session_state.stats_defaults['stats']['Input Signal']
                            if 'Length' in signal_stats:
                                trace_length = float(signal_stats['Length']) * st.session_state.dt

                    # Default to 0-4 seconds if we can't determine
                    if trace_length <= 0:
                        trace_length = 4.0

                    st.session_state.plot_settings['time_min'] = st.number_input(
                        "Time Min (seconds)",
                        min_value=0.0,
                        max_value=trace_length,
                        value=st.session_state.plot_settings.get('time_min', 0.0),
                        step=0.1,
                        format="%.2f"
                    )

                with col2:
                    st.session_state.plot_settings['time_max'] = st.number_input(
                        "Time Max (seconds)",
                        min_value=0.0,
                        max_value=trace_length * 1.5,  # Allow some extra room
                        value=st.session_state.plot_settings.get('time_max', trace_length),
                        step=0.1,
                        format="%.2f"
                    )

                # Frequency limits for spectrograms
                st.markdown("### Frequency Limits for Spectrograms")
                st.markdown("These settings control the frequency range displayed in Magnitude Spectrogram and Magnitude*Voice plots.")

                # Calculate Nyquist frequency
                nyquist_freq = 0.5 / st.session_state.dt if hasattr(st.session_state, 'dt') and st.session_state.dt > 0 else 125.0

                col1, col2 = st.columns(2)

                with col1:
                    st.session_state.plot_settings['freq_min'] = st.number_input(
                        "Frequency Min (Hz)",
                        min_value=0.0,
                        max_value=nyquist_freq,
                        value=st.session_state.plot_settings.get('freq_min', 0.0),
                        step=1.0,
                        format="%.1f"
                    )

                with col2:
                    st.session_state.plot_settings['freq_max'] = st.number_input(
                        "Frequency Max (Hz)",
                        min_value=0.0,
                        max_value=nyquist_freq,
                        value=st.session_state.plot_settings.get('freq_max', nyquist_freq),
                        step=1.0,
                        format="%.1f"
                    )

                # Add a note about these settings being transferred to the next step
                st.info("These time and frequency limit settings will be transferred to the next step for plotting spectral descriptors.")

                # Map the time and frequency limits to the format expected by the plotting functions
                st.session_state.plot_settings['Time (Y-axis)'] = (
                    st.session_state.plot_settings.get('time_min', 0.0),
                    st.session_state.plot_settings.get('time_max', trace_length)
                )
                st.session_state.plot_settings['Frequency'] = (
                    st.session_state.plot_settings.get('freq_min', 0.0),
                    st.session_state.plot_settings.get('freq_max', nyquist_freq)
                )

            with tab_spectral:
                # Get statistics from calculated defaults
                stats = st.session_state.stats_defaults.get('stats', {})
                defaults = st.session_state.stats_defaults.get('defaults', {})

                stats = st.session_state.stats_defaults.get('stats', {})

                # Create descriptor limits based on statistics and store in plot_settings
                descriptor_limits_display = {} # Use a separate dict for display order
                if stats:
                    for descriptor_name, stats_data in stats.items():
                        try:
                            # Use P5 and P95 as default limits, fallback to Min/Max if not available
                            min_val = float(stats_data.get('P5', stats_data.get('Min', 0)))
                            max_val = float(stats_data.get('P95', stats_data.get('Max', 1)))

                            # Special handling for specific descriptors
                            if descriptor_name in ['HFC', 'Spectral Decrease']:
                                min_val = 0.0  # These should always start at 0
                                max_val = float(stats_data.get('P95', 1.0))
                            elif descriptor_name in ['Spectral Bandwidth', 'Spectral Rolloff']:
                                min_val = 0.0
                                max_val = float(stats_data.get('Max', 50.0))
                            elif descriptor_name == 'WOSS':
                                # Use symmetric limits for WOSS
                                abs_max = max(abs(min_val), abs(max_val))
                                min_val = -abs_max
                                max_val = abs_max

                            # Store in session state for use in visualization with consistent keys
                            key_base = descriptor_name.lower().replace(' ', '_').replace('*', '').replace('/', '_')
                            min_key_plot = key_base + '_min'
                            max_key_plot = key_base + '_max'

                            # Initialize plot_settings if not already set (e.g., from a previous run)
                            if min_key_plot not in st.session_state.plot_settings:
                                st.session_state.plot_settings[min_key_plot] = min_val
                            if max_key_plot not in st.session_state.plot_settings:
                                st.session_state.plot_settings[max_key_plot] = max_val

                            # Store for display purposes
                            descriptor_limits_display[descriptor_name] = (st.session_state.plot_settings[min_key_plot], st.session_state.plot_settings[max_key_plot])

                        except (ValueError, KeyError) as e:
                            logging.warning(f"Error setting limits for {descriptor_name}: {e}")
                            # Use default fallback values and store
                            key_base = descriptor_name.lower().replace(' ', '_').replace('*', '').replace('/', '_')
                            min_key_plot = key_base + '_min'
                            max_key_plot = key_base + '_max'
                            st.session_state.plot_settings[min_key_plot] = -1.0
                            st.session_state.plot_settings[max_key_plot] = 1.0
                            descriptor_limits_display[descriptor_name] = (-1.0, 1.0)

                # --- New Descriptor and Colormap Limits Section ---
                st.subheader("Descriptor and Colormap Limits")
                st.info("Adjust display ranges and colormap scaling for each descriptor.")

                # Define defaults and available colormaps
                descriptor_settings = {
                    "Input Signal": {"default": (-1, 1), "colormap": "RdBu"},
                    "HFC": {"default": (0, 2), "colormap": "rainbow"},  # Changed from (0, 1) to (0, 2)
                    "Spectral Decrease": {"default": (0, 1), "colormap": "rainbow"},
                    "Spectral Slope": {"default": (0, 1), "colormap": "RdBu"}, # Default might need adjustment based on stats
                    "Mag*Voice Slope": {"default": (-1, 1), "colormap": "RdBu"}, # Default might need adjustment based on stats
                    "Spectral Bandwidth": {"default": (0, 50), "colormap": "rainbow"}, # Default might need adjustment based on stats
                    "Spectral Rolloff": {"default": (0, 50), "colormap": "rainbow"}, # Default might need adjustment based on stats
                    "Magnitude Spectrogram": {"default": (-1, 1), "colormap": "rainbow"}, # Not typically plotted this way, but included
                    "Magnitude * Voice": {"default": (-1, 1), "colormap": "rainbow"}, # Default might need adjustment based on stats
                    "Normalized Dominant Frequency": {"default": (0, 100), "colormap": "rainbow"}, # Default might need adjustment based on stats
                    "WOSS": {"default": (-3, 3), "colormap": "RdBu"} # Default might need adjustment based on stats
                }
                available_colormaps = pcolors.named_colorscales() # Get all Plotly colormaps

                # Create three columns for better organization
                cols = st.columns(3)

                # Use calculated stats to refine default limits where appropriate
                stats = st.session_state.stats_defaults.get('stats', {})
                if stats:
                    for desc_name, desc_stats_data in stats.items():
                        if desc_name in descriptor_settings:
                            try:
                                # Use P5/P95 from stats as the default display limits
                                min_stat = float(desc_stats_data.get('P5', desc_stats_data.get('Min', descriptor_settings[desc_name]['default'][0])))
                                max_stat = float(desc_stats_data.get('P95', desc_stats_data.get('Max', descriptor_settings[desc_name]['default'][1])))

                                # Apply special handling from previous logic
                                if desc_name in ['HFC', 'Spectral Decrease', 'Spectral Bandwidth', 'Spectral Rolloff']:
                                    min_stat = 0.0
                                elif desc_name == 'WOSS':
                                    abs_max_stat = max(abs(min_stat), abs(max_stat))
                                    min_stat = -abs_max_stat
                                    max_stat = abs_max_stat

                                # Update the default tuple in descriptor_settings
                                descriptor_settings[desc_name]['default'] = (min_stat, max_stat)
                            except (ValueError, KeyError, TypeError):
                                logging.warning(f"Could not use stats to update default limits for {desc_name}. Using predefined defaults.")


                # Iterate through descriptors to create controls
                for idx, (descriptor, settings) in enumerate(descriptor_settings.items()):
                    col = cols[idx % 3]
                    with col:
                        st.write(f"**{descriptor}**")
                        key_base = descriptor.lower().replace(' ', '_').replace('*', '').replace('/', '_')

                        # --- Get current values from session state or use defaults ---
                        # Colormap / Display Limits
                        cmap_min_key_state = f"{key_base}_cmap_min"
                        cmap_max_key_state = f"{key_base}_cmap_max"
                        # Default colormap limits use the descriptor's default settings
                        current_colormap_limits = (
                            st.session_state.plot_settings.get(cmap_min_key_state, settings['default'][0]), # Use settings['default'] as fallback
                            st.session_state.plot_settings.get(cmap_max_key_state, settings['default'][1])  # Use settings['default'] as fallback
                        )

                        # Colormap Name
                        cmap_name_key_state = f"{key_base}_cmap_name"
                        current_colormap_name = st.session_state.plot_settings.get(cmap_name_key_state, settings['colormap'])
                        # Ensure the current colormap exists in the available list
                        if current_colormap_name not in available_colormaps:
                            current_colormap_name = settings['colormap'] # Fallback to default if saved one is invalid

                        # --- Create Widgets ---
                        # Colormap / Display limits
                        st.write("Display/Colormap Limits:")
                        cmap_min_val = st.number_input(
                            f"Min", # Unique label part removed ##{key_base}_cmap
                            value=float(current_colormap_limits[0]),
                            key=f"cmap_min_widget_{key_base}", # Unique widget key
                            format="%.3f"
                        )
                        cmap_max_val = st.number_input(
                            f"Max", # Unique label part removed ##{key_base}_cmap
                            value=float(current_colormap_limits[1]),
                            key=f"cmap_max_widget_{key_base}", # Unique widget key
                            format="%.3f"
                        )
                        # Update session state for colormap / display limits
                        st.session_state.plot_settings[cmap_min_key_state] = cmap_min_val
                        st.session_state.plot_settings[cmap_max_key_state] = cmap_max_val

                        # Colormap selection
                        selected_colormap = st.selectbox(
                            f"Colormap", # Unique label part removed ##{key_base}
                            options=available_colormaps,
                            index=available_colormaps.index(current_colormap_name) if current_colormap_name in available_colormaps else 0,
                            key=f"cmap_select_widget_{key_base}" # Unique widget key
                        )
                        # Update session state for colormap name
                        st.session_state.plot_settings[cmap_name_key_state] = selected_colormap

                        st.markdown("---") # Separator between descriptors

            with tab_display:
                st.info("Percentiles calculated from sample trace:")
                stats = st.session_state.stats_defaults.get('stats', {})
                # Use st.json for better display of the stats dictionary
                st.json(stats, expanded=False)

                # Use calculated percentiles as defaults for normalization limits
                # ... existing normalization settings ...

                # Colormap selection is now handled in the 'Spectral Descriptors' tab.
                # Keep the section colormap setting here.
                st.session_state.plot_settings['section_colormap'] = st.selectbox(
                    "Colormap for Seismic Section Background", options=['gray', 'seismic', 'coolwarm', 'RdBu'],
                    index=['gray', 'seismic', 'coolwarm', 'RdBu'].index(st.session_state.plot_settings.get('section_colormap', 'gray')),
                    key="section_colormap_selector"
                )

            # Confirmation Button
            st.markdown("---")
            if st.button("Confirm Settings & Proceed", key="confirm_settings"):
                st.session_state.current_step = "select_mode"
                logging.info("Settings confirmed, proceeding to mode selection.")
                st.rerun()
        else:
            st.info("Click 'Calculate Defaults & Configure Display' to proceed.")
    # --- End Parameter Configuration Expander ---


# == STEP 3: Select Mode and Traces ==
elif st.session_state.current_step == "select_mode":
    st.header("Step 3: Select Analysis Mode and Traces")
    st.sidebar.header("Mode Selection")

    # Mode Selection
    st.session_state.selection_mode = st.sidebar.selectbox(
        "Select Mode",
        options=[
            "By well markers",
            "Single inline (all crosslines)",
            "Single crossline (all inlines)",
            "By inline/crossline section (AOI)",
            "By Polyline File Import"
        ],
        index=0
    )

    # Well Markers Mode
    if st.session_state.selection_mode == "By well markers":
        st.subheader("Well Markers Mode")
        if st.session_state.well_df is not None and not st.session_state.well_df.empty:
            # Use the Streamlit-compatible function to get available markers
            available_markers = get_surfaces_streamlit(st.session_state.well_df)
            st.session_state.selected_well_markers = st.multiselect(
                "Select Well Markers",
                options=available_markers,
                default=available_markers[:min(len(available_markers), 3)] # Default to first 3 or fewer
            )
            st.session_state.plot_mode_wells = st.radio(
                "Plot Mode",
                options=[1, 2],
                format_func=lambda x: "Individual Plots" if x == 1 else "Comparative Plots",
                index=0
            )
            st.session_state.plot_twt = st.checkbox("Plot Two-Way Travel Time (TWT)", value=False)
        else:
            st.warning("No well data available. Please upload well data in Step 1.")

    # Single Inline Mode
    elif st.session_state.selection_mode == "Single inline (all crosslines)":
        st.subheader("Single Inline Mode")
        if st.session_state.header_loader:
            # Get min and max inline numbers
            min_inline = int(np.min(st.session_state.header_loader.inlines))
            max_inline = int(np.max(st.session_state.header_loader.inlines))

            # Default to previously selected inline or min inline
            default_inline = st.session_state.selected_inline if st.session_state.selected_inline else min_inline

            # Inline number selection
            st.session_state.selected_inline = st.number_input(
                f"Specify an inline number ({min_inline}-{max_inline}):",
                min_value=min_inline,
                max_value=max_inline,
                value=default_inline,
                step=1
            )

            # Batch size selection if GPU is available
            if GPU_AVAILABLE:
                suggested_batch, free_mb = get_suggested_batch_size()
                st.info(f"Estimated free GPU memory: {free_mb:.1f} MB. Suggested batch size: {suggested_batch}")
                st.session_state.batch_size = st.number_input(
                    "Batch size for GPU processing:",
                    min_value=10,
                    max_value=4000,
                    value=suggested_batch,
                    step=10,
                    help="Number of traces to process at once. Higher values use more GPU memory."
                )
            else:
                st.warning("GPU processing not available. Processing will be slower.")
                st.session_state.batch_size = None
        else:
            st.warning("SEG-Y headers not loaded yet.")

    # Single Crossline Mode
    elif st.session_state.selection_mode == "Single crossline (all inlines)":
        st.subheader("Single Crossline Mode")
        if st.session_state.header_loader:
            # Get min and max crossline numbers
            min_crossline = int(np.min(st.session_state.header_loader.crosslines))
            max_crossline = int(np.max(st.session_state.header_loader.crosslines))

            # Default to previously selected crossline or min crossline
            default_crossline = st.session_state.selected_crossline if st.session_state.selected_crossline else min_crossline

            # Crossline number selection
            st.session_state.selected_crossline = st.number_input(
                f"Specify a crossline number ({min_crossline}-{max_crossline}):",
                min_value=min_crossline,
                max_value=max_crossline,
                value=default_crossline,
                step=1
            )

            # Batch size selection if GPU is available
            if GPU_AVAILABLE:
                suggested_batch, free_mb = get_suggested_batch_size()
                st.info(f"Estimated free GPU memory: {free_mb:.1f} MB. Suggested batch size: {suggested_batch}")
                st.session_state.batch_size = st.number_input(
                    "Batch size for GPU processing:",
                    min_value=10,
                    max_value=4000,
                    value=suggested_batch,
                    step=10,
                    help="Number of traces to process at once. Higher values use more GPU memory."
                )
            else:
                st.warning("GPU processing not available. Processing will be slower.")
                st.session_state.batch_size = None
        else:
            st.warning("SEG-Y headers not loaded yet.")

    # AOI Mode
    elif st.session_state.selection_mode == "By inline/crossline section (AOI)":
        st.subheader("AOI Mode")

        # Get actual inline/crossline ranges from the loaded SEG-Y file
        if st.session_state.header_loader:
            # Extract actual inline/crossline ranges from the header loader
            inlines = st.session_state.header_loader.inlines
            crosslines = st.session_state.header_loader.crosslines

            # Get the full inline/crossline range using the new method
            range_dict = st.session_state.header_loader.get_inline_crossline_range()
            actual_inline_min = range_dict['inline_min']
            actual_inline_max = range_dict['inline_max']
            actual_xline_min = range_dict['xline_min']
            actual_xline_max = range_dict['xline_max']

            # Initialize session state values if not already set
            if st.session_state.aoi_inline_min is None:
                st.session_state.aoi_inline_min = actual_inline_min
            if st.session_state.aoi_inline_max is None:
                st.session_state.aoi_inline_max = actual_inline_max
            if st.session_state.aoi_xline_min is None:
                st.session_state.aoi_xline_min = actual_xline_min
            if st.session_state.aoi_xline_max is None:
                st.session_state.aoi_xline_max = actual_xline_max

            # No need to initialize fixed values for specific inline/crossline as we only support Full AOI
            st.session_state.aoi_plot_fixed_value = None

        # Display AOI bounds
        col1, col2 = st.columns(2)
        with col1:
            st.session_state.aoi_inline_min = st.number_input(
                "Inline Min",
                value=st.session_state.aoi_inline_min or 0,
                step=1,
                key="aoi_inline_min_input"
            )
            st.session_state.aoi_xline_min = st.number_input(
                "Crossline Min",
                value=st.session_state.aoi_xline_min or 0,
                step=1,
                key="aoi_xline_min_input"
            )

        with col2:
            st.session_state.aoi_inline_max = st.number_input(
                "Inline Max",
                value=st.session_state.aoi_inline_max or 100,
                step=1,
                key="aoi_inline_max_input"
            )
            st.session_state.aoi_xline_max = st.number_input(
                "Crossline Max",
                value=st.session_state.aoi_xline_max or 100,
                step=1,
                key="aoi_xline_max_input"
            )

        # Processing option selection - only Full AOI is available
        st.session_state.aoi_processing_option = "Full AOI"  # Set directly to Full AOI
        st.info("Processing Option: Full AOI")

        # No specific inline/crossline input needed as we only support Full AOI
        st.session_state.aoi_plot_section_type = None
        st.session_state.aoi_plot_fixed_value = None

        # Also ensure the headers dataframe is properly loaded and filtered
        if st.session_state.header_loader:
            headers_df = pd.DataFrame({
                'inline': st.session_state.header_loader.inlines,
                'crossline': st.session_state.header_loader.crosslines,
                'x': st.session_state.header_loader.x_coords,
                'y': st.session_state.header_loader.y_coords
            })
            headers_df['trace_idx'] = st.session_state.header_loader.unique_indices
        else:
            st.warning("SEG-Y headers not loaded yet.")
            headers_df = pd.DataFrame()

        # Store the filtered AOI bounds for reference
        st.session_state.aoi_bounds = {
            'inline_min': st.session_state.aoi_inline_min,
            'inline_max': st.session_state.aoi_inline_max,
            'xline_min': st.session_state.aoi_xline_min,
            'xline_max': st.session_state.aoi_xline_max
        }

    # Polyline Mode
    elif st.session_state.selection_mode == "By Polyline File Import":
        st.subheader("Polyline Mode")

        # Polyline File Upload
        st.subheader("Upload Polyline File")
        polyline_file = st.file_uploader("Choose a polyline file", type=["txt", "csv"], key="polyline_file_uploader")
        if polyline_file is not None:
            st.session_state.polyline_file_info = {
                'name': polyline_file.name,
                'buffer': polyline_file
            }
            st.success(f"Uploaded {polyline_file.name}")

            # Show tolerance slider only after file is uploaded
            st.session_state.polyline_tolerance = st.slider(
                "Polyline Tolerance",
                min_value=0.0,
                max_value=20.0,  # Maximum value of 20.0
                value=st.session_state.polyline_tolerance,
                step=1.0,  # Changed from 0.1 to 1.0
                key="polyline_tolerance_slider",
                help="Maximum distance from polyline to include traces (larger values select more traces)"
            )
        else:
            st.warning("Please upload a polyline file before proceeding.")

    # Proceed Button
    if st.button("Proceed", key="proceed_button"):
        st.session_state.current_step = "select_traces"
        st.rerun()


# == STEP 3.5: Select Traces ==
elif st.session_state.current_step == "select_traces":
    st.header("Step 3.5: Select Traces for Analysis")

    if st.session_state.selection_mode == "By well markers":
        if st.session_state.well_df is not None and not st.session_state.well_df.empty:
            # Get well marker pairs using the Streamlit-compatible function
            well_marker_dict = get_well_marker_pairs_streamlit(st.session_state.well_df)

            # Filter by selected markers if any
            if st.session_state.selected_well_markers:
                filtered_dict = {}
                for display_str, idx in well_marker_dict.items():
                    surface = display_str.split(" - ")[1]
                    if surface in st.session_state.selected_well_markers:
                        filtered_dict[display_str] = idx
                well_marker_dict = filtered_dict

            # Use Streamlit multiselect for well-marker pairs
            selected_pairs = st.multiselect(
                "Select Well-Marker Pairs for Analysis",
                options=list(well_marker_dict.keys()),
                default=list(well_marker_dict.keys())[:min(3, len(well_marker_dict))]
            )

            if selected_pairs:
                # Get the selected indices
                selected_indices = [well_marker_dict[pair] for pair in selected_pairs]
                st.session_state.selected_indices = selected_indices

                # Load trace data for selected well markers
                with st.spinner("Loading trace data for selected well markers..."):
                    try:
                        loaded_trace_data = []
                        for idx in selected_indices:
                            row = st.session_state.well_df.iloc[idx]

                            well_x, well_y = row["X"], row["Y"]
                            trace_idx = get_nearest_trace_index(st.session_state.header_loader, well_x, well_y)

                            # Load the trace sample
                            trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)

                            # Create trace data dictionary
                            trace_data = {
                                'trace_sample': trace_sample,
                                'trace_idx': trace_idx,
                                'well_marker_name': f"{row['Well']} - {row['Surface']}",
                                'marker_value': row.get('TWT_sec', None) if st.session_state.plot_twt else None
                            }
                            loaded_trace_data.append(trace_data)

                        st.session_state.loaded_trace_data = loaded_trace_data
                        st.success(f"Loaded {len(loaded_trace_data)} traces for analysis.")

                        # Add a button to calculate descriptors
                        st.markdown("---")
                        st.markdown("### Next Steps:")
                        st.markdown("1. Click **Calculate Descriptors** to process the selected traces and compute signal descriptors")
                        st.markdown("2. Review the statistical summary that will appear")
                        st.markdown("3. Click **View Results** to see the detailed analysis")

                        if st.button("Calculate Descriptors", key="calculate_descriptors_button", help="Process the selected traces and compute spectral descriptors"):
                            # Calculate descriptors
                            with st.spinner("Calculating spectral descriptors..."):
                                calculated_descriptors = []
                                for trace_data in loaded_trace_data:
                                    # Use the descriptor settings from plot_settings
                                    descriptor_settings = {
                                        'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                                        'shape': st.session_state.plot_settings.get('shape', 0.35),
                                        'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                                        'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                                        'b1': st.session_state.plot_settings.get('b1', 5.0),
                                        'b2': st.session_state.plot_settings.get('b2', 40.0),
                                        'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                                        'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80),
                                        'epsilon': st.session_state.plot_settings.get('epsilon', 1e-4)
                                    }

                                    # Ensure time and frequency limits are properly set
                                    if 'Time (Y-axis)' not in st.session_state.plot_settings and 'time_min' in st.session_state.plot_settings:
                                        st.session_state.plot_settings['Time (Y-axis)'] = (
                                            st.session_state.plot_settings.get('time_min', 0.0),
                                            st.session_state.plot_settings.get('time_max', 4.0)
                                        )

                                    if 'Frequency' not in st.session_state.plot_settings and 'freq_min' in st.session_state.plot_settings:
                                        nyquist_freq = 0.5 / st.session_state.dt if hasattr(st.session_state, 'dt') and st.session_state.dt > 0 else 125.0
                                        st.session_state.plot_settings['Frequency'] = (
                                            st.session_state.plot_settings.get('freq_min', 0.0),
                                            st.session_state.plot_settings.get('freq_max', nyquist_freq)
                                        )

                                    # Calculate descriptor
                                    try:
                                        if GPU_AVAILABLE:
                                            # Remove WOSS-specific parameters from descriptor_settings
                                            gpu_descriptor_settings = {
                                                k: v for k, v in descriptor_settings.items()
                                                if k not in ['epsilon', 'fdom_exponent', 'hfc_p95']
                                            }

                                            descriptor = dlogst_spec_descriptor_gpu(
                                                trace_data['trace_sample'],
                                                st.session_state.dt,
                                                **gpu_descriptor_settings
                                            )
                                        else:
                                            # Fallback to CPU version if available
                                            st.warning("GPU not available. Using CPU for descriptor calculation (slower).")
                                            # This would need a CPU implementation
                                            descriptor = {}
                                        calculated_descriptors.append(descriptor)
                                    except Exception as e:
                                        st.error(f"Error calculating descriptor for {trace_data['well_marker_name']}: {e}")
                                        calculated_descriptors.append({})

                                st.session_state.calculated_descriptors = calculated_descriptors
                                st.session_state.analysis_complete = True
                                st.success("Descriptor calculation complete.")

                                # Calculate statistics for the descriptors
                                if calculated_descriptors:
                                    # Initialize dictionaries to store statistics for all available outputs
                                    stats = {}

                                    # Create mapping between display names and internal descriptor keys
                                    descriptor_mapping = {
                                        # Signal data
                                        "Input Signal": "data",

                                        # Spectrograms and time-frequency representations
                                        "Magnitude Spectrogram": "tf_map",
                                        "Magnitude * Voice": "mag_voice",

                                        # Frequency-related descriptors
                                        "Normalized dominant frequencies": "norm_fdom",
                                        "Normalized Dominant Frequency": "norm_fdom",
                                        "Dominant Frequency": "fdom",
                                        "Peak Frequency": "peak_freq",
                                        "Spectral Centroid": "spec_centroid",

                                        # Slope-related descriptors
                                        "Spectral Slope": "spec_slope",
                                        "Mag*Voice Slope": "mag_voice_slope",
                                        "Voice Slope": "voice_slope",
                                        "Slope Magnitude": "mag_voice_slope",

                                        # Bandwidth and spectral shape descriptors
                                        "Spectral Bandwidth": "spec_bandwidth",
                                        "Spectral Rolloff": "spec_rolloff",
                                        "Spectral Decrease": "spec_decrease",

                                        # High-frequency content
                                        "HFC": "hfc",
                                        "Normalized HFC": "hfc",  # We'll normalize this manually

                                        # Composite descriptors
                                        "WOSS": "WOSS"
                                    }

                                    # Initialize stats for all available outputs
                                    for output in AVAILABLE_OUTPUTS_SINGLE + AVAILABLE_OUTPUTS_MULTI:
                                        if output not in stats: # Initialize for all, including Mag Spec
                                            stats[output] = {'values': []}

                                    # Add custom stats for normalized values
                                    stats['Normalized HFC'] = {'values': []}

                                    # Collect values for each descriptor
                                    for desc in calculated_descriptors:
                                        if not desc:  # Skip empty descriptors
                                            continue

                                        # Process each available output
                                        for output_name, values_dict in stats.items():
                                            # Get the corresponding internal key
                                            internal_key = descriptor_mapping.get(output_name)

                                            # Skip if no mapping or descriptor doesn't exist in current result
                                            if not internal_key:
                                                continue
                                            if internal_key not in desc:
                                                continue

                                            # Special cases for 2D Spectrogram statistics (mean of the 2D array)
                                            if output_name == "Magnitude Spectrogram" and isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 2:
                                                mean_mag_spec = np.mean(desc[internal_key])
                                                if not np.isnan(mean_mag_spec): values_dict['values'].append(mean_mag_spec)
                                            # Add case for Magnitude * Voice
                                            elif output_name == "Magnitude * Voice" and isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 2:
                                                mean_mag_spec = np.mean(desc[internal_key])
                                                if not np.isnan(mean_mag_spec): values_dict['values'].append(mean_mag_spec)
                                            # Special case for normalized HFC
                                            elif output_name == 'Normalized HFC' and 'hfc' in desc:
                                                hfc_p95 = st.session_state.plot_settings.get('hfc_p95', 1.0)
                                                if hfc_p95 > 0:
                                                    normalized_hfc = desc['hfc'] / hfc_p95
                                                    values_dict['values'].extend(normalized_hfc)
                                            # Handle WOSS calculation if not already present
                                            elif output_name == 'WOSS' and 'WOSS' not in desc and all(k in desc for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                                                woss = calculate_woss(desc, st.session_state.plot_settings)
                                                values_dict['values'].extend(woss)
                                            # Regular case (for 1D arrays)
                                            elif isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 1:
                                                values_dict['values'].extend(desc[internal_key])

                                    # Calculate statistics for each descriptor
                                    for desc_name, desc_stats in stats.items():
                                        if desc_stats['values']:
                                            values = np.array(desc_stats['values'])
                                            desc_stats['min'] = np.min(values)
                                            desc_stats['max'] = np.max(values)
                                            desc_stats['p5'] = np.percentile(values, 5)
                                            desc_stats['p90'] = np.percentile(values, 90)
                                        else:
                                            desc_stats['min'] = 'N/A'
                                            desc_stats['max'] = 'N/A'
                                            desc_stats['p5'] = 'N/A'
                                            desc_stats['p90'] = 'N/A'

                                    # Display the statistics in an expander
                                    with st.expander("📊 Descriptor Statistics Summary", expanded=True):
                                        st.markdown("### Signal Descriptor Statistics")
                                        st.markdown("The following statistics have been calculated for the selected traces:")

                                        # Group descriptors into categories for better organization
                                        categories = {
                                            "Primary Descriptors": [
                                                "Slope Magnitude", "Normalized HFC", "Normalized Dominant Frequencies",
                                                "WOSS", "Normalized Dominant Frequency", "Mag*Voice Slope"
                                            ],
                                            "Spectral Properties": [
                                                "Spectral Slope", "Spectral Bandwidth", "Spectral Rolloff",
                                                "Spectral Decrease", "Magnitude * Voice"
                                            ],
                                            "Signal Characteristics": [
                                                "Input Signal", "HFC", "Magnitude Spectrogram"
                                            ],
                                            "Other Descriptors": []  # For any remaining descriptors
                                        }

                                        # Assign each descriptor to a category
                                        categorized_stats = {cat: [] for cat in categories.keys()}

                                        for desc_name, desc_stats in stats.items():
                                            if not desc_stats['values']:  # Skip empty descriptors
                                                continue

                                            # Find which category this descriptor belongs to
                                            assigned = False
                                            for cat_name, cat_descriptors in categories.items():
                                                if desc_name in cat_descriptors:
                                                    if isinstance(desc_stats['min'], (int, float)):
                                                        categorized_stats[cat_name].append({
                                                            "Descriptor": desc_name,
                                                            "Min": desc_stats['min'],  # Keep as numeric
                                                            "Max": desc_stats['max'],  # Keep as numeric
                                                            "P5": desc_stats['p5'],    # Keep as numeric
                                                            "P90": desc_stats['p90']   # Keep as numeric
                                                        })
                                                    else:
                                                        categorized_stats[cat_name].append({
                                                            "Descriptor": desc_name,
                                                            "Min": desc_stats['min'],
                                                            "Max": desc_stats['max'],
                                                            "P5": desc_stats['p5'],
                                                            "P90": desc_stats['p90']
                                                        })
                                                    assigned = True
                                                    break

                                            # If not assigned to any specific category, put in "Other Descriptors"
                                            if not assigned and desc_stats['values']:
                                                if isinstance(desc_stats['min'], (int, float)):
                                                    categorized_stats["Other Descriptors"].append({
                                                        "Descriptor": desc_name,
                                                        "Min": desc_stats['min'],  # Keep as numeric
                                                        "Max": desc_stats['max'],  # Keep as numeric
                                                        "P5": desc_stats['p5'],    # Keep as numeric
                                                        "P90": desc_stats['p90']   # Keep as numeric
                                                    })
                                                else:
                                                    categorized_stats["Other Descriptors"].append({
                                                        "Descriptor": desc_name,
                                                        "Min": desc_stats['min'],
                                                        "Max": desc_stats['max'],
                                                        "P5": desc_stats['p5'],
                                                        "P90": desc_stats['p90']
                                                    })

                                        # Display tables by category
                                        for cat_name, cat_stats in categorized_stats.items():
                                            if cat_stats:  # Only show categories with data
                                                st.subheader(cat_name)
                                                # Create DataFrame with numeric values
                                                df = pd.DataFrame(cat_stats)

                                                # Format the DataFrame for display with 4 decimal places
                                                # but keep the underlying data as numeric
                                                formatted_df = df.style.format({
                                                    "Min": "{:.4f}",
                                                    "Max": "{:.4f}",
                                                    "P5": "{:.4f}",
                                                    "P90": "{:.4f}"
                                                }, na_rep="N/A")

                                                st.table(formatted_df)

                                        # Add explanation
                                        st.markdown("""
                                        **Note:**
                                        - **Min/Max**: Absolute minimum and maximum values across all traces
                                        - **P5**: 5th percentile value (95% of values are above this)
                                        - **P90**: 90th percentile value (10% of values are above this)
                                        """)

                                    # Store the statistics in session state for later use
                                    st.session_state.descriptor_statistics = stats

                        # Proceed to results - only show this button after descriptors are calculated
                        if st.session_state.analysis_complete:
                            st.markdown("---")
                            st.success("✅ Descriptor calculation is complete. You can now view the detailed results.")
                            if st.button("View Results", key="view_results_button", help="Open the detailed analysis report"):
                                st.session_state.current_step = "view_results"
                                st.rerun()
                    except Exception as e:
                        st.error(f"Error processing well markers: {e}")
                        logging.error(f"Well marker processing failed: {e}", exc_info=True)
            else:
                st.warning("Please select at least one well-marker pair.")
        else:
            st.error("No well data available. Please go back and upload well data.")
            if st.button("Back to Mode Selection"):
                st.session_state.current_step = "select_mode"
                st.rerun()

    elif st.session_state.selection_mode == "By inline/crossline section (AOI)":
        # Process AOI selection
        st.info("Processing AOI selection...")

        # Get the selected AOI
        inline_min = st.session_state.aoi_inline_min
        inline_max = st.session_state.aoi_inline_max
        xline_min = st.session_state.aoi_xline_min
        xline_max = st.session_state.aoi_xline_max

        # Find traces within the AOI
        with st.spinner("Finding traces within the AOI..."):
            try:
                # Get header dataframe
                headers_df = pd.DataFrame({
                    'inline': st.session_state.header_loader.inlines,
                    'crossline': st.session_state.header_loader.crosslines,
                    'x': st.session_state.header_loader.x_coords,
                    'y': st.session_state.header_loader.y_coords
                })
                headers_df['trace_idx'] = st.session_state.header_loader.unique_indices

                # Filter by inline/crossline range
                aoi_df = headers_df[
                    (headers_df['inline'] >= inline_min) &
                    (headers_df['inline'] <= inline_max) &
                    (headers_df['crossline'] >= xline_min) &
                    (headers_df['crossline'] <= xline_max)
                ]

                if aoi_df.empty:
                    st.error("No traces found within the specified AOI.")
                    if st.button("Back to Mode Selection"):
                        st.session_state.current_step = "select_mode"
                        st.rerun()
                else:
                    st.success(f"Found {len(aoi_df)} traces within the AOI.")
                    st.session_state.selected_indices = aoi_df['trace_idx'].tolist()

                    # Proceed to export configuration (only Full AOI is supported)
                    if st.button("Configure Export", key="configure_export_button"):
                        st.session_state.current_step = "configure_export"
                        st.rerun()
            except Exception as e:
                st.error(f"Error processing AOI: {e}")
                logging.error(f"AOI processing failed: {e}", exc_info=True)

    elif st.session_state.selection_mode == "By Polyline File Import":
        # Process polyline selection
        st.info("Processing polyline selection...")

        if st.session_state.polyline_file_info is None:
            st.error("No polyline file uploaded. Please go back and upload a polyline file in the polyline mode.")
            if st.button("Back to Mode Selection"):
                st.session_state.current_step = "select_mode"
                st.rerun()
        else:
            # Parse polyline file
            with st.spinner("Parsing polyline file..."):
                try:
                    polyline_content = st.session_state.polyline_file_info['buffer'].getvalue().decode('utf-8')
                    polyline_vertices = parse_polyline_string(polyline_content)
                    st.session_state.polyline_vertices = polyline_vertices

                    # Find traces near polyline
                    tolerance = st.session_state.polyline_tolerance
                    selected_indices = find_traces_near_polyline(
                        st.session_state.header_loader,
                        polyline_vertices,
                        tolerance
                    )

                    if not selected_indices:
                        st.warning(f"No traces found within {tolerance} units of the polyline. Try increasing the tolerance.")
                    else:
                        st.success(f"Found {len(selected_indices)} traces near the polyline.")
                        st.session_state.selected_indices = selected_indices

                        # Add a confirmation button to load traces
                        if 'traces_loaded_polyline' not in st.session_state:
                            st.session_state.traces_loaded_polyline = False

                        if not st.session_state.traces_loaded_polyline:
                            if st.button("Load Traces for Processing", key="load_traces_polyline"):
                                with st.spinner(f"Loading {len(selected_indices)} traces near polyline..."):
                                    # Load trace data for selected indices
                                    loaded_data_temp = []
                                    try:
                                        for idx in selected_indices:
                                            trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, idx)
                                            if trace_sample is not None:
                                                loaded_data_temp.append({
                                                    'trace_sample': trace_sample,
                                                    'trace_idx': idx
                                                })
                                        st.session_state.loaded_trace_data = loaded_data_temp
                                        st.session_state.traces_loaded_polyline = True # Mark as loaded
                                        logging.info(f"Successfully loaded {len(loaded_data_temp)} traces for polyline selection.")
                                        st.rerun() # Rerun to update UI after loading
                                    except Exception as load_err:
                                        st.error(f"Error loading traces for polyline selection: {load_err}")
                                        logging.error(f"Trace loading failed for polyline selection: {load_err}", exc_info=True)
                                        st.session_state.loaded_trace_data = [] # Clear potentially partial data
                                        st.session_state.traces_loaded_polyline = False
                        else:
                            st.success(f"✅ Traces loaded successfully. {len(st.session_state.loaded_trace_data)} traces are ready for processing.")

                        # Select outputs for multi-trace mode
                        st.subheader("Select Outputs")

                        # Use the AVAILABLE_OUTPUTS_SECTION constant for polyline mode (option 5)
                        # This excludes Magnitude Spectrogram and Magnitude * Voice
                        available_outputs = AVAILABLE_OUTPUTS_SECTION

                        # Default to previously selected outputs or WOSS and Input Signal
                        default_outputs = st.session_state.selected_outputs if st.session_state.selected_outputs else ["Input Signal", "WOSS"]
                        # Filter default outputs to only include available outputs
                        default_outputs = [output for output in default_outputs if output in available_outputs]

                        st.session_state.selected_outputs = st.multiselect(
                            "Select outputs to display:",
                            options=available_outputs,
                            default=default_outputs
                        )

                        # Batch size selection if GPU is available
                        if GPU_AVAILABLE:
                            suggested_batch, free_mb = get_suggested_batch_size()
                            st.info(f"Estimated free GPU memory: {free_mb:.1f} MB. Suggested batch size: {suggested_batch}")
                            st.session_state.batch_size = st.number_input(
                                "Batch size for GPU processing:",
                                min_value=10,
                                max_value=4000,
                                value=suggested_batch,
                                step=10,
                                help="Number of traces to process at once. Higher values use more GPU memory."
                            )
                        else:
                            st.warning("GPU processing not available. Processing will be slower.")
                            st.session_state.batch_size = None

                        if not st.session_state.selected_outputs:
                            st.warning("Please select at least one output.")
                        else:
                            # Calculate spectral descriptors
                            st.subheader("Calculate Spectral Descriptors")

                            # Get spectral parameters from plot_settings
                            spectral_params = {
                                'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                                'shape': st.session_state.plot_settings.get('shape', 0.35),
                                'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                                'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                                'b1': st.session_state.plot_settings.get('b1', 5.0),
                                'b2': st.session_state.plot_settings.get('b2', 40.0),
                                'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                                'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80)
                            }

                            # Calculate descriptors - only enable if traces are loaded
                            calculate_button_disabled = not st.session_state.traces_loaded_polyline
                            if calculate_button_disabled:
                                st.warning("Please load traces first before calculating descriptors.")

                            if st.button("Calculate Descriptors", key="calculate_descriptors_polyline", disabled=calculate_button_disabled):
                                with st.spinner("Calculating spectral descriptors..."):
                                    try:
                                        if not st.session_state.loaded_trace_data:
                                            st.error("No trace data loaded to calculate descriptors.")
                                        else:
                                            # Prepare data for GPU function (assuming all traces have same length)
                                            data_2d = np.array([item['trace_sample'] for item in st.session_state.loaded_trace_data]) # [traces, samples] format

                                            # Use GPU function
                                            if GPU_AVAILABLE:
                                                batch_size_gpu = st.session_state.get('batch_size', 512) # Get GPU batch size

                                                # Call the chunked GPU function
                                                calculated_descriptors_gpu = dlogst_spec_descriptor_gpu_2d_chunked(
                                                    data_2d,
                                                    st.session_state.dt,
                                                    batch_size_gpu,
                                                    **spectral_params # Pass spectral parameters
                                                )

                                                # Convert GPU results back to list of dicts (CPU)
                                                st.session_state.calculated_descriptors = []
                                                num_traces_calculated = data_2d.shape[0] # Get number of traces from input

                                                # Check if calculated_descriptors_gpu is a dict and contains expected keys
                                                if isinstance(calculated_descriptors_gpu, dict):
                                                    # Ensure all expected arrays have the correct second dimension (number of traces)
                                                    valid_calculation = True
                                                    for key, arr in calculated_descriptors_gpu.items():
                                                        if isinstance(arr, np.ndarray):
                                                            # Special case for freqst which is always 1D
                                                            if key == 'freqst':
                                                                continue
                                                            # Special case for data which is the original input
                                                            elif key == 'data':
                                                                if arr.shape[0] != num_traces_calculated:
                                                                    logging.error(f"Descriptor array '{key}' has incorrect first dimension: {arr.shape}, expected {num_traces_calculated}")
                                                                    valid_calculation = False
                                                                    break
                                                            # Special case for time which is 1D
                                                            elif key == 'time':
                                                                continue
                                                            # Handle 2D arrays (most descriptors)
                                                            elif arr.ndim == 2:
                                                                # For 2D arrays, first dimension should be traces, second should be time samples
                                                                if arr.shape[0] != num_traces_calculated:
                                                                    logging.error(f"Descriptor array '{key}' has incorrect first dimension: {arr.shape}, expected {num_traces_calculated}")
                                                                    valid_calculation = False
                                                                    break
                                                            # Handle 1D arrays that should be 2D
                                                            elif arr.ndim == 1 and key != 'freqst':
                                                                # For single trace, reshape to 2D
                                                                if num_traces_calculated == 1:
                                                                    logging.info(f"Reshaping 1D array '{key}' to 2D for single trace")
                                                                    calculated_descriptors_gpu[key] = arr.reshape(-1, 1)
                                                                else:
                                                                    logging.error(f"Descriptor array '{key}' is 1D but expected 2D for {num_traces_calculated} traces.")
                                                                    valid_calculation = False
                                                                    break
                                                            # Handle 3D arrays (mag, mag_voice)
                                                            elif arr.ndim == 3:
                                                                # For 3D arrays like mag and mag_voice, the first dimension should be traces
                                                                if arr.shape[0] != num_traces_calculated:
                                                                    logging.error(f"3D descriptor array '{key}' has incorrect first dimension: {arr.shape}, expected first dim {num_traces_calculated}")
                                                                    valid_calculation = False
                                                                    break

                                                    if valid_calculation:
                                                        for i in range(num_traces_calculated):
                                                            desc_dict = {}
                                                            for key, arr in calculated_descriptors_gpu.items():
                                                                if isinstance(arr, np.ndarray):
                                                                    if arr.ndim == 2:
                                                                        desc_dict[key] = arr[i, :]  # Extract the i-th row (trace) instead of column
                                                                    elif arr.ndim == 1 and key == 'freqst': # Handle freqst
                                                                        desc_dict[key] = arr
                                                                    elif arr.ndim == 3 and key in ['mag', 'mag_voice']: # Handle 3D arrays
                                                                        # For 3D arrays, extract the i-th trace's 2D slice
                                                                        desc_dict[key] = arr[i, :, :]
                                                            st.session_state.calculated_descriptors.append(desc_dict)
                                                    else:
                                                        st.error("Descriptor calculation resulted in inconsistent array shapes. Check logs.")
                                                        raise ValueError("Descriptor calculation shape mismatch.")
                                                else:
                                                    st.error("GPU descriptor calculation did not return the expected dictionary format.")
                                                    raise TypeError("Unexpected result type from GPU calculation.")

                                                # Calculate WOSS if selected and components are available
                                                if 'WOSS' in st.session_state.selected_outputs:
                                                    epsilon = st.session_state.plot_settings.get('epsilon', 1e-4)
                                                    fdom_exponent = st.session_state.plot_settings.get('fdom_exponent', 2.0) # Get exponent
                                                    for desc_dict in st.session_state.calculated_descriptors:
                                                        if all(k in desc_dict for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                                                            # Create settings dictionary for WOSS calculation
                                                            woss_settings = {
                                                                'epsilon': epsilon,
                                                                'fdom_exponent': fdom_exponent,
                                                                'hfc_p95': st.session_state.plot_settings.get('hfc_p95', 1.0)
                                                            }
                                                            desc_dict['WOSS'] = calculate_woss(desc_dict, woss_settings)
                                                        else:
                                                            logging.warning(f"Skipping WOSS calculation for a trace due to missing components (hfc, norm_fdom, mag_voice_slope).")
                                                            desc_dict['WOSS'] = np.full_like(desc_dict.get('hfc', []), np.nan) # Fill with NaN if components missing

                                                st.session_state.analysis_complete = True
                                                st.success(f"Calculated descriptors for {len(st.session_state.calculated_descriptors)} traces using GPU.")
                                                logging.info(f"Calculated descriptors for {len(st.session_state.calculated_descriptors)} traces (Polyline Mode).")
                                                # Proceed to results view
                                                st.session_state.current_step = "view_results"
                                                st.rerun()
                                            else:
                                                st.error("GPU is not available. Cannot calculate descriptors for sections efficiently.")
                                    except Exception as calc_err:
                                        st.error(f"Error calculating descriptors: {calc_err}")
                                        logging.error(f"Descriptor calculation failed (Polyline Mode): {calc_err}", exc_info=True)
                                        st.session_state.calculated_descriptors = []
                                        st.session_state.analysis_complete = False
                except Exception as e:
                    st.error(f"Error processing polyline: {e}")
                    logging.error(f"Polyline processing failed: {e}", exc_info=True)

    # Single Inline Mode
    elif st.session_state.selection_mode == "Single inline (all crosslines)":
        st.info("Processing single inline selection...")

        if st.session_state.header_loader and st.session_state.selected_inline is not None:
            try:
                # Get the selected inline number
                inline_choice = st.session_state.selected_inline

                # Filter traces by inline
                inline_mask = st.session_state.header_loader.inlines == inline_choice
                chosen_indices = st.session_state.header_loader.unique_indices[inline_mask]

                if len(chosen_indices) == 0:
                    st.error(f"No traces available for inline {inline_choice}.")
                    if st.button("Back to Mode Selection"):
                        st.session_state.current_step = "select_mode"
                        st.rerun()
                else:
                    st.success(f"Found {len(chosen_indices)} traces for inline {inline_choice}.")
                    st.session_state.selected_indices = chosen_indices.tolist()

                    # Add a confirmation button to load traces
                    if 'traces_loaded_inline' not in st.session_state:
                        st.session_state.traces_loaded_inline = False

                    if not st.session_state.traces_loaded_inline:
                        if st.button("Load Traces for Processing", key="load_traces_inline"):
                            with st.spinner(f"Loading {len(chosen_indices)} traces for inline {inline_choice}..."):
                                # --- ADDED TRACE LOADING LOGIC ---
                                loaded_data_temp = []
                                try:
                                    for idx in chosen_indices:
                                        trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, idx)
                                        if trace_sample is not None:
                                            loaded_data_temp.append({
                                                'trace_sample': trace_sample,
                                                'trace_idx': idx
                                                # Add other relevant info if needed, e.g., crossline number
                                            })
                                    st.session_state.loaded_trace_data = loaded_data_temp
                                    st.session_state.traces_loaded_inline = True # Mark as loaded
                                    logging.info(f"Successfully loaded {len(loaded_data_temp)} traces for inline {inline_choice}.")
                                    st.rerun() # Rerun to update UI after loading
                                except Exception as load_err:
                                    st.error(f"Error loading traces for inline {inline_choice}: {load_err}")
                                    logging.error(f"Trace loading failed for inline {inline_choice}: {load_err}", exc_info=True)
                                    st.session_state.loaded_trace_data = [] # Clear potentially partial data
                                    st.session_state.traces_loaded_inline = False
                                # --- END ADDED TRACE LOADING LOGIC ---
                    else:
                        st.success(f"✅ Traces loaded successfully. {len(st.session_state.loaded_trace_data)} traces are ready for processing.")

                    # Select outputs for 2D/3D mode
                    st.subheader("Select Outputs")

                    # Use the AVAILABLE_OUTPUTS_SECTION constant for inline mode (option 2)
                    # This excludes Magnitude Spectrogram and Magnitude * Voice
                    available_outputs = AVAILABLE_OUTPUTS_SECTION

                    # Default to previously selected outputs or WOSS and Input Signal
                    default_outputs = st.session_state.selected_outputs if st.session_state.selected_outputs else ["Input Signal", "WOSS"]
                    # Filter default outputs to only include available outputs
                    default_outputs = [output for output in default_outputs if output in available_outputs]

                    st.session_state.selected_outputs = st.multiselect(
                        "Select outputs to display:",
                        options=available_outputs,
                        default=default_outputs
                    )

                    if not st.session_state.selected_outputs:
                        st.warning("Please select at least one output.")
                    else:
                        # Calculate spectral descriptors
                        st.subheader("Calculate Spectral Descriptors")

                        # Get spectral parameters from plot_settings
                        spectral_params = {
                            'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                            'shape': st.session_state.plot_settings.get('shape', 0.35),
                            'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                            'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                            'b1': st.session_state.plot_settings.get('b1', 5.0),
                            'b2': st.session_state.plot_settings.get('b2', 40.0),
                            'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                            'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80)
                        }

                        # Calculate descriptors - only enable if traces are loaded
                        calculate_button_disabled = not st.session_state.traces_loaded_inline
                        if calculate_button_disabled:
                            st.warning("Please load traces first before calculating descriptors.")

                        if st.button("Calculate Descriptors", key="calculate_descriptors_inline", disabled=calculate_button_disabled):
                            with st.spinner("Calculating spectral descriptors..."):
                                # --- ADDED DESCRIPTOR CALCULATION LOGIC ---
                                try:
                                    if not st.session_state.loaded_trace_data:
                                        st.error("No trace data loaded to calculate descriptors.")
                                    else:
                                        # Prepare data for GPU function (assuming all traces have same length)
                                        data_2d = np.array([item['trace_sample'] for item in st.session_state.loaded_trace_data]) # [traces, samples] format

                                        # Use GPU function
                                        if GPU_AVAILABLE:
                                            # Choose the correct GPU function based on whether magnitude is needed
                                            # For section plots, we usually don't need the full spectrograms
                                            # Let's assume dlogst_spec_descriptor_gpu_2d_chunked is sufficient
                                            # If Magnitude Spectrogram or Mag*Voice are selected, this might need adjustment
                                            # or those options should be disabled for section plots.

                                            batch_size_gpu = st.session_state.get('batch_size', 512) # Get GPU batch size

                                            # Call the chunked GPU function (without magnitude)
                                            calculated_descriptors_gpu = dlogst_spec_descriptor_gpu_2d_chunked(
                                                data_2d,
                                                st.session_state.dt,
                                                batch_size_gpu,
                                                **spectral_params # Pass spectral parameters
                                            )

                                            # Convert GPU results back to list of dicts (CPU)
                                            st.session_state.calculated_descriptors = []
                                            num_traces_calculated = data_2d.shape[0] # Get number of traces from input

                                            # Check if calculated_descriptors_gpu is a dict and contains expected keys
                                            if isinstance(calculated_descriptors_gpu, dict):
                                                # Ensure all expected arrays have the correct second dimension (number of traces)
                                                valid_calculation = True
                                                for key, arr in calculated_descriptors_gpu.items():
                                                    if isinstance(arr, np.ndarray):
                                                        # Special case for freqst which is always 1D
                                                        if key == 'freqst':
                                                            continue
                                                        # Special case for data which is the original input
                                                        elif key == 'data':
                                                            if arr.shape[0] != num_traces_calculated:
                                                                logging.error(f"Descriptor array '{key}' has incorrect first dimension: {arr.shape}, expected {num_traces_calculated}")
                                                                valid_calculation = False
                                                                break
                                                        # Special case for time which is 1D
                                                        elif key == 'time':
                                                            continue
                                                        # Handle 2D arrays (most descriptors)
                                                        elif arr.ndim == 2:
                                                            # For 2D arrays, first dimension should be traces, second should be time samples
                                                            if arr.shape[0] != num_traces_calculated:
                                                                logging.error(f"Descriptor array '{key}' has incorrect first dimension: {arr.shape}, expected {num_traces_calculated}")
                                                                valid_calculation = False
                                                                break
                                                        # Handle 1D arrays that should be 2D
                                                        elif arr.ndim == 1 and key != 'freqst':
                                                            # For single trace, reshape to 2D
                                                            if num_traces_calculated == 1:
                                                                logging.info(f"Reshaping 1D array '{key}' to 2D for single trace")
                                                                calculated_descriptors_gpu[key] = arr.reshape(-1, 1)
                                                            else:
                                                                logging.error(f"Descriptor array '{key}' is 1D but expected 2D for {num_traces_calculated} traces.")
                                                                valid_calculation = False
                                                                break
                                                        # Handle 3D arrays (mag, mag_voice)
                                                        elif arr.ndim == 3:
                                                            # For 3D arrays like mag and mag_voice, the first dimension should be traces
                                                            if arr.shape[0] != num_traces_calculated:
                                                                logging.error(f"3D descriptor array '{key}' has incorrect first dimension: {arr.shape}, expected first dim {num_traces_calculated}")
                                                                valid_calculation = False
                                                                break


                                                if valid_calculation:
                                                    for i in range(num_traces_calculated):
                                                        desc_dict = {}
                                                        for key, arr in calculated_descriptors_gpu.items():
                                                            if isinstance(arr, np.ndarray):
                                                                if arr.ndim == 2:
                                                                    desc_dict[key] = arr[i, :]  # Extract the i-th row (trace) instead of column
                                                                elif arr.ndim == 1 and key == 'freqst': # Handle freqst
                                                                    desc_dict[key] = arr
                                                                elif arr.ndim == 3 and key in ['mag', 'mag_voice']: # Handle 3D arrays
                                                                    # For 3D arrays, extract the i-th trace's 2D slice
                                                                    desc_dict[key] = arr[i, :, :]
                                                                # Add handling for other potential 1D arrays if necessary
                                                        st.session_state.calculated_descriptors.append(desc_dict)
                                                else:
                                                     st.error("Descriptor calculation resulted in inconsistent array shapes. Check logs.")
                                                     raise ValueError("Descriptor calculation shape mismatch.")

                                            else:
                                                st.error("GPU descriptor calculation did not return the expected dictionary format.")
                                                raise TypeError("Unexpected result type from GPU calculation.")


                                            # Calculate WOSS if selected and components are available
                                            if 'WOSS' in st.session_state.selected_outputs:
                                                epsilon = st.session_state.plot_settings.get('epsilon', 1e-4)
                                                fdom_exponent = st.session_state.plot_settings.get('fdom_exponent', 2.0) # Get exponent from settings
                                                for desc_dict in st.session_state.calculated_descriptors:
                                                    if all(k in desc_dict for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                                                        # Create settings dictionary for WOSS calculation
                                                        woss_settings = {
                                                            'epsilon': epsilon,
                                                            'fdom_exponent': fdom_exponent,
                                                            'hfc_p95': st.session_state.plot_settings.get('hfc_p95', 1.0)
                                                        }
                                                        desc_dict['WOSS'] = calculate_woss(desc_dict, woss_settings)
                                                    else:
                                                        logging.warning(f"Skipping WOSS calculation for a trace due to missing components (hfc, norm_fdom, mag_voice_slope).")
                                                        desc_dict['WOSS'] = np.full_like(desc_dict.get('hfc', []), np.nan) # Fill with NaN if components missing


                                            st.session_state.analysis_complete = True
                                            st.success(f"Calculated descriptors for {len(st.session_state.calculated_descriptors)} traces using GPU.")
                                            logging.info(f"Calculated descriptors for {len(st.session_state.calculated_descriptors)} traces (Inline Mode).")
                                            # Proceed to results view
                                            st.session_state.current_step = "view_results"
                                            st.rerun()

                                        else:
                                            st.error("GPU is not available. Cannot calculate descriptors for sections efficiently.")
                                            # Optionally, implement a CPU fallback here if desired, but it will be slow.

                                except Exception as calc_err:
                                    st.error(f"Error calculating descriptors: {calc_err}")
                                    logging.error(f"Descriptor calculation failed (Inline Mode): {calc_err}", exc_info=True)
                                    st.session_state.calculated_descriptors = []
                                    st.session_state.analysis_complete = False
                                # --- END ADDED DESCRIPTOR CALCULATION LOGIC ---
            except Exception as e:
                st.error(f"Error processing inline selection: {e}")
                logging.error(f"Inline processing failed: {e}", exc_info=True)
        else:
            st.error("SEG-Y headers not loaded or inline not selected.")
            if st.button("Back to Mode Selection"):
                st.session_state.current_step = "select_mode"
                st.rerun()

    # Single Crossline Mode
    elif st.session_state.selection_mode == "Single crossline (all inlines)":
        st.info("Processing single crossline selection...")

        if st.session_state.header_loader and st.session_state.selected_crossline is not None:
            try:
                # Get the selected crossline number
                crossline_choice = st.session_state.selected_crossline

                # Filter traces by crossline
                crossline_mask = st.session_state.header_loader.crosslines == crossline_choice
                chosen_indices = st.session_state.header_loader.unique_indices[crossline_mask]

                if len(chosen_indices) == 0:
                    st.error(f"No traces available for crossline {crossline_choice}.")
                    if st.button("Back to Mode Selection"):
                        st.session_state.current_step = "select_mode"
                        st.rerun()
                else:
                    st.success(f"Found {len(chosen_indices)} traces for crossline {crossline_choice}.")
                    st.session_state.selected_indices = chosen_indices.tolist()

                    # Add a confirmation button to load traces
                    if 'traces_loaded_crossline' not in st.session_state:
                        st.session_state.traces_loaded_crossline = False

                    if not st.session_state.traces_loaded_crossline:
                        if st.button("Load Traces for Processing", key="load_traces_crossline"):
                            with st.spinner(f"Loading {len(chosen_indices)} traces for crossline {crossline_choice}..."):
                                # --- ADDED TRACE LOADING LOGIC ---
                                loaded_data_temp = []
                                try:
                                    for idx in chosen_indices:
                                        trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, idx)
                                        if trace_sample is not None:
                                            loaded_data_temp.append({
                                                'trace_sample': trace_sample,
                                                'trace_idx': idx
                                                # Add other relevant info if needed, e.g., inline number
                                            })
                                    st.session_state.loaded_trace_data = loaded_data_temp
                                    st.session_state.traces_loaded_crossline = True # Mark as loaded
                                    logging.info(f"Successfully loaded {len(loaded_data_temp)} traces for crossline {crossline_choice}.")
                                    st.rerun() # Rerun to update UI after loading
                                except Exception as load_err:
                                    st.error(f"Error loading traces for crossline {crossline_choice}: {load_err}")
                                    logging.error(f"Trace loading failed for crossline {crossline_choice}: {load_err}", exc_info=True)
                                    st.session_state.loaded_trace_data = [] # Clear potentially partial data
                                    st.session_state.traces_loaded_crossline = False
                                # --- END ADDED TRACE LOADING LOGIC ---
                    else:
                        st.success(f"✅ Traces loaded successfully. {len(st.session_state.loaded_trace_data)} traces are ready for processing.")

                    # Select outputs for 2D/3D mode
                    st.subheader("Select Outputs")

                    # Use the AVAILABLE_OUTPUTS_SECTION constant for crossline mode (option 3)
                    # This excludes Magnitude Spectrogram and Magnitude * Voice
                    available_outputs = AVAILABLE_OUTPUTS_SECTION

                    # Default to previously selected outputs or WOSS and Input Signal
                    default_outputs = st.session_state.selected_outputs if st.session_state.selected_outputs else ["Input Signal", "WOSS"]
                    # Filter default outputs to only include available outputs
                    default_outputs = [output for output in default_outputs if output in available_outputs]

                    st.session_state.selected_outputs = st.multiselect(
                        "Select outputs to display:",
                        options=available_outputs,
                        default=default_outputs
                    )

                    if not st.session_state.selected_outputs:
                        st.warning("Please select at least one output.")
                    else:
                        # Calculate spectral descriptors
                        st.subheader("Calculate Spectral Descriptors")

                        # Get spectral parameters from plot_settings
                        spectral_params = {
                            'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                            'shape': st.session_state.plot_settings.get('shape', 0.35),
                            'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                            'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                            'b1': st.session_state.plot_settings.get('b1', 5.0),
                            'b2': st.session_state.plot_settings.get('b2', 40.0),
                            'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                            'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80)
                        }

                        # Calculate descriptors - only enable if traces are loaded
                        calculate_button_disabled = not st.session_state.traces_loaded_crossline
                        if calculate_button_disabled:
                            st.warning("Please load traces first before calculating descriptors.")

                        if st.button("Calculate Descriptors", key="calculate_descriptors_crossline", disabled=calculate_button_disabled):
                            with st.spinner("Calculating spectral descriptors..."):
                                # --- ADDED DESCRIPTOR CALCULATION LOGIC ---
                                try:
                                    if not st.session_state.loaded_trace_data:
                                        st.error("No trace data loaded to calculate descriptors.")
                                    else:
                                        # Prepare data for GPU function (assuming all traces have same length)
                                        data_2d = np.array([item['trace_sample'] for item in st.session_state.loaded_trace_data]) # [traces, samples] format

                                        # Use GPU function
                                        if GPU_AVAILABLE:
                                            batch_size_gpu = st.session_state.get('batch_size', 512) # Get GPU batch size

                                            # Call the chunked GPU function (without magnitude)
                                            calculated_descriptors_gpu = dlogst_spec_descriptor_gpu_2d_chunked(
                                                data_2d,
                                                st.session_state.dt,
                                                batch_size_gpu,
                                                **spectral_params # Pass spectral parameters
                                            )

                                            # Convert GPU results back to list of dicts (CPU)
                                            st.session_state.calculated_descriptors = []
                                            num_traces_calculated = data_2d.shape[0] # Get number of traces from input

                                            # Check if calculated_descriptors_gpu is a dict and contains expected keys
                                            if isinstance(calculated_descriptors_gpu, dict):
                                                 # Ensure all expected arrays have the correct second dimension (number of traces)
                                                valid_calculation = True
                                                for key, arr in calculated_descriptors_gpu.items():
                                                    if isinstance(arr, np.ndarray):
                                                        # Special case for freqst which is always 1D
                                                        if key == 'freqst':
                                                            continue
                                                        # Special case for data which is the original input
                                                        elif key == 'data':
                                                            if arr.shape[0] != num_traces_calculated:
                                                                logging.error(f"Descriptor array '{key}' has incorrect first dimension: {arr.shape}, expected {num_traces_calculated}")
                                                                valid_calculation = False
                                                                break
                                                        # Special case for time which is 1D
                                                        elif key == 'time':
                                                            continue
                                                        # Handle 2D arrays (most descriptors)
                                                        elif arr.ndim == 2:
                                                            # For 2D arrays, first dimension should be traces, second should be time samples
                                                            if arr.shape[0] != num_traces_calculated:
                                                                logging.error(f"Descriptor array '{key}' has incorrect first dimension: {arr.shape}, expected {num_traces_calculated}")
                                                                valid_calculation = False
                                                                break
                                                        # Handle 1D arrays that should be 2D
                                                        elif arr.ndim == 1 and key != 'freqst':
                                                            # For single trace, reshape to 2D
                                                            if num_traces_calculated == 1:
                                                                logging.info(f"Reshaping 1D array '{key}' to 2D for single trace")
                                                                calculated_descriptors_gpu[key] = arr.reshape(-1, 1)
                                                            else:
                                                                logging.error(f"Descriptor array '{key}' is 1D but expected 2D for {num_traces_calculated} traces.")
                                                                valid_calculation = False
                                                                break
                                                        # Handle 3D arrays (mag, mag_voice)
                                                        elif arr.ndim == 3:
                                                            # For 3D arrays like mag and mag_voice, the first dimension should be traces
                                                            if arr.shape[0] != num_traces_calculated:
                                                                logging.error(f"3D descriptor array '{key}' has incorrect first dimension: {arr.shape}, expected first dim {num_traces_calculated}")
                                                                valid_calculation = False
                                                                break

                                                if valid_calculation:
                                                    for i in range(num_traces_calculated):
                                                        desc_dict = {}
                                                        for key, arr in calculated_descriptors_gpu.items():
                                                            if isinstance(arr, np.ndarray):
                                                                if arr.ndim == 2:
                                                                    desc_dict[key] = arr[i, :]  # Extract the i-th row (trace) instead of column
                                                                elif arr.ndim == 1 and key == 'freqst': # Handle freqst
                                                                    desc_dict[key] = arr
                                                                elif arr.ndim == 3 and key in ['mag', 'mag_voice']: # Handle 3D arrays
                                                                    # For 3D arrays, extract the i-th trace's 2D slice
                                                                    desc_dict[key] = arr[i, :, :]
                                                        st.session_state.calculated_descriptors.append(desc_dict)
                                                else:
                                                     st.error("Descriptor calculation resulted in inconsistent array shapes. Check logs.")
                                                     raise ValueError("Descriptor calculation shape mismatch.")
                                            else:
                                                st.error("GPU descriptor calculation did not return the expected dictionary format.")
                                                raise TypeError("Unexpected result type from GPU calculation.")


                                            # Calculate WOSS if selected and components are available
                                            if 'WOSS' in st.session_state.selected_outputs:
                                                epsilon = st.session_state.plot_settings.get('epsilon', 1e-4)
                                                fdom_exponent = st.session_state.plot_settings.get('fdom_exponent', 2.0) # Get exponent
                                                for desc_dict in st.session_state.calculated_descriptors:
                                                    if all(k in desc_dict for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                                                        # Create settings dictionary for WOSS calculation
                                                        woss_settings = {
                                                            'epsilon': epsilon,
                                                            'fdom_exponent': fdom_exponent,
                                                            'hfc_p95': st.session_state.plot_settings.get('hfc_p95', 1.0)
                                                        }
                                                        desc_dict['WOSS'] = calculate_woss(desc_dict, woss_settings)
                                                    else:
                                                        logging.warning(f"Skipping WOSS calculation for a trace due to missing components (hfc, norm_fdom, mag_voice_slope).")
                                                        desc_dict['WOSS'] = np.full_like(desc_dict.get('hfc', []), np.nan) # Fill with NaN

                                            st.session_state.analysis_complete = True
                                            st.success(f"Calculated descriptors for {len(st.session_state.calculated_descriptors)} traces using GPU.")
                                            logging.info(f"Calculated descriptors for {len(st.session_state.calculated_descriptors)} traces (Crossline Mode).")
                                            # Proceed to results view
                                            st.session_state.current_step = "view_results"
                                            st.rerun()

                                        else:
                                            st.error("GPU is not available. Cannot calculate descriptors for sections efficiently.")

                                except Exception as calc_err:
                                    st.error(f"Error calculating descriptors: {calc_err}")
                                    logging.error(f"Descriptor calculation failed (Crossline Mode): {calc_err}", exc_info=True)
                                    st.session_state.calculated_descriptors = []
                                    st.session_state.analysis_complete = False
                                # --- END ADDED DESCRIPTOR CALCULATION LOGIC ---
            except Exception as e:
                st.error(f"Error processing crossline selection: {e}")
                logging.error(f"Crossline processing failed: {e}", exc_info=True)
        else:
            st.error("SEG-Y headers not loaded or crossline not selected.")
            if st.button("Back to Mode Selection"):
                st.session_state.current_step = "select_mode"
                st.rerun()

    # Add a back button
    if st.button("Back to Mode Selection", key="back_to_mode_button"):
        # Reset trace loading flags when going back to mode selection
        if 'traces_loaded_inline' in st.session_state:
            st.session_state.traces_loaded_inline = False
        if 'traces_loaded_crossline' in st.session_state:
            st.session_state.traces_loaded_crossline = False
        if 'traces_loaded_polyline' in st.session_state:
            st.session_state.traces_loaded_polyline = False

        st.session_state.current_step = "select_mode"
        st.rerun()

# == STEP 3.6: Configure Export (AOI Mode) ==
elif st.session_state.current_step == "configure_export":
    st.header("Step 3.6: Configure AOI Export")

    if not st.session_state.selected_indices:
        st.error("No traces selected for export. Please go back to Step 3.")
        if st.button("Back to Mode Selection"):
            st.session_state.current_step = "select_mode"
            st.rerun()
    else:
        st.info(f"Configuring export for {len(st.session_state.selected_indices)} traces in the AOI.")

        # Determine available attributes for export
        available_for_export_display = EXPORTABLE_ATTR_DISPLAY_NAMES # Use predefined list
        selected_attrs_display = []
        selected_attrs_internal = []

        try:
            # Placeholder: In a real scenario, you might verify attribute calculation is possible
            # For now, assume all predefined attributes can be calculated/exported.
            pass # Added pass to fix indentation error on line 519

        except FileNotFoundError:
            st.error(f"Error: SEG-Y file not found at expected path: {st.session_state.header_loader.temp_file_path}. Please reload data.")
            logging.error(f"SEG-Y file not found at {st.session_state.header_loader.temp_file_path} during export config.")
            reset_state()
            st.rerun()
        except Exception as e:
            st.error(f"Error during export configuration setup: {e}")
            logging.error(f"Export config setup failed: {e}", exc_info=True)
            # Prevent proceeding without attributes
            available_for_export_display = []


        # --- Export Configuration UI ---
        st.subheader("Export Settings")

        # Attribute Selection
        selected_attrs_display = st.multiselect(
            "Select Attributes to Export:",
            options=available_for_export_display,
            default=[attr_name for attr_name in available_for_export_display if attr_name != "Original Seismic Amplitude"] # Default to all except original
        )
        selected_attrs_internal = [ATTR_NAME_MAP[attr_name] for attr_name in selected_attrs_display]

        # Grouping and Batching
        grouping_type = st.selectbox(
            "Group Export Files By:",
            options=["inline", "crossline"],
            index=0,
            key="export_grouping_select"
        )
        st.session_state.export_grouping = grouping_type # Store selection

        # Suggest batch size based on grouping
        if st.session_state.header_loader:
            # Create headers_df from the header_loader attributes if it doesn't exist
            if not hasattr(st.session_state.header_loader, 'headers_df'):
                # Create a DataFrame from the header information
                st.session_state.header_loader.headers_df = pd.DataFrame({
                    'inline': st.session_state.header_loader.inlines,
                    'crossline': st.session_state.header_loader.crosslines,
                    'x': st.session_state.header_loader.x_coords,
                    'y': st.session_state.header_loader.y_coords,
                    'trace_idx': st.session_state.header_loader.unique_indices
                })

            headers_df = st.session_state.header_loader.headers_df
            if grouping_type in headers_df.columns:
                num_unique_groups = headers_df[grouping_type].nunique()
                suggested_batch = get_suggested_batch_size_for_export(num_unique_groups)
                st.info(f"Number of unique {grouping_type}s: {num_unique_groups}. Suggested batch step: {suggested_batch}")
            else:
                suggested_batch = 1
                st.warning(f"Column '{grouping_type}' not found in headers. Defaulting batch step to 1.")
        else:
            suggested_batch = 1
            st.warning("Header data not available for suggesting batch size.")

        batch_step = st.number_input(
            f"Batch Step (Number of {grouping_type.capitalize()}s per File)",
            min_value=1,
            value=st.session_state.get('export_batch_step', suggested_batch), # Use previous or suggested
            step=1,
            key="export_batch_step_input"
        )
        st.session_state.export_batch_step = batch_step # Store selection

        # GPU Batch Size for Calculation (if GPU available)
        if GPU_AVAILABLE:
            st.session_state.batch_size = st.number_input(
                "GPU Processing Batch Size (Traces)",
                min_value=32,
                max_value=4096,
                value=st.session_state.get('batch_size', 512), # Default or previous value
                step=32,
                help="Adjust based on GPU memory. Larger values process faster but use more memory."
            )
        else:
             st.warning("GPU not available. Export requires GPU for efficient processing.")


        st.markdown("---")
        # Disable button if GPU needed but not available, or no attributes selected
        disable_export_button = (not GPU_AVAILABLE) or (not selected_attrs_internal)
        if st.button("Start Export Process", key="start_export_button", disabled=disable_export_button):
            if not selected_attrs_internal:
                st.warning("Please select at least one attribute to export.")
            elif not GPU_AVAILABLE:
                 st.error("GPU is required for the export process but is not available.")
            else:
                # Store final selections in session state before proceeding
                st.session_state.export_attributes = selected_attrs_internal
                st.session_state.export_grouping = grouping_type
                st.session_state.export_batch_step = batch_step
                # batch_size is already stored above if GPU_AVAILABLE

                st.session_state.current_step = "export_process"
                st.session_state.export_in_progress = True # Flag to indicate export started
                logging.info(f"Starting export process for attributes: {selected_attrs_internal}, grouping by {grouping_type}, batch step {batch_step}.")
                st.rerun()

# == STEP 4: View Results / Perform Export ==
elif st.session_state.current_step == "view_results":
    st.header("Step 4: View Analysis Results")

    if not st.session_state.analysis_complete or not st.session_state.loaded_trace_data or not st.session_state.calculated_descriptors:
        st.warning("Analysis is not yet complete or data is missing. Please go back to Step 3.")
        if st.button("Back to Mode Selection"):
             st.session_state.current_step = "select_mode"
             # Clear potentially incomplete results
             st.session_state.loaded_trace_data = []
             st.session_state.calculated_descriptors = []
             st.session_state.analysis_complete = False
             st.rerun()
    else:
        st.success(f"Displaying results for {len(st.session_state.loaded_trace_data)} traces.")
        # Add button to go back

        # --- Output Selection ---
        st.markdown("---")
        st.subheader("Select Outputs to Display")
        # Determine available outputs based on mode
        # Determine available outputs based on mode
        # Determine available outputs based on mode
        if st.session_state.selection_mode == "By well markers":
            if st.session_state.plot_mode_wells == 1: # Individual plots
                available_outputs = AVAILABLE_OUTPUTS_SINGLE
            else: # Comparative plots
                available_outputs = AVAILABLE_OUTPUTS_MULTI
        elif st.session_state.selection_mode in ["Single inline (all crosslines)", "Single crossline (all inlines)", "By Polyline File Import"]:
            available_outputs = AVAILABLE_OUTPUTS_SECTION # Use the new list for section/polyline modes
        elif st.session_state.selection_mode == "By inline/crossline section (AOI)":
            # AOI mode currently goes to export, not plotting in view_results.
            # If plotting is added later for AOI in this step, AVAILABLE_OUTPUTS_SECTION is likely appropriate.
            # For now, assign AVAILABLE_OUTPUTS_SECTION as a placeholder if this step were used for AOI plotting.
            available_outputs = AVAILABLE_OUTPUTS_SECTION
        else:
            available_outputs = [] # Default empty

        # For single trace mode (option 1), always show all spectral descriptors
        is_multi_trace_mode = st.session_state.selection_mode != "Single Well (Single Trace)"
        if not is_multi_trace_mode:
            valid_outputs = available_outputs.copy()  # Use all available outputs for single trace mode
        else:
            # For multi-trace modes, determine valid outputs based on the specific mode
            if st.session_state.selection_mode in ["Single inline (all crosslines)", "Single crossline (all inlines)", "By Polyline File Import"]:
                valid_outputs = AVAILABLE_OUTPUTS_SECTION.copy() # Use section-specific outputs
            else:
                valid_outputs = AVAILABLE_OUTPUTS_MULTI.copy() # Use standard multi-trace outputs for others (e.g., comparative)
            # # Original filtering logic (commented out based on request):
            # # For multi-trace mode, check which outputs are actually present in the first descriptor (as a proxy)
            # valid_outputs = []
            # if st.session_state.calculated_descriptors:
            #     first_desc = st.session_state.calculated_descriptors[0]
            #     for out in available_outputs:
            #         internal_name = ATTR_NAME_MAP.get(out, out.lower().replace(" ", "_").replace("*", "")) # Simple mapping
            #         if out == "Input Signal": # Always available
            #             valid_outputs.append(out)
            #         elif out == "WOSS": # Check if components exist or if it was calculated
            #             if 'WOSS' in first_desc or all(k in first_desc for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
            #                  valid_outputs.append(out)
            #         elif internal_name in first_desc:
            #             valid_outputs.append(out)
            #         elif out == "Normalized dominant frequencies" and "norm_fdom" in first_desc: # Handle specific name variations
            #              valid_outputs.append(out)
            #         elif out == "Magnitude * Voice" and "mag_voice" in first_desc:
            #              valid_outputs.append(out)

        # Use previously selected outputs as default if available
        default_selection = st.session_state.get('selected_outputs', [])
        # Filter default selection to only include currently valid outputs
        default_selection = [out for out in default_selection if out in valid_outputs]
        if not default_selection and valid_outputs: # If previous selection invalid, default to first valid
             default_selection = [valid_outputs[0]]

        # Add a "Select All" button
        col1, col2 = st.columns([1, 3])
        with col1:
            if st.button("Select All", key="select_all_button"):
                default_selection = valid_outputs.copy()

        with col2:
            selected_outputs = st.multiselect(
                "Choose outputs:",
                options=valid_outputs,
                default=default_selection,
                key="output_selector"
            )

        st.session_state.selected_outputs = selected_outputs # Update session state

        if not selected_outputs:
            st.warning("Please select at least one output to display.")
        else:
            # --- Plotting Logic ---
            try:
                with st.spinner("Generating plots..."): # Spinner for plotting
                    # Mode 1: Well Markers - Individual Plots
                    if st.session_state.selection_mode == "By well markers" and st.session_state.plot_mode_wells == 1:
                        # Calculate global normalization values if needed
                        hfc_p95_global = st.session_state.plot_settings.get('hfc_p95', 1.0)
                        spec_decrease_p95_global = st.session_state.plot_settings.get('spec_decrease_p95', 1.0)

                        # Create a copy of plot settings and add the selected outputs
                        plot_settings_copy = dict(st.session_state.plot_settings)
                        plot_settings_copy['selected_outputs'] = st.session_state.selected_outputs
                        plot_settings_copy['hfc_p95'] = hfc_p95_global
                        plot_settings_copy['spec_decrease_p95'] = spec_decrease_p95_global

                        # For each trace, create an individual plot with all selected outputs
                        for i, (trace, descriptor) in enumerate(zip(st.session_state.loaded_trace_data, st.session_state.calculated_descriptors)):
                            if not descriptor:  # Skip if descriptor calculation failed for this trace
                                logging.warning(f"Skipping plot for trace {trace['trace_idx']} - missing descriptor.")
                                continue

                            # Calculate WOSS if needed
                            if 'WOSS' in st.session_state.selected_outputs and 'WOSS' not in descriptor:
                                descriptor['WOSS'] = calculate_woss(descriptor, plot_settings_copy)

                            # Create time vector for this trace
                            time_vector = np.arange(len(trace['trace_sample'])) * st.session_state.dt  # Time in seconds

                            # Get well marker name or trace index for the title
                            well_marker_name = trace.get('well_marker_name', f"Trace {trace['trace_idx']}")

                            # Use plot_spectral_descriptors to create individual plots for each trace
                            # --- Apply fixed axis limits and descriptor limits from Step 2 ---
                            # Ensure the keys used here match what plot_spectral_descriptors expects
                            plot_settings_copy['Spectral Decrease'] = [0, 1] # Fixed range [0, 1]
                            plot_settings_copy['HFC'] = [0, 2] # Fixed range [0, 2]

                            # Apply Mag*Voice Slope limits and colormap from Step 2
                            if 'mag_voice_slope_cmap_min' in st.session_state.plot_settings and 'mag_voice_slope_cmap_max' in st.session_state.plot_settings:
                                plot_settings_copy['Mag*Voice Slope'] = [
                                    st.session_state.plot_settings['mag_voice_slope_cmap_min'],
                                    st.session_state.plot_settings['mag_voice_slope_cmap_max']
                                ]
                                # Also set the key that might be used in visualization.py
                                plot_settings_copy['mag_voice_slope'] = [
                                    st.session_state.plot_settings['mag_voice_slope_cmap_min'],
                                    st.session_state.plot_settings['mag_voice_slope_cmap_max']
                                ]
                                # Also pass the individual min/max values directly
                                plot_settings_copy['mag_voice_slope_cmap_min'] = st.session_state.plot_settings['mag_voice_slope_cmap_min']
                                plot_settings_copy['mag_voice_slope_cmap_max'] = st.session_state.plot_settings['mag_voice_slope_cmap_max']
                                # Enhanced logging to debug the issue
                                logging.info(f"Setting Mag*Voice Slope limits in app.py: {plot_settings_copy['Mag*Voice Slope']}")
                                logging.info(f"Also setting mag_voice_slope limits: {plot_settings_copy['mag_voice_slope']}")
                                logging.info(f"Also setting individual min/max: {plot_settings_copy['mag_voice_slope_cmap_min']}, {plot_settings_copy['mag_voice_slope_cmap_max']}")
                            if 'mag_voice_slope_cmap_name' in st.session_state.plot_settings:
                                plot_settings_copy['mag_voice_slope_colormap'] = st.session_state.plot_settings['mag_voice_slope_cmap_name']
                                # Also set alternative key that might be used
                                plot_settings_copy['mag_voice_slope_cmap'] = st.session_state.plot_settings['mag_voice_slope_cmap_name']

                            # Apply Input Signal (Seismic Amplitude) limits
                            if 'input_signal_cmap_min' in st.session_state.plot_settings and 'input_signal_cmap_max' in st.session_state.plot_settings:
                                plot_settings_copy['Input Signal'] = [
                                    st.session_state.plot_settings['input_signal_cmap_min'],
                                    st.session_state.plot_settings['input_signal_cmap_max']
                                ]
                                # Also set lowercase key for visualization.py
                                plot_settings_copy['input_signal'] = [
                                    st.session_state.plot_settings['input_signal_cmap_min'],
                                    st.session_state.plot_settings['input_signal_cmap_max']
                                ]
                                logging.info(f"Setting Input Signal limits in app.py: {plot_settings_copy['Input Signal']}")
                            if 'input_signal_cmap_name' in st.session_state.plot_settings:
                                plot_settings_copy['input_signal_colormap'] = st.session_state.plot_settings['input_signal_cmap_name']

                            # Apply WOSS limits and colormap from Step 2
                            if 'woss_cmap_min' in st.session_state.plot_settings and 'woss_cmap_max' in st.session_state.plot_settings:
                                plot_settings_copy['WOSS'] = [
                                    st.session_state.plot_settings['woss_cmap_min'],
                                    st.session_state.plot_settings['woss_cmap_max']
                                ]
                                # Also set lowercase key for visualization.py
                                plot_settings_copy['woss'] = [
                                    st.session_state.plot_settings['woss_cmap_min'],
                                    st.session_state.plot_settings['woss_cmap_max']
                                ]
                                logging.info(f"Setting WOSS limits in app.py: {plot_settings_copy['WOSS']}")
                            if 'woss_cmap_name' in st.session_state.plot_settings:
                                plot_settings_copy['woss_colormap'] = st.session_state.plot_settings['woss_cmap_name']

                            # --- End descriptor limits ---

                            # Add marker value if available
                            marker_val = trace.get('marker_value')

                            # Add plot_mode_wells to plot_settings_copy
                            plot_settings_copy['plot_mode_wells'] = st.session_state.plot_mode_wells

                            fig = plot_spectral_descriptors(
                                trace_data=trace['trace_sample'],
                                time_vector=time_vector,
                                descriptors=descriptor,
                                plot_settings=plot_settings_copy, # Pass the prepared settings with plot_mode_wells
                                trace_idx=trace['trace_idx'],
                                well_marker_name=well_marker_name
                                # marker_value=marker_val # Removed: plot_spectral_descriptors doesn't accept this argument
                            )

                            # Display the plot
                            st.plotly_chart(fig, use_container_width=True)

                            # --- REMOVED Redundant Axis Updates ---
                            # The plot_spectral_descriptors function now handles setting
                            # the axis ranges based on the plot_settings_copy passed to it.
                            # No need to update layout or axes here again.
                            # --- End REMOVED Redundant Axis Updates ---

                    elif st.session_state.selection_mode == "By well markers" and st.session_state.plot_mode_wells == 2:
                        # Setup for comparative plots
                        num_traces = len(st.session_state.loaded_trace_data)
                        if num_traces == 0:
                            st.warning("No trace data loaded for comparative plot.")
                            st.stop() # Skip if no data

                        # Calculate time vector based on the first trace (assuming all have same length/dt)
                        first_trace_sample = st.session_state.loaded_trace_data[0]['trace_sample']
                        time_vector = np.arange(len(first_trace_sample)) * st.session_state.dt # Time in seconds

                        # Create a copy of plot settings
                        plot_settings_copy = dict(st.session_state.plot_settings)

                        # Add plot_mode_wells to plot_settings_copy
                        plot_settings_copy['plot_mode_wells'] = st.session_state.plot_mode_wells

                        # Log the available keys in plot_settings for debugging
                        logging.info(f"Available keys in plot_settings: {list(st.session_state.plot_settings.keys())}")

                        # Calculate global normalization values (similar to above)
                        hfc_p95_global = st.session_state.plot_settings.get('hfc_p95', 1.0)
                        spec_decrease_p95_global = st.session_state.plot_settings.get('spec_decrease_p95', 1.0)
                        # Recalculate if needed (less efficient)
                        if hfc_p95_global == 1.0 and len(st.session_state.calculated_descriptors) > 0:
                            try:
                                all_hfc = np.concatenate([d['hfc'] for d in st.session_state.calculated_descriptors if d and 'hfc' in d and d['hfc'].size > 0])
                                if all_hfc.size > 0: hfc_p95_global = np.percentile(all_hfc, 95)
                            except Exception: pass
                        if spec_decrease_p95_global == 1.0 and len(st.session_state.calculated_descriptors) > 0:
                            try:
                                all_spec_decrease = np.concatenate([d['spec_decrease'] for d in st.session_state.calculated_descriptors if d and 'spec_decrease' in d and d['spec_decrease'].size > 0])
                                if all_spec_decrease.size > 0: spec_decrease_p95_global = np.percentile(all_spec_decrease, 95)
                            except Exception: pass

                        # Ensure Spectrogram settings are in plot_settings_copy for comparative plots
                        # Magnitude Spectrogram
                        if 'magnitude_spectrogram_cmap_min' in st.session_state.plot_settings and 'magnitude_spectrogram_cmap_max' in st.session_state.plot_settings:
                            plot_settings_copy['Magnitude Spectrogram'] = [
                                st.session_state.plot_settings['magnitude_spectrogram_cmap_min'],
                                st.session_state.plot_settings['magnitude_spectrogram_cmap_max']
                            ]
                        if 'magnitude_spectrogram_cmap_name' in st.session_state.plot_settings:
                            plot_settings_copy['magnitude_spectrogram_colormap'] = st.session_state.plot_settings['magnitude_spectrogram_cmap_name']

                        # Magnitude * Voice (using the double underscore key from Step 2)
                        if 'magnitude__voice_cmap_min' in st.session_state.plot_settings and 'magnitude__voice_cmap_max' in st.session_state.plot_settings:
                            plot_settings_copy['Magnitude * Voice'] = [
                                st.session_state.plot_settings['magnitude__voice_cmap_min'],
                                st.session_state.plot_settings['magnitude__voice_cmap_max']
                            ]
                        if 'magnitude__voice_cmap_name' in st.session_state.plot_settings:
                            plot_settings_copy['magnitude_voice_colormap'] = st.session_state.plot_settings['magnitude__voice_cmap_name'] # Key used by plotting functions

                        # Loop through each selected output to create a separate figure
                        for output in st.session_state.selected_outputs:
                            # Calculate global min/max for Magnitude Spectrogram and Magnitude * Voice if needed
                            global_vmin = None
                            global_vmax = None
                            if output in ["Magnitude Spectrogram", "Time-Frequency Magnitude Spectrogram", "Magnitude * Voice", "Magnitude*Voice"]:
                                # First check if we have user-defined min/max values from Step 2
                                if (output == "Magnitude Spectrogram" or output == "Time-Frequency Magnitude Spectrogram") and 'magnitude_spectrogram_cmap_min' in st.session_state.plot_settings and 'magnitude_spectrogram_cmap_max' in st.session_state.plot_settings:
                                    global_vmin = st.session_state.plot_settings['magnitude_spectrogram_cmap_min']
                                    global_vmax = st.session_state.plot_settings['magnitude_spectrogram_cmap_max']
                                    logging.info(f"Using user-defined limits for {output}: vmin={global_vmin}, vmax={global_vmax}")
                                elif (output == "Magnitude * Voice" or output == "Magnitude*Voice") and 'magnitude__voice_cmap_min' in st.session_state.plot_settings and 'magnitude__voice_cmap_max' in st.session_state.plot_settings:
                                    global_vmin = st.session_state.plot_settings['magnitude__voice_cmap_min']
                                    global_vmax = st.session_state.plot_settings['magnitude__voice_cmap_max']
                                    logging.info(f"Using user-defined limits for {output}: vmin={global_vmin}, vmax={global_vmax}")
                                else:
                                    # Calculate global min/max from the data
                                    global_vmin = np.inf
                                    global_vmax = -np.inf
                                    descriptor_key = "mag" if (output == "Magnitude Spectrogram" or output == "Time-Frequency Magnitude Spectrogram") else "mag_voice"
                                    for descriptor in st.session_state.calculated_descriptors:
                                        if descriptor and descriptor_key in descriptor and isinstance(descriptor[descriptor_key], np.ndarray):
                                            data_array = descriptor[descriptor_key]
                                            if data_array.size > 0:
                                                # Use percentiles instead of min/max to avoid outliers
                                                if data_array.size > 100:  # Only use percentiles for larger arrays
                                                    p_low = np.nanpercentile(data_array, 1)  # 1st percentile
                                                    p_high = np.nanpercentile(data_array, 99)  # 99th percentile
                                                    global_vmin = min(global_vmin, p_low)
                                                    global_vmax = max(global_vmax, p_high)
                                                else:
                                                    global_vmin = min(global_vmin, np.nanmin(data_array))
                                                    global_vmax = max(global_vmax, np.nanmax(data_array))

                                    # Handle case where no valid data was found
                                    if global_vmin == np.inf:
                                        global_vmin = 0
                                    if global_vmax == -np.inf:
                                        global_vmax = 1 # Default range

                                    # For Magnitude * Voice, ensure symmetric range around zero if appropriate
                                    if (output == "Magnitude * Voice" or output == "Magnitude*Voice") and global_vmin < 0 and global_vmax > 0:
                                        abs_max = max(abs(global_vmin), abs(global_vmax))
                                        global_vmin = -abs_max
                                        global_vmax = abs_max

                                logging.info(f"Calculated global limits for {output}: vmin={global_vmin}, vmax={global_vmax}")


                            # Create subplot figure for this output type
                            trace_names = [trace.get('well_marker_name', f"Trace {trace['trace_idx']}") for trace in st.session_state.loaded_trace_data]
                            fig = make_subplots(rows=1, cols=num_traces, subplot_titles=trace_names, shared_yaxes=True)

                            # Use the same height (850) as in option 1 (Individual Plots)
                            # Calculate subplot width based on number of traces
                            subplot_width = 300  # Base width per subplot
                            if output in ["Magnitude Spectrogram", "Time-Frequency Magnitude Spectrogram", "Magnitude * Voice", "Magnitude*Voice"]:
                                subplot_width = 400  # Wider for heatmaps

                            # Match the layout style from option 1
                            # For spectrograms with shared colorbar, add more right margin
                            # Increase right margin to accommodate shared colorbars
                            right_margin = 300 if output in ["Magnitude Spectrogram", "Time-Frequency Magnitude Spectrogram", "Magnitude * Voice", "Magnitude*Voice"] else 100

                            fig.update_layout(
                                title_text=f"{output} for Selected Well Markers",
                                height=850,  # Set to 850 to match option 1
                                width=max(800, subplot_width * num_traces),
                                showlegend=output == "Normalized dominant frequencies",  # Show legend only for multi-line plots
                                margin=dict(r=right_margin),  # Add right margin for colorbars
                                # Ensure enough space between subplots for shared colorbars
                                plot_bgcolor='white',
                                paper_bgcolor='white'
                            )

                            # Loop through traces to add data to the current figure's subplots
                            for i, (trace, descriptor) in enumerate(zip(st.session_state.loaded_trace_data, st.session_state.calculated_descriptors)):
                                if not descriptor:  # Skip if descriptor calculation failed for this trace
                                    logging.warning(f"Skipping plot for trace {trace['trace_idx']} - missing descriptor.")
                                    continue

                                # Calculate WOSS if needed
                                if output == 'WOSS' and 'WOSS' not in descriptor:
                                    descriptor['WOSS'] = calculate_woss(descriptor, plot_settings_copy)

                                # Add the specific output to the subplot using the updated settings
                                # Pass global limits if calculated
                                # For spectrograms, use shared colorbar (only show on last subplot)
                                use_shared_colorbar = output in ["Magnitude Spectrogram", "Time-Frequency Magnitude Spectrogram", "Magnitude * Voice", "Magnitude*Voice"]
                                is_last_subplot = (i == len(st.session_state.loaded_trace_data) - 1)

                                add_output_to_subplot(
                                    fig=fig,
                                    output=output,
                                    trace_data_item=trace,
                                    descriptors=descriptor,
                                    time_vector=time_vector,
                                    row=1,
                                    col=i + 1,
                                    plot_settings=plot_settings_copy,
                                    hfc_p95=hfc_p95_global,
                                    spec_decrease_p95=spec_decrease_p95_global,
                                    global_vmin=global_vmin, # Pass global vmin
                                    global_vmax=global_vmax, # Pass global vmax
                                    use_shared_colorbar=use_shared_colorbar, # Use shared colorbar for spectrograms
                                    is_last_subplot=is_last_subplot # Indicate if this is the last subplot
                                )

                            # Update y-axis title for the first subplot (shared axis)
                            fig.update_yaxes(title_text='Time (s)', row=1, col=1)

                            # Apply the same time limits (Y-axis) as in option 1
                            time_limits = plot_settings_copy.get('Time (Y-axis)', [0, len(time_vector) * st.session_state.dt])
                            fig.update_yaxes(range=time_limits[::-1], row=1, col=None) # Apply reversed time limits to all y-axes in the row

                            # --- Apply Descriptor Limits (X-axis) from Step 2 ---
                            # Derive the key base from the output name
                            key_base = output.lower().replace(' ', '_').replace('*', '').replace('/', '_')
                            cmap_min_key_state = f"{key_base}_cmap_min"
                            cmap_max_key_state = f"{key_base}_cmap_max"

                            # Get the min/max values from plot settings, provide defaults if not found
                            # Need access to descriptor_settings defined in Step 2 for defaults
                            # Re-define or pass descriptor_settings if necessary, using a simple fallback for now
                            default_limits = [-1, 1] # Simple fallback, ideally get from Step 2 context
                            if output == "HFC" or output == "Spectral Decrease": default_limits = [0, 1]
                            elif output == "Spectral Bandwidth" or output == "Spectral Rolloff": default_limits = [0, 50]
                            elif output == "WOSS": default_limits = [-3, 3]
                            # Add more specific defaults if needed

                            # Get min/max values from plot settings
                            min_val = plot_settings_copy.get(cmap_min_key_state, default_limits[0])
                            max_val = plot_settings_copy.get(cmap_max_key_state, default_limits[1])

                            # Set specific fixed ranges or use user settings
                            if output == "Spectral Decrease":
                                descriptor_range = [0, 1] # Fixed range [0, 1]
                            elif output == "HFC":
                                descriptor_range = [0, 2] # Fixed range [0, 2]
                            elif output in ["Magnitude Spectrogram", "Time-Frequency Magnitude Spectrogram", "Magnitude * Voice", "Magnitude*Voice"]:
                                # Use freq_min and freq_max from Step 2 for these plots
                                freq_min = plot_settings_copy.get('freq_min', 0.0)
                                freq_max = plot_settings_copy.get('freq_max', 125.0) # Default to 125 Hz if not set
                                descriptor_range = [freq_min, freq_max]
                            elif output == "Spectral Slope":
                                # Use specific spectral slope limits if defined in Step 2
                                slope_min_key = 'spectral_slope_cmap_min'
                                slope_max_key = 'spectral_slope_cmap_max'
                                if slope_min_key in plot_settings_copy and slope_max_key in plot_settings_copy:
                                    descriptor_range = [plot_settings_copy[slope_min_key], plot_settings_copy[slope_max_key]]
                                else:
                                    # Fallback to generic min/max or default if specific keys aren't found
                                    descriptor_range = [min_val, max_val]
                            elif output == "Mag*Voice Slope":
                                # Use specific MV Slope limits if defined in Step 2
                                mv_slope_min_key = 'mag_voice_slope_cmap_min'
                                mv_slope_max_key = 'mag_voice_slope_cmap_max'

                                # Enhanced logging to debug the issue
                                logging.info(f"Checking for Mag*Voice Slope limits in plot_settings_copy")
                                logging.info(f"mv_slope_min_key '{mv_slope_min_key}' exists: {mv_slope_min_key in plot_settings_copy}")
                                logging.info(f"mv_slope_max_key '{mv_slope_max_key}' exists: {mv_slope_max_key in plot_settings_copy}")

                                # Try multiple possible key formats for Mag*Voice Slope
                                if mv_slope_min_key in plot_settings_copy and mv_slope_max_key in plot_settings_copy:
                                    descriptor_range = [plot_settings_copy[mv_slope_min_key], plot_settings_copy[mv_slope_max_key]]
                                    logging.info(f"Setting Mag*Voice Slope limits in comparative plots: {descriptor_range}")
                                elif 'Mag*Voice Slope' in plot_settings_copy and isinstance(plot_settings_copy['Mag*Voice Slope'], list):
                                    descriptor_range = plot_settings_copy['Mag*Voice Slope']
                                    logging.info(f"Setting Mag*Voice Slope limits from 'Mag*Voice Slope' key: {descriptor_range}")
                                elif 'mag_voice_slope' in plot_settings_copy and isinstance(plot_settings_copy['mag_voice_slope'], list):
                                    descriptor_range = plot_settings_copy['mag_voice_slope']
                                    logging.info(f"Setting Mag*Voice Slope limits from 'mag_voice_slope' key: {descriptor_range}")
                                else:
                                    # Calculate p5 and p95 from the data if available
                                    try:
                                        all_mv_slopes = []
                                        for desc in st.session_state.calculated_descriptors:
                                            if desc and 'mag_voice_slope' in desc and desc['mag_voice_slope'].size > 0:
                                                all_mv_slopes.append(desc['mag_voice_slope'])

                                        if all_mv_slopes:
                                            all_mv_slopes_array = np.concatenate(all_mv_slopes)
                                            p5 = np.percentile(all_mv_slopes_array, 5)
                                            p95 = np.percentile(all_mv_slopes_array, 95)
                                            # Use symmetric limits based on the larger absolute value
                                            abs_max = max(abs(p5), abs(p95))
                                            descriptor_range = [-abs_max, abs_max]
                                            logging.info(f"Setting Mag*Voice Slope limits from p5/p95 of data: {descriptor_range}")
                                        else:
                                            # Fallback to generic min/max or default if no data available
                                            descriptor_range = [min_val, max_val]
                                            logging.info(f"Using fallback Mag*Voice Slope limits (no data available): {descriptor_range}")
                                    except Exception as e:
                                        # Final fallback to reasonable defaults
                                        descriptor_range = [-10, 10]
                                        logging.info(f"Using default Mag*Voice Slope limits due to error: {e}")
                            elif output == "Normalized Dominant Frequency" or output == "Normalized dominant frequencies":
                                freq_min = plot_settings_copy.get('freq_min', 0.0)
                                freq_max = plot_settings_copy.get('freq_max', 125.0)
                                descriptor_range = [freq_min, freq_max]
                            else:
                                # Use user-defined min and max from the generic keys
                                descriptor_range = [min_val, max_val]

                            # Apply the descriptor limits to all x-axes in the row
                            fig.update_xaxes(range=descriptor_range, row=1, col=None)
                            # --- End Apply Descriptor Limits ---

                            st.plotly_chart(fig, use_container_width=True) # Use container width for comparative

                    # Modes 2, 3, 4 (Plot), 5: Seismic Section Plot
                    else:
                         # Calculate WOSS for all descriptors if needed
                         if 'WOSS' in st.session_state.selected_outputs:
                             for desc in st.session_state.calculated_descriptors:
                                 if 'WOSS' not in desc and desc: # Check desc is not empty
                                     desc['WOSS'] = calculate_woss(desc, st.session_state.plot_settings)

                         # Use our new multi-trace section visualization function
                         # Create time vector
                         time_vector = np.arange(len(st.session_state.loaded_trace_data[0]['trace_sample'])) * st.session_state.dt

                         # Extract trace data
                         trace_data_list = [trace['trace_sample'] for trace in st.session_state.loaded_trace_data]

                         # Calculate global normalization values if not already done
                         if 'hfc_p95' not in st.session_state.plot_settings and len(st.session_state.calculated_descriptors) > 0:
                             try:
                                 all_hfc = np.concatenate([d['hfc'] for d in st.session_state.calculated_descriptors if d and 'hfc' in d and d['hfc'].size > 0])
                                 if all_hfc.size > 0:
                                     hfc_p95_global = np.percentile(all_hfc, 95)
                                 else:
                                     hfc_p95_global = 1.0
                             except Exception:
                                 hfc_p95_global = 1.0
                         else:
                             hfc_p95_global = st.session_state.plot_settings.get('hfc_p95', 1.0)

                         if 'spec_decrease_p95' not in st.session_state.plot_settings and len(st.session_state.calculated_descriptors) > 0:
                             try:
                                 all_spec_decrease = np.concatenate([d['spec_decrease'] for d in st.session_state.calculated_descriptors if d and 'spec_decrease' in d and d['spec_decrease'].size > 0])
                                 if all_spec_decrease.size > 0:
                                     spec_decrease_p95_global = np.percentile(all_spec_decrease, 95)
                                 else:
                                     spec_decrease_p95_global = 1.0
                             except Exception:
                                 spec_decrease_p95_global = 1.0
                         else:
                             spec_decrease_p95_global = st.session_state.plot_settings.get('spec_decrease_p95', 1.0)
                         # Loop through each selected output and create a section plot
                         for output in st.session_state.selected_outputs:
                             # Create title based on selection mode
                             title = f"{output} Section"
                             if st.session_state.selection_mode == "By inline/crossline section (AOI)":
                                 title += " - Full AOI"
                             elif st.session_state.selection_mode == "Single inline (all crosslines)":
                                 title += f" - Inline {st.session_state.selected_inline}"
                             elif st.session_state.selection_mode == "Single crossline (all inlines)":
                                 title += f" - Crossline {st.session_state.selected_crossline}"
                             elif st.session_state.selection_mode == "By Polyline File Import":
                                 title += " - Polyline Section"

                             # Pass global normalization values through plot_settings
                             plot_settings_copy = st.session_state.plot_settings.copy()
                             plot_settings_copy['hfc_p95'] = hfc_p95_global
                             plot_settings_copy['spec_decrease_p95'] = spec_decrease_p95_global

                             # Map the descriptor limits from step 2 to the visualization
                             # Input Signal
                             if 'input_signal_cmap_min' in st.session_state.plot_settings and 'input_signal_cmap_max' in st.session_state.plot_settings:
                                 plot_settings_copy['Input Signal'] = [
                                     st.session_state.plot_settings['input_signal_cmap_min'],
                                     st.session_state.plot_settings['input_signal_cmap_max']
                                 ]
                             if 'input_signal_cmap_name' in st.session_state.plot_settings:
                                 plot_settings_copy['input_signal_colormap'] = st.session_state.plot_settings['input_signal_cmap_name']


                             # WOSS
                             if 'woss_cmap_min' in st.session_state.plot_settings and 'woss_cmap_max' in st.session_state.plot_settings:
                                 plot_settings_copy['WOSS'] = [
                                     st.session_state.plot_settings['woss_cmap_min'],
                                     st.session_state.plot_settings['woss_cmap_max']
                                 ]
                             if 'woss_cmap_name' in st.session_state.plot_settings:
                                 plot_settings_copy['woss_colormap'] = st.session_state.plot_settings['woss_cmap_name']

                             # Magnitude Spectrogram
                             if 'magnitude_spectrogram_cmap_min' in st.session_state.plot_settings and 'magnitude_spectrogram_cmap_max' in st.session_state.plot_settings:
                                 plot_settings_copy['Magnitude Spectrogram'] = [
                                     st.session_state.plot_settings['magnitude_spectrogram_cmap_min'],
                                     st.session_state.plot_settings['magnitude_spectrogram_cmap_max']
                                 ]
                             if 'magnitude_spectrogram_cmap_name' in st.session_state.plot_settings:
                                 plot_settings_copy['magnitude_spectrogram_colormap'] = st.session_state.plot_settings['magnitude_spectrogram_cmap_name']


                             # Magnitude * Voice
                             # Use the correct keys from Step 2 (_cmap_min, _cmap_max, _cmap_name)
                             if 'magnitude__voice_cmap_min' in st.session_state.plot_settings and 'magnitude__voice_cmap_max' in st.session_state.plot_settings:
                                 plot_settings_copy['Magnitude * Voice'] = [
                                     st.session_state.plot_settings['magnitude__voice_cmap_min'],
                                     st.session_state.plot_settings['magnitude__voice_cmap_max']
                                 ]
                             if 'magnitude__voice_cmap_name' in st.session_state.plot_settings:
                                 plot_settings_copy['magnitude_voice_colormap'] = st.session_state.plot_settings['magnitude__voice_cmap_name']


                             # Spectral Slope
                             if 'spectral_slope_cmap_min' in st.session_state.plot_settings and 'spectral_slope_cmap_max' in st.session_state.plot_settings:
                                 plot_settings_copy['Spectral Slope'] = [
                                     st.session_state.plot_settings['spectral_slope_cmap_min'],
                                     st.session_state.plot_settings['spectral_slope_cmap_max']
                                 ]
                                 # Also set the key that might be used in visualization.py
                                 plot_settings_copy['spectral_slope'] = [
                                     st.session_state.plot_settings['spectral_slope_cmap_min'],
                                     st.session_state.plot_settings['spectral_slope_cmap_max']
                                 ]
                                 # Also pass the individual min/max values directly
                                 plot_settings_copy['spectral_slope_cmap_min'] = st.session_state.plot_settings['spectral_slope_cmap_min']
                                 plot_settings_copy['spectral_slope_cmap_max'] = st.session_state.plot_settings['spectral_slope_cmap_max']
                                 # Enhanced logging to debug the issue
                                 logging.info(f"Setting Spectral Slope limits in app.py: {plot_settings_copy['Spectral Slope']}")
                                 logging.info(f"Also setting spectral_slope limits: {plot_settings_copy['spectral_slope']}")
                                 logging.info(f"Also setting individual min/max: {plot_settings_copy['spectral_slope_cmap_min']}, {plot_settings_copy['spectral_slope_cmap_max']}")
                             if 'spectral_slope_cmap_name' in st.session_state.plot_settings:
                                 plot_settings_copy['spectral_slope_colormap'] = st.session_state.plot_settings['spectral_slope_cmap_name']


                             # Spectral Bandwidth
                             if 'spectral_bandwidth_cmap_min' in st.session_state.plot_settings and 'spectral_bandwidth_cmap_max' in st.session_state.plot_settings:
                                 plot_settings_copy['Spectral Bandwidth'] = [
                                     st.session_state.plot_settings['spectral_bandwidth_cmap_min'],
                                     st.session_state.plot_settings['spectral_bandwidth_cmap_max']
                                 ]
                             if 'spectral_bandwidth_cmap_name' in st.session_state.plot_settings:
                                 plot_settings_copy['spectral_bandwidth_colormap'] = st.session_state.plot_settings['spectral_bandwidth_cmap_name']


                             # Spectral Rolloff
                             if 'spectral_rolloff_cmap_min' in st.session_state.plot_settings and 'spectral_rolloff_cmap_max' in st.session_state.plot_settings:
                                 plot_settings_copy['Spectral Rolloff'] = [
                                     st.session_state.plot_settings['spectral_rolloff_cmap_min'],
                                     st.session_state.plot_settings['spectral_rolloff_cmap_max']
                                 ]
                             if 'spectral_rolloff_cmap_name' in st.session_state.plot_settings:
                                 plot_settings_copy['spectral_rolloff_colormap'] = st.session_state.plot_settings['spectral_rolloff_cmap_name']


                             # Mag*Voice Slope
                             # First check if the specific cmap min/max keys exist
                             if 'mag_voice_slope_cmap_min' in st.session_state.plot_settings and 'mag_voice_slope_cmap_max' in st.session_state.plot_settings:
                                 # Set all possible key formats to ensure compatibility
                                 plot_settings_copy['Mag*Voice Slope'] = [
                                     st.session_state.plot_settings['mag_voice_slope_cmap_min'],
                                     st.session_state.plot_settings['mag_voice_slope_cmap_max']
                                 ]
                                 plot_settings_copy['mag_voice_slope'] = [
                                     st.session_state.plot_settings['mag_voice_slope_cmap_min'],
                                     st.session_state.plot_settings['mag_voice_slope_cmap_max']
                                 ]
                                 # Also pass the individual min/max values directly
                                 plot_settings_copy['mag_voice_slope_cmap_min'] = st.session_state.plot_settings['mag_voice_slope_cmap_min']
                                 plot_settings_copy['mag_voice_slope_cmap_max'] = st.session_state.plot_settings['mag_voice_slope_cmap_max']
                                 # Enhanced logging to debug the issue
                                 logging.info(f"Setting Mag*Voice Slope limits in app.py (comparative): {plot_settings_copy['Mag*Voice Slope']}")
                                 logging.info(f"Also setting mag_voice_slope limits (comparative): {plot_settings_copy['mag_voice_slope']}")
                                 logging.info(f"Also setting individual min/max (comparative): {plot_settings_copy['mag_voice_slope_cmap_min']}, {plot_settings_copy['mag_voice_slope_cmap_max']}")
                             else:
                                 # If specific keys don't exist, try to calculate from the data
                                 try:
                                     all_mv_slopes = []
                                     for desc in st.session_state.calculated_descriptors:
                                         if desc and 'mag_voice_slope' in desc and desc['mag_voice_slope'].size > 0:
                                             all_mv_slopes.append(desc['mag_voice_slope'])

                                     if all_mv_slopes:
                                         all_mv_slopes_array = np.concatenate(all_mv_slopes)
                                         p5 = np.percentile(all_mv_slopes_array, 5)
                                         p95 = np.percentile(all_mv_slopes_array, 95)
                                         # Use symmetric limits based on the larger absolute value
                                         abs_max = max(abs(p5), abs(p95))
                                         mv_slope_min = -abs_max
                                         mv_slope_max = abs_max

                                         # Set all possible key formats
                                         plot_settings_copy['Mag*Voice Slope'] = [mv_slope_min, mv_slope_max]
                                         plot_settings_copy['mag_voice_slope'] = [mv_slope_min, mv_slope_max]
                                         plot_settings_copy['mag_voice_slope_cmap_min'] = mv_slope_min
                                         plot_settings_copy['mag_voice_slope_cmap_max'] = mv_slope_max

                                         logging.info(f"Setting Mag*Voice Slope limits from p5/p95 of data: {[mv_slope_min, mv_slope_max]}")
                                     else:
                                         # Use reasonable defaults if no data available
                                         plot_settings_copy['Mag*Voice Slope'] = [-10, 10]
                                         plot_settings_copy['mag_voice_slope'] = [-10, 10]
                                         plot_settings_copy['mag_voice_slope_cmap_min'] = -10
                                         plot_settings_copy['mag_voice_slope_cmap_max'] = 10
                                         logging.info(f"Setting default Mag*Voice Slope limits (no data available): {[-10, 10]}")
                                 except Exception as e:
                                     # Final fallback to reasonable defaults
                                     plot_settings_copy['Mag*Voice Slope'] = [-10, 10]
                                     plot_settings_copy['mag_voice_slope'] = [-10, 10]
                                     plot_settings_copy['mag_voice_slope_cmap_min'] = -10
                                     plot_settings_copy['mag_voice_slope_cmap_max'] = 10
                                     logging.info(f"Setting default Mag*Voice Slope limits due to error: {e}")

                             # Set colormap name if available
                             if 'mag_voice_slope_cmap_name' in st.session_state.plot_settings:
                                 plot_settings_copy['mag_voice_slope_colormap'] = st.session_state.plot_settings['mag_voice_slope_cmap_name']


                             # Spectral Decrease
                             # Use colormap limits from step 2 for Spectral Decrease
                             if 'spectral_decrease_cmap_min' in st.session_state.plot_settings and 'spectral_decrease_cmap_max' in st.session_state.plot_settings:
                                 plot_settings_copy['Spectral Decrease'] = [
                                     st.session_state.plot_settings['spectral_decrease_cmap_min'],
                                     st.session_state.plot_settings['spectral_decrease_cmap_max']
                                 ]
                                 # Also set individual min/max values directly
                                 plot_settings_copy['spectral_decrease_cmap_min'] = st.session_state.plot_settings['spectral_decrease_cmap_min']
                                 plot_settings_copy['spectral_decrease_cmap_max'] = st.session_state.plot_settings['spectral_decrease_cmap_max']
                                 logging.info(f"Setting Spectral Decrease limits from step 2: {plot_settings_copy['Spectral Decrease']}")
                             else:
                                 # Fallback to default range if not set in step 2
                                 plot_settings_copy['Spectral Decrease'] = [0, 1]
                                 logging.info(f"Using default Spectral Decrease limits: [0, 1]")

                             if 'spectral_decrease_cmap_name' in st.session_state.plot_settings:
                                 plot_settings_copy['spectral_decrease_colormap'] = st.session_state.plot_settings['spectral_decrease_cmap_name']


                             # HFC
                             # Use colormap limits from step 2 for HFC
                             if 'hfc_cmap_min' in st.session_state.plot_settings and 'hfc_cmap_max' in st.session_state.plot_settings:
                                 plot_settings_copy['HFC'] = [
                                     st.session_state.plot_settings['hfc_cmap_min'],
                                     st.session_state.plot_settings['hfc_cmap_max']
                                 ]
                                 # Also set individual min/max values directly
                                 plot_settings_copy['hfc_cmap_min'] = st.session_state.plot_settings['hfc_cmap_min']
                                 plot_settings_copy['hfc_cmap_max'] = st.session_state.plot_settings['hfc_cmap_max']
                                 logging.info(f"Setting HFC limits from step 2: {plot_settings_copy['HFC']}")
                             else:
                                 # Fallback to default range if not set in step 2
                                 plot_settings_copy['HFC'] = [0, 2]
                                 logging.info(f"Using default HFC limits: [0, 2]")

                             if 'hfc_cmap_name' in st.session_state.plot_settings:
                                 plot_settings_copy['hfc_colormap'] = st.session_state.plot_settings['hfc_cmap_name']


                             # Normalized dominant frequencies
                             if 'normalized_dominant_frequency_cmap_min' in st.session_state.plot_settings and 'normalized_dominant_frequency_cmap_max' in st.session_state.plot_settings:
                                 plot_settings_copy['Normalized Dominant Frequency'] = [
                                     st.session_state.plot_settings['normalized_dominant_frequency_cmap_min'],
                                     st.session_state.plot_settings['normalized_dominant_frequency_cmap_max']
                                 ]
                             if 'normalized_dominant_frequency_cmap_name' in st.session_state.plot_settings:
                                 plot_settings_copy['normalized_dominant_frequency_colormap'] = st.session_state.plot_settings['normalized_dominant_frequency_cmap_name']


                             # Also map general colormap settings if needed by the function
                             # if 'colormap_name' in st.session_state.plot_settings: # Example, adjust if needed
                             #     plot_settings_copy['colormap'] = st.session_state.plot_settings['colormap_name']

                             # Determine x-axis values based on selection mode
                             x_axis_values = None
                             x_axis_title = "Trace Index"

                             if st.session_state.selection_mode == "Single inline (all crosslines)":
                                 # For single inline, use crossline numbers as x-axis
                                 x_axis_values = []
                                 for t in st.session_state.loaded_trace_data:
                                     idx_positions = np.where(st.session_state.header_loader.unique_indices == t['trace_idx'])[0]
                                     if len(idx_positions) > 0:
                                         x_axis_values.append(st.session_state.header_loader.crosslines[idx_positions[0]])
                                     else:
                                         x_axis_values.append(0)  # Fallback value
                                 x_axis_title = "Crossline"
                             elif st.session_state.selection_mode == "Single crossline (all inlines)":
                                 # For single crossline, use inline numbers as x-axis
                                 x_axis_values = []
                                 for t in st.session_state.loaded_trace_data:
                                     idx_positions = np.where(st.session_state.header_loader.unique_indices == t['trace_idx'])[0]
                                     if len(idx_positions) > 0:
                                         x_axis_values.append(st.session_state.header_loader.inlines[idx_positions[0]])
                                     else:
                                         x_axis_values.append(0)  # Fallback value
                                 x_axis_title = "Inline"
                             elif st.session_state.selection_mode == "By Polyline File Import":
                                 # For polyline, use trace indices as x-axis
                                 x_axis_values = np.arange(len(st.session_state.loaded_trace_data))
                                 x_axis_title = "Trace Index"

                             # Add detailed logging before calling plot_multi_trace_section
                             logging.info(f"Calling plot_multi_trace_section for {output}")
                             logging.info(f"Number of traces in trace_data_list: {len(trace_data_list)}")
                             logging.info(f"Time vector length: {len(time_vector)}")
                             logging.info(f"Number of descriptors: {len(st.session_state.calculated_descriptors)}")
                             logging.info(f"X-axis values: {x_axis_values[:5] if x_axis_values is not None and len(x_axis_values) > 0 else 'None'}")

                             # Check if trace_data_list and calculated_descriptors have the same length
                             if len(trace_data_list) != len(st.session_state.calculated_descriptors):
                                 logging.warning(f"Mismatch between trace_data_list length ({len(trace_data_list)}) and calculated_descriptors length ({len(st.session_state.calculated_descriptors)})")

                             # Check if any descriptors are None or empty
                             for i, desc in enumerate(st.session_state.calculated_descriptors):
                                 if desc is None or len(desc) == 0:
                                     logging.warning(f"Descriptor {i} is None or empty")

                             fig_section = plot_multi_trace_section(
                                 trace_data_list,
                                 time_vector,
                                 st.session_state.calculated_descriptors,
                                 plot_settings_copy,
                                 output_type=output,
                                 title=title,
                                 x_axis_values=x_axis_values,
                                 x_axis_title=x_axis_title
                             )

                             if fig_section:
                                 logging.info(f"Successfully generated {output} section plot, displaying it")
                                 st.plotly_chart(fig_section, use_container_width=True)
                             else:
                                 logging.warning(f"plot_multi_trace_section returned None for {output}")
                                 st.warning(f"Could not generate {output} section plot.")

            except Exception as e:
                st.error(f"An error occurred during plot generation: {e}")
                logging.error(f"Plot generation failed: {e}", exc_info=True)
# Button to go back after viewing results
            if st.button("Back to Mode Selection", key="back_to_mode_after_plots"):
                 st.session_state.current_step = "select_mode"
                 # Keep loaded data and settings, just allow re-selection/re-analysis
                 st.session_state.analysis_complete = False # Mark as incomplete to trigger re-analysis
                 st.rerun()

# == STEP 4.5: Export Process (AOI Mode) ==
elif st.session_state.current_step == "export_process":
    st.header("Step 4.5: Processing AOI Export")
    # Check if export was actually started
    if not st.session_state.get('export_in_progress', False):
         st.warning("Export process not initiated correctly. Please configure export first.")
         if st.button("Go to Export Configuration"):
              st.session_state.current_step = "configure_export"
              st.rerun()
         # Add a safety break
         st.stop()

    # Use context manager for temporary directories
    with tempfile.TemporaryDirectory() as temp_batch_dir, \
         tempfile.TemporaryDirectory() as temp_export_dir:

        st.info(f"Using temporary batch directory: `{temp_batch_dir}`")
        st.info(f"Using temporary export directory: `{temp_export_dir}`")
        status_text_export = st.empty()
        batch_file_log_path = os.path.join(temp_export_dir, "batch_creation_log.txt") # Log batch creation details

        try:
            # Get necessary info from session state
            selected_indices = st.session_state.selected_indices
            selected_attrs = st.session_state.export_attributes
            grouping_type = st.session_state.export_grouping
            batch_step = st.session_state.export_batch_step
            headers_df = st.session_state.header_loader.headers_df
            # Use the actual path (could be temp) from the loader object
            # segy_path = st.session_state.header_loader.temp_file_path # Old way
            segy_path = st.session_state.header_loader.source_file_path # Use the stored path

            # Log information about the selected AOI
            logging.info(f"Processing export with {len(selected_indices)} traces selected from AOI")

            # --- Export Setup ---
            if grouping_type not in headers_df.columns:
                st.error(f"Grouping column '{grouping_type}' not found in headers. Aborting export.")
                raise ValueError(f"Grouping column '{grouping_type}' not found.")

            unique_groups = sorted(headers_df[grouping_type].unique())
            if not unique_groups:
                st.error(f"No unique groups found for '{grouping_type}'. Aborting export.")
                raise ValueError(f"No unique groups found for '{grouping_type}'.")

            if batch_step <= 0:
                st.warning("Batch step must be positive. Setting to 1.")
                batch_step = 1

            # Process groups in batches
            num_groups_processed = 0
            overall_progress = st.progress(0, text=f"Starting export process...")
            all_batch_descriptors_list = [] # Store descriptors if needed later, maybe not necessary

            # Initialize dictionary to store batch filenames per attribute
            all_batch_files = {attr: [] for attr in selected_attrs}

            # Open batch log file
            with open(batch_file_log_path, "w") as batch_file_log:
                batch_file_log.write("--- Batch File Creation Log ---\n\n")

                for i in range(0, len(unique_groups), batch_step):
                    # --- Batch Setup ---
                    batch_start_index = i
                    batch_end_index = min(i + batch_step, len(unique_groups))
                    current_batch_groups = unique_groups[batch_start_index:batch_end_index]
                    batch_start_group = current_batch_groups[0]
                    batch_end_group = current_batch_groups[-1]
                    # --- End Batch Setup ---

                    status_text_export.text(f"Processing batch: {grouping_type.capitalize()}s {batch_start_group} to {batch_end_group}...")
                    batch_file_log.write(f"Processing Batch: {grouping_type.capitalize()}s {batch_start_group}-{batch_end_group}\n")

                    # Find trace indices for this batch, filtered by selected AOI
                    # Use the approach from the reference implementation
                    batch_indices = []
                    for group_value in current_batch_groups:
                        group_mask = headers_df[grouping_type] == group_value
                        aoi_mask = headers_df['trace_idx'].isin(selected_indices)
                        combined_mask = group_mask & aoi_mask
                        group_indices = headers_df.loc[combined_mask, 'trace_idx'].tolist()
                        batch_indices.extend(group_indices)
                    batch_file_log.write(f"  Indices: {len(batch_indices)} traces (filtered by AOI)\n")


                    if not batch_indices:
                        batch_file_log.write("  - Skipping batch (no traces found).\n")
                        continue

                    # Load trace data for the batch
                    batch_trace_data = []
                    with st.spinner(f"Loading {len(batch_indices)} traces for batch {batch_start_group}-{batch_end_group}..."):
                        try:
                            # Determine max length for padding
                            max_len = 0
                            for trace_idx in batch_indices:
                                trace = load_trace_sample(segy_path, trace_idx)
                                batch_trace_data.append({'trace_sample': trace, 'trace_idx': trace_idx})
                                max_len = max(max_len, len(trace))

                            # Pad traces if necessary (GPU function might require uniform length)
                            # --- Padding Logic ---
                            if (max_len > 0):
                                for item in batch_trace_data:
                                    if len(item['trace_sample']) < max_len:
                                        pad_width = max_len - len(item['trace_sample'])
                                        item['trace_sample'] = np.pad(item['trace_sample'], (0, pad_width), 'constant')
                            # --- End Padding Logic ---

                        except FileNotFoundError:
                            st.error(f"Error: SEG-Y file not found at {segy_path} during batch loading. Aborting export.")
                            logging.error(f"SEG-Y file not found at {segy_path} during export batch loading.")
                            raise # Re-raise to stop the export
                        except Exception as load_e:
                            st.error(f"Error loading traces for batch {batch_start_group}-{batch_end_group}: {load_e}")
                            logging.error(f"Trace loading failed for batch {batch_start_group}-{batch_end_group}: {load_e}", exc_info=True)
                            batch_file_log.write(f"  - ERROR loading traces: {load_e}. Skipping batch.\n")
                            continue # Skip this batch

                    if not batch_trace_data:
                        batch_file_log.write("  - Skipping batch (failed to load trace data).\n")
                        continue

                    # Calculate descriptors for the batch
                    batch_descriptors = [] # List of dictionaries, one per trace
                    with st.spinner(f"Calculating descriptors for batch {batch_start_group}-{batch_end_group}..."):
                        # --- Descriptor Settings Setup ---
                        descriptor_settings = {
                            'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                            'shape': st.session_state.plot_settings.get('shape', 0.35),
                            'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                            'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                            'b1': st.session_state.plot_settings.get('b1', 5.0),
                            'b2': st.session_state.plot_settings.get('b2', 40.0),
                            'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                            'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80)
                        }
                        # --- End Descriptor Settings Setup ---
                        try:
                            if GPU_AVAILABLE:
                                if not batch_trace_data:
                                     raise ValueError("No trace data available for GPU processing.")
                                # Stack traces for GPU processing
                                arr = np.stack([t['trace_sample'] for t in batch_trace_data]).astype(np.float32)
                                fmax_calc = max_len // 2 if max_len > 0 else 250 # Default fmax if max_len is 0

                                descriptors_all_dict = dlogst_spec_descriptor_gpu_2d_chunked(
                                    arr, st.session_state.dt, fmax=fmax_calc,
                                    batch_size=st.session_state.batch_size or 512,
                                    **descriptor_settings
                                )
                                # --- Unpack Descriptors ---
                                num_traces_in_batch = arr.shape[0]
                                batch_descriptors = [{} for _ in range(num_traces_in_batch)]
                                for key, value_array in descriptors_all_dict.items():
                                    if value_array.shape[0] == num_traces_in_batch:
                                        for trace_idx in range(num_traces_in_batch):
                                            batch_descriptors[trace_idx][key] = value_array[trace_idx]
                                    else:
                                         logging.warning(f"Shape mismatch for descriptor '{key}' in batch {batch_start_group}-{batch_end_group}. Expected {num_traces_in_batch} traces, got {value_array.shape[0]}. Skipping this descriptor for the batch.")
                                         batch_file_log.write(f"  - WARNING: Shape mismatch for descriptor '{key}'. Skipping.\n")
                                # --- End Unpack Descriptors ---

                                # Calculate WOSS if requested
                                if 'WOSS' in selected_attrs:
                                     hfc_p95 = st.session_state.plot_settings.get('hfc_p95', 1.0) # Use pre-calculated p95
                                     epsilon = st.session_state.plot_settings.get('epsilon', 1e-10)
                                     fdom_exponent = st.session_state.plot_settings.get('fdom_exponent', 2.0)
                                     for desc_idx, desc in enumerate(batch_descriptors):
                                         try:
                                             # Use calculate_woss helper, ensuring inputs are correct
                                             desc['WOSS'] = calculate_woss(desc, st.session_state.plot_settings)
                                         except Exception as woss_e:
                                             logging.warning(f"Could not calculate WOSS for trace index {batch_trace_data[desc_idx]['trace_idx']} in batch: {woss_e}")
                                             batch_file_log.write(f"  - WARNING: Failed WOSS calculation for trace index {batch_trace_data[desc_idx]['trace_idx']}: {woss_e}\n")
                                             # Assign default value if calculation fails
                                             sample_len = len(batch_trace_data[desc_idx]['trace_sample'])
                                             desc['WOSS'] = np.zeros(sample_len, dtype=np.float32)


                            else:
                                st.error("GPU required for efficient export. Aborting.")
                                logging.error("GPU required for export, but not available.")
                                raise RuntimeError("GPU required for export.")
                        except Exception as desc_e:
                            st.error(f"Error calculating descriptors for batch {batch_start_group}-{batch_end_group}: {desc_e}")
                            logging.error(f"Descriptor calculation failed for batch {batch_start_group}-{batch_end_group}: {desc_e}", exc_info=True)
                            batch_file_log.write(f"  - ERROR calculating descriptors: {desc_e}. Skipping batch.\n")
                            continue # Skip this batch

                    # Export each selected attribute for this batch
                    with st.spinner(f"Writing batch files for {grouping_type}s {batch_start_group}-{batch_end_group}..."):
                        try:
                            with segyio.open(segy_path, 'r', ignore_geometry=True) as src:
                                spec = segyio.spec()
                                spec.format = src.format # Use source format
                                spec.samples = range(max_len) if max_len > 0 else src.samples # Use padded length or original
                                spec.tracecount = len(batch_indices) # Set tracecount for the batch file

                                for attr in selected_attrs:
                                    display_name = REVERSE_ATTR_NAME_MAP.get(attr, attr)
                                    batch_output_filename = f"batch_{batch_start_group}_{batch_end_group}_{attr}.sgy"
                                    batch_output_path = os.path.join(temp_batch_dir, batch_output_filename)
                                    batch_file_log.write(f"  - Creating: {batch_output_filename}\n")
                                    # Add batch file to the tracking dictionary
                                    all_batch_files[attr].append(batch_output_path)

                                    with segyio.create(batch_output_path, spec) as dst:
                                        # --- Copy Headers and Write Trace Data ---
                                        for i, (trace_item, descriptor) in enumerate(zip(batch_trace_data, batch_descriptors)):
                                            original_trace_idx = trace_item['trace_idx']
                                            # Copy header from original file
                                            dst.header[i] = src.header[original_trace_idx]

                                            # Get the calculated attribute data
                                            trace_to_write = None
                                            if attr == 'data': # Handle original data export
                                                 trace_to_write = trace_item['trace_sample']
                                            elif descriptor and attr in descriptor:
                                                trace_to_write = descriptor[attr]
                                            else:
                                                batch_file_log.write(f"    - Warning: Attribute '{attr}' missing/invalid for trace index {original_trace_idx}. Writing zeros.\n")
                                                logging.warning(f"Attribute '{attr}' missing for trace index {original_trace_idx} in batch {batch_start_group}-{batch_end_group}.")

                                            # Ensure data is float32 and correct length, write zeros if missing/error
                                            if trace_to_write is not None:
                                                if len(trace_to_write) == max_len:
                                                    dst.trace[i] = trace_to_write.astype(np.float32)
                                                else:
                                                    batch_file_log.write(f"    - Warning: Length mismatch for '{attr}' on trace index {original_trace_idx} (expected {max_len}, got {len(trace_to_write)}). Writing zeros.\n")
                                                    logging.warning(f"Length mismatch for '{attr}' on trace index {original_trace_idx}. Expected {max_len}, got {len(trace_to_write)}.")
                                                    dst.trace[i] = np.zeros(max_len, dtype=np.float32)
                                            else:
                                                # Write zeros if attribute was missing
                                                dst.trace[i] = np.zeros(max_len, dtype=np.float32)
                                        # --- End Copy Headers and Write Trace Data ---

                        except Exception as write_e:
                            st.error(f"Error writing batch files for {batch_start_group}-{batch_end_group}: {write_e}")
                            logging.error(f"Batch file writing failed for batch {batch_start_group}-{batch_end_group}: {write_e}", exc_info=True)
                            batch_file_log.write(f"  - ERROR writing batch files: {write_e}. Skipping batch output.\n")
                            # Decide if to continue with next batch or abort
                            continue # Continue to next batch for now

                    # Update progress
                    num_groups_processed += len(current_batch_groups)
                    progress_percentage = num_groups_processed / len(unique_groups)
                    overall_progress.progress(progress_percentage, text=f"Processed {num_groups_processed}/{len(unique_groups)} {grouping_type} groups...")
                    # Clear memory (optional, Python's GC usually handles it)
                    del batch_trace_data, batch_descriptors, arr # If memory is tight

            status_text_export.text("Batch processing complete. Merging files...")
            overall_progress.progress(1.0, text="Merging batch files...") # Show merging progress

            # Merge batch files for each attribute
            final_merged_files_info = []
            merge_log_content = ""
            with st.spinner("Merging batch files for each attribute..."):
                for attr in selected_attrs:
                    # --- Merging Logic ---
                    display_name = REVERSE_ATTR_NAME_MAP.get(attr, attr)
                    final_output_filename = f"merged_{st.session_state.segy_file_info['name']}_{attr}.sgy"
                    final_output_path = os.path.join(temp_export_dir, final_output_filename)

                    # Use the explicit list of batch files from the tracking dictionary
                    batch_files = all_batch_files[attr]

                    # Filter out any files that might not exist (safety check)
                    batch_files = [f for f in batch_files if os.path.exists(f)]

                    logging.info(f"Merging {len(batch_files)} batch files for attribute {attr}")
                    logging.info(f"Outputting merged file to: {final_output_path}")
                    # --- End Merging Logic ---

                    # Call merge_segy_batch_files with only the required parameters
                    merge_log = merge_segy_batch_files(batch_files, final_output_path)
                    merge_log_content += f"Merge Log for {display_name}:\n{merge_log}\n\n"
                    # Store info needed for download (attribute name, final path in temp_export_dir)
                    if os.path.exists(final_output_path): # Check if merge was successful
                         final_merged_files_info.append({"attribute": display_name, "path": final_output_path})
                         logging.info(f"Successfully merged batch files for {attr} into {final_output_path}")
                    else:
                         logging.error(f"Merging failed for attribute {attr}. Output file not found: {final_output_path}")
                         st.error(f"Merging failed for attribute: {display_name}. See logs for details.")


            # Save the merge log
            merge_log_path = os.path.join(temp_export_dir, "merge_log.txt")
            with open(merge_log_path, "w") as f:
                f.write(merge_log_content)
                f.write("\n--- Batch Creation Log ---\n")
                # Append batch creation log content
                try:
                    with open(batch_file_log_path, "r") as bf_log:
                         f.write(bf_log.read())
                except Exception:
                     f.write("Error reading batch creation log.")

            logging.info(f"Combined merge and batch log saved to {merge_log_path}")
            # Add log file to download list only if it exists
            if os.path.exists(merge_log_path):
                 final_merged_files_info.append({"attribute": "Processing Log", "path": merge_log_path})
            else:
                 logging.warning("Merge log file was not created.")


            # Don't clean up temp_batch_dir here, it's handled by context manager

            status_text_export.success("Export and merge process complete!")
            overall_progress.empty()
            st.session_state.export_in_progress = False
            # Store results for download step - paths are inside temp_export_dir
            st.session_state.exported_files_info = final_merged_files_info
            # Need to copy files out of temp_export_dir or keep it alive until download
            # Let's copy to a more persistent temp dir for the download step
            persistent_export_dir = tempfile.mkdtemp(prefix="woss_export_")
            final_downloadable_files = []
            for file_info in final_merged_files_info:
                 try:
                     if os.path.exists(file_info['path']): # Check if file exists before copying
                         base_name = os.path.basename(file_info['path'])
                         new_path = os.path.join(persistent_export_dir, base_name)
                         shutil.copy2(file_info['path'], new_path)
                         final_downloadable_files.append({"attribute": file_info['attribute'], "path": new_path})
                     else:
                          st.warning(f"Skipping copy for non-existent file: {file_info['path']}")
                          logging.warning(f"File not found, skipping copy to persistent dir: {file_info['path']}")
                 except Exception as copy_e:
                      st.warning(f"Could not prepare file for download {os.path.basename(file_info.get('path','N/A'))} : {copy_e}")
                      logging.warning(f"Failed to copy {file_info['path']} to {persistent_export_dir}: {copy_e}")

            st.session_state.exported_files_info = final_downloadable_files # Update with new paths
            st.session_state.export_output_dir = persistent_export_dir # Store the persistent directory path
            st.session_state.current_step = "download_export"
            st.rerun()

        except FileNotFoundError as fnf_err:
             # Handle file not found during the main process (already logged likely)
             st.error(f"Export failed: Required file not found. {fnf_err}")
             logging.error(f"Export process failed due to FileNotFoundError: {fnf_err}", exc_info=True)
             st.session_state.export_in_progress = False
        except Exception as e:
            st.error(f"An error occurred during the export process: {e}")
            logging.error(f"Export process failed: {e}", exc_info=True)
            st.session_state.export_in_progress = False
            # Context managers will clean up temp_batch_dir and temp_export_dir
            if st.button("Back to Configuration"):
                 st.session_state.current_step = "configure_export"
                 st.rerun()


# == STEP 5: Download Exported Files ==
elif st.session_state.current_step == "download_export":
    st.header("Step 5: Download Exported Files")

    export_dir = st.session_state.get('export_output_dir')
    exported_files = st.session_state.get('exported_files_info')

    if not export_dir or not exported_files:
        st.warning("No exported files found or export directory missing. Please go back and complete the export process.")
        if st.button("Go to Data Loading"):
            reset_state()
            st.rerun()
    else:
        st.success("Export process completed successfully!")
        st.write("The following files are ready for download:")
        st.info(f"Files are temporarily stored and will be cleaned up after starting a new analysis or closing the session.")

        df_files = pd.DataFrame(exported_files)
        df_files['filename'] = df_files['path'].apply(os.path.basename)
        st.dataframe(df_files[['attribute', 'filename']])

        # Zip file download
        try:
            zip_buffer = BytesIO()
            # Use a base filename derived from the original SEG-Y if possible
            base_zip_filename = "WOSS_AOI_Export_Files.zip"
            if 'segy_file_info' in st.session_state and st.session_state.segy_file_info:
                 base_zip_filename = f"{st.session_state.segy_file_info['name']}_AOI_Export_Files.zip"

            with st.spinner("Preparing zip file for download..."):
                with zipfile.ZipFile(zip_buffer, "a", zipfile.ZIP_DEFLATED, False) as zip_file:
                    for file_info in exported_files:
                        try:
                            file_path = file_info['path']
                            if os.path.exists(file_path):
                                arcname = os.path.basename(file_path) # Name inside the zip file
                                zip_file.write(file_path, arcname=arcname)
                                logging.info(f"Added {arcname} to zip.")
                            else:
                                st.warning(f"File not found, cannot add to zip: {file_path}")
                                logging.warning(f"File not found for zipping: {file_path}")
                        except Exception as ze:
                             st.warning(f"Error adding {os.path.basename(file_info.get('path','N/A'))} to zip: {ze}")
                             logging.error(f"Error adding file to zip: {ze}", exc_info=True)

            zip_buffer.seek(0) # Rewind buffer
            st.download_button(
                label="Download All Exported Files (.zip)",
                data=zip_buffer,
                file_name=base_zip_filename,
                mime="application/zip",
                key="download_zip_button"
            )
            st.success("Zip file prepared. Click the button above to download.")

        except Exception as e:
            st.error(f"Could not create zip file: {e}")
            logging.error(f"Zip file creation failed: {e}", exc_info=True)


        st.markdown("---")
        col1, col2 = st.columns(2)

        with col1:
            if st.button("Back to Mode Selection"):
                # Keep the export directory for now in case user wants to come back
                st.session_state.current_step = "select_mode"
                st.rerun()

        with col2:
            if st.button("Start New Analysis"):
                # Clean up the persistent temporary export directory
                if export_dir and os.path.exists(export_dir):
                    try:
                        shutil.rmtree(export_dir)
                        logging.info(f"Cleaned up export directory: {export_dir}")
                    except Exception as clean_e:
                        logging.warning(f"Could not remove export directory {export_dir}: {clean_e}")

                reset_state() # Reset session state for a new run
                st.rerun()

# --- Fallback / Initial State ---
else:
     # This case should ideally not be reached if current_step is managed correctly
     st.info("Welcome! Please start by loading data in the sidebar.")
     if st.session_state.current_step != "load_data":
          # If state is somehow invalid, reset to start
          reset_state()
          st.rerun()
