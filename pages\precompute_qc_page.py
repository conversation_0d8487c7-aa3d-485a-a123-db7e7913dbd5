"""
Pre-computation & QC Page for the WOSS Seismic Analysis Tool.

This module handles the UI rendering for pre-computation and quality control
of the selected seismic data area before full analysis.
"""

import streamlit as st
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import logging

# Import common modules
from common.constants import APP_TITLE
from common.session_state import initialize_session_state, reset_state

# Import utility functions
from utils.data_utils import load_trace_sample
from utils.precomputation_utils import run_precomputation, apply_smoothing
from utils.qc_utils import display_qc_results, plot_amplitude_spectrum

def render():
    """Render the pre-computation & QC page UI."""
    # Initialize session state if needed
    initialize_session_state()

    st.header("Step 3.5: Pre-computation & Quality Control")
    st.sidebar.header("Pre-computation Options")

    # Log current state for debugging
    logging.info(f"In precompute_qc - display_params_configured: {st.session_state.get('display_params_configured')}")
    logging.info(f"In precompute_qc - area_selected: {st.session_state.get('area_selected')}")
    logging.info(f"In precompute_qc - selected_data_for_precompute: {st.session_state.get('selected_data_for_precompute') is not None}")
    logging.info(f"In precompute_qc - auto_run_precomputation: {st.session_state.get('auto_run_precomputation', False)}")

    # Check if area is selected
    if not st.session_state.get('area_selected'):
        logging.warning("Area not selected. Redirecting to select_traces step.")
        st.warning("Please select an area first (Step 3).")
        if st.button("Go to Select Area", use_container_width=True, type="primary"):
            st.session_state.current_step = "select_traces"
            st.rerun()
        return

    # Check if selected_data_for_precompute is available
    if not st.session_state.get('selected_data_for_precompute'):
        logging.warning("No selected data for precompute. Redirecting to select_traces step.")
        st.warning("No data selected for pre-computation. Please go back to Step 3 and select an area.")
        if st.button("Return to Select Area", use_container_width=True, type="primary"):
            st.session_state.current_step = "select_traces"
            st.rerun()
        return

    # Check if selected_indices is available and not empty
    if not st.session_state.get('selected_indices'):
        logging.warning("No selected indices but area_selected is True.")
        st.warning("No traces were found for the selected area. You may need to adjust your selection in Step 3.")
        # Provide a button to return to Step 3 if needed
        if st.button("Return to Select Area", use_container_width=True, type="primary"):
            st.session_state.current_step = "select_mode"
            st.rerun()
        # Don't return here, allow the user to see the rest of the page

    # Display information about the selected area
    st.subheader("Selected Area Information")

    # Get selected data from session state
    selected_data = st.session_state.get('selected_data_for_precompute', {})
    selected_indices = st.session_state.get('selected_indices', [])

    # Display different information based on selection mode
    if st.session_state.selection_mode == "By well markers":
        st.write(f"Selected well markers: {', '.join(st.session_state.selected_well_markers)}")
        if st.session_state.get('selected_well_marker_pairs'):
             st.write(f"Selected well-marker pairs: {', '.join(st.session_state.area_selected_details.get('labels', []))}") # Use labels from details
        st.write(f"Number of selected traces: {len(selected_indices)}")
    elif st.session_state.selection_mode == "Single inline (all crosslines)":
        st.write(f"Selected inline: {st.session_state.selected_inline}")
        st.write(f"Number of selected traces: {len(selected_indices)}")
    elif st.session_state.selection_mode == "Single crossline (all inlines)":
        st.write(f"Selected crossline: {st.session_state.selected_crossline}")
        st.write(f"Number of selected traces: {len(selected_indices)}")
    elif st.session_state.selection_mode == "By inline/crossline section (AOI)":
        st.write(f"Selected AOI: Inlines {st.session_state.aoi_inline_min}-{st.session_state.aoi_inline_max}, Crosslines {st.session_state.aoi_xline_min}-{st.session_state.aoi_xline_max}")
        st.write(f"Number of selected traces: {len(selected_indices)}")
    elif st.session_state.selection_mode == "By Polyline File Import":
        st.write(f"Selected polyline from file: {st.session_state.polyline_file_info['name'] if st.session_state.polyline_file_info else 'None'}")
        st.write(f"Polyline tolerance: {st.session_state.polyline_tolerance}")
        st.write(f"Number of selected traces: {len(selected_indices)}")

    # Display a warning if no traces were selected, but don't block the user from proceeding
    if len(selected_indices) == 0:
        st.warning("No traces were found for the selected area. You may need to adjust your selection in Step 3.")
        # Provide a button to return to Step 3 if needed
        if st.button("Return to Select Area", key="return_to_select_area_button"):
            st.session_state.current_step = "select_mode"
            st.rerun()

    # Check if we should automatically run precomputation (coming from well-marker selection)
    auto_run = st.session_state.get('auto_run_precomputation', False)

    # If auto_run is True and precomputation hasn't been completed yet, set default parameters and run precomputation
    if auto_run and not st.session_state.get('precomputation_complete', False):
        logging.info("Auto-running precomputation with default parameters")
        # Set default precomputation parameters
        st.session_state.precomputation_params = {
            "apply_smoothing": True,
            "smoothing_window": 5,
            "apply_normalization": True,
            "normalization_method": "Max Amplitude",
            "apply_filter": False,
            "filter_type": None,
            "filter_params": {}
        }

        # Run precomputation with default parameters (this code is duplicated from the submit button handler)
        with st.spinner("Automatically performing pre-computation and QC..."):
            try:
                # Get the selected indices from session state
                selected_indices = st.session_state.get('selected_indices', [])

                if not selected_indices:
                    st.error("No traces selected for pre-computation. Please go back to Step 3 and adjust your selection.")
                else:
                    # Limit to 10 traces for QC to avoid memory issues
                    qc_indices = selected_indices[:min(10, len(selected_indices))]

                    # Load trace samples for the selected indices
                    loaded_trace_data = []
                    for trace_idx in qc_indices:
                        try:
                            trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)
                            if trace_sample is not None and len(trace_sample) > 0:
                                loaded_trace_data.append({'trace_sample': trace_sample, 'trace_idx': trace_idx})
                        except Exception as e:
                            logging.warning(f"Could not load trace {trace_idx}: {e}")

                    if not loaded_trace_data:
                        st.error("Could not load any trace data for pre-computation. Please go back to Step 3 and adjust your selection.")
                    else:
                        # Extract trace samples for processing
                        original_traces = [item['trace_sample'] for item in loaded_trace_data]

                        # Run pre-computation
                        processed_traces = run_precomputation(original_traces, st.session_state.precomputation_params)

                        # Store results
                        precomputed_data = []
                        for i, item in enumerate(loaded_trace_data):
                            if i < len(processed_traces):  # Ensure we don't go out of bounds
                                precomputed_item = item.copy()
                                precomputed_item['processed_trace'] = processed_traces[i]
                                precomputed_data.append(precomputed_item)

                        # Calculate HFC p95 for WOSS calculation
                        hfc_values = []
                        for i, desc in enumerate(processed_traces):
                            if hasattr(desc, 'get') and desc.get('hfc') is not None:
                                hfc_values.extend(desc['hfc'])

                        if hfc_values:
                            # Get the user-configured HFC percentile (default to 95 if not specified)
                            hfc_percentile = st.session_state.plot_settings.get('hfc_percentile', 95.0)
                            hfc_p95 = np.percentile(hfc_values, hfc_percentile)
                            # Store in plot_settings for use in analyze_data_page.py
                            st.session_state.plot_settings['hfc_p95'] = float(hfc_p95)
                            logging.info(f"Calculated HFC p{hfc_percentile}: {hfc_p95}")

                            # Add a more visible confirmation for debugging
                            st.info(f"HFC p{hfc_percentile} value calculated: {hfc_p95:.4f} - This value will be used for WOSS calculation in step 3-4")

                        # Store the precomputed data
                        st.session_state.precomputed_data_output = precomputed_data
                        st.session_state.precomputation_complete = True

                        # Generate QC results
                        qc_results = {
                            "original_traces": original_traces,
                            "processed_traces": processed_traces,
                            "trace_indices": [item['trace_idx'] for item in loaded_trace_data]
                        }
                        st.session_state.qc_results_info = qc_results

                        # Clear the auto_run flag to prevent running again on page refresh
                        st.session_state.auto_run_precomputation = False

                        st.success("Pre-computation and QC completed automatically!")
                        st.rerun()  # Rerun to display QC results
            except Exception as e:
                st.error(f"An error occurred during automatic pre-computation/QC: {e}")
                logging.error(f"Automatic pre-computation/QC error: {e}", exc_info=True)
                st.session_state.precomputation_complete = False
                # Clear the auto_run flag to prevent running again on page refresh
                st.session_state.auto_run_precomputation = False

    # Pre-computation parameters
    st.subheader("Pre-computation Parameters")

    # Create a form for the parameters
    with st.form(key="precomputation_form"):
        # Smoothing parameters
        st.write("### Signal Smoothing")
        apply_smoothing = st.checkbox("Apply smoothing to seismic data", value=True)
        smoothing_window = st.slider("Smoothing window size", min_value=3, max_value=21, value=5, step=2,
                                    help="Window size for moving average smoothing. Must be odd number.")

        # Normalization parameters
        st.write("### Normalization")
        apply_normalization = st.checkbox("Apply trace normalization", value=True)
        normalization_method = st.selectbox("Normalization method",
                                           options=["Max Amplitude", "RMS", "L2 Norm"],
                                           index=0,
                                           help="Method to normalize trace amplitudes")

        # Filtering parameters
        st.write("### Frequency Filtering")
        apply_filter = st.checkbox("Apply frequency filter", value=False)
        filter_type = st.selectbox("Filter type",
                                  options=["Bandpass", "Lowpass", "Highpass"],
                                  index=0,
                                  disabled=not apply_filter)

        # Only show filter parameters if filtering is enabled
        if apply_filter:
            if filter_type == "Bandpass":
                col1, col2 = st.columns(2)
                with col1:
                    low_cut = st.number_input("Low cutoff frequency (Hz)", min_value=1.0, max_value=100.0, value=5.0, step=1.0)
                with col2:
                    high_cut = st.number_input("High cutoff frequency (Hz)", min_value=10.0, max_value=200.0, value=80.0, step=1.0)
                filter_params = {"low_cut": low_cut, "high_cut": high_cut}
            elif filter_type == "Lowpass":
                high_cut = st.number_input("Cutoff frequency (Hz)", min_value=1.0, max_value=200.0, value=60.0, step=1.0)
                filter_params = {"high_cut": high_cut}
            elif filter_type == "Highpass":
                low_cut = st.number_input("Cutoff frequency (Hz)", min_value=1.0, max_value=100.0, value=5.0, step=1.0)
                filter_params = {"low_cut": low_cut}
        else:
            filter_params = {}

        # Submit button
        submit_button = st.form_submit_button("Run Pre-computation & QC")

    # Store parameters in session state
    if submit_button:
        st.session_state.precomputation_params = {
            "apply_smoothing": apply_smoothing,
            "smoothing_window": smoothing_window,
            "apply_normalization": apply_normalization,
            "normalization_method": normalization_method,
            "apply_filter": apply_filter,
            "filter_type": filter_type if apply_filter else None,
            "filter_params": filter_params
        }

        # Run pre-computation
        with st.spinner("Performing pre-computation and QC..."):
            try:
                # Get the selected indices from session state
                selected_indices = st.session_state.get('selected_indices', [])

                if not selected_indices:
                    st.error("No traces selected for pre-computation. Please go back to Step 3 and adjust your selection.")
                    # Don't return, allow the user to see the error message and use the navigation buttons

                # Limit to 10 traces for QC to avoid memory issues
                qc_indices = selected_indices[:min(10, len(selected_indices))]

                # Load trace samples for the selected indices
                loaded_trace_data = []
                for trace_idx in qc_indices:
                    try:
                        trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)
                        if trace_sample is not None and len(trace_sample) > 0:
                            loaded_trace_data.append({'trace_sample': trace_sample, 'trace_idx': trace_idx})
                    except Exception as e:
                        logging.warning(f"Could not load trace {trace_idx}: {e}")

                if not loaded_trace_data:
                    st.error("Could not load any trace data for pre-computation. Please go back to Step 3 and adjust your selection.")
                    # Don't return, allow the user to see the error message and use the navigation buttons

                # Extract trace samples for processing
                original_traces = [item['trace_sample'] for item in loaded_trace_data]

                # Run pre-computation
                processed_traces = run_precomputation(original_traces, st.session_state.precomputation_params)

                # Store results
                precomputed_data = []
                for i, item in enumerate(loaded_trace_data):
                    if i < len(processed_traces):  # Ensure we don't go out of bounds
                        precomputed_item = item.copy()
                        precomputed_item['processed_trace'] = processed_traces[i]
                        precomputed_data.append(precomputed_item)

                # Calculate HFC p95 for WOSS calculation
                # This is important for the analyze_data_page.py to use the correct value
                hfc_values = []
                for i, desc in enumerate(processed_traces):
                    if hasattr(desc, 'get') and desc.get('hfc') is not None:
                        hfc_values.extend(desc['hfc'])

                if hfc_values:
                    # Get the user-configured HFC percentile (default to 95 if not specified)
                    hfc_percentile = st.session_state.plot_settings.get('hfc_percentile', 95.0)
                    hfc_p95 = np.percentile(hfc_values, hfc_percentile)
                    # Store in plot_settings for use in analyze_data_page.py
                    st.session_state.plot_settings['hfc_p95'] = float(hfc_p95)
                    logging.info(f"Calculated HFC p{hfc_percentile}: {hfc_p95}")

                    # Add a more visible confirmation for debugging
                    st.info(f"HFC p{hfc_percentile} value calculated: {hfc_p95:.4f} - This value will be used for WOSS calculation in step 3-4")

                # Store the precomputed data
                st.session_state.precomputed_data_output = precomputed_data
                st.session_state.precomputation_complete = True

                # Generate QC results
                qc_results = {
                    "original_traces": original_traces,
                    "processed_traces": processed_traces,
                    "trace_indices": [item['trace_idx'] for item in loaded_trace_data]
                }
                st.session_state.qc_results_info = qc_results

                st.success("Pre-computation and QC completed successfully!")
                st.rerun()  # Rerun to display QC results
            except Exception as e:
                st.error(f"An error occurred during pre-computation/QC: {e}")
                logging.error(f"Pre-computation/QC error: {e}", exc_info=True)
                st.session_state.precomputation_complete = False

    # Display QC results if available
    if st.session_state.get('precomputation_complete') and st.session_state.get('qc_results_info'):
        st.subheader("Quality Control Results")

        # Display QC plots
        qc_results = st.session_state.qc_results_info

        # Create tabs for different QC visualizations
        tab1, tab2, tab3 = st.tabs(["Trace Comparison", "Spectrum Analysis", "Statistics"])

        with tab1:
            st.write("### Original vs Processed Traces")

            # Select which trace to display
            trace_index = st.selectbox(
                "Select trace to display",
                options=range(len(qc_results["original_traces"])),
                format_func=lambda i: f"Trace {i+1} (Index: {qc_results['trace_indices'][i]})"
            )

            # Plot original vs processed trace
            fig, ax = plt.subplots(1, 2, figsize=(12, 6))

            # Original trace
            ax[0].plot(qc_results["original_traces"][trace_index])
            ax[0].set_title("Original Trace")
            ax[0].set_xlabel("Sample")
            ax[0].set_ylabel("Amplitude")

            # Processed trace
            # Check if the processed trace is a dictionary (contains descriptors) or a numpy array
            processed_trace_data = qc_results["processed_traces"][trace_index]
            if isinstance(processed_trace_data, dict) and 'data' in processed_trace_data:
                plot_data = processed_trace_data['data']
            else:
                plot_data = processed_trace_data
            ax[1].plot(plot_data, color='orange')
            ax[1].set_title("Processed Trace")
            ax[1].set_xlabel("Sample")
            ax[1].set_ylabel("Amplitude")

            plt.tight_layout()
            st.pyplot(fig)

        with tab2:
            st.write("### Frequency Spectrum Comparison")

            # Select which trace to display
            trace_index = st.selectbox(
                "Select trace for spectrum analysis",
                options=range(len(qc_results["original_traces"])),
                format_func=lambda i: f"Trace {i+1} (Index: {qc_results['trace_indices'][i]})",
                key="spectrum_trace_select"
            )

            # Plot amplitude spectra
            fig, ax = plt.subplots(1, 2, figsize=(12, 6))

            # Original trace spectrum
            # Check if the original trace is a dictionary (shouldn't be, but for safety) or a numpy array
            original_trace_data = qc_results["original_traces"][trace_index]
            if isinstance(original_trace_data, dict) and 'data' in original_trace_data:
                plot_data_orig = original_trace_data['data']
            else:
                plot_data_orig = original_trace_data
            orig_spec = plot_amplitude_spectrum(plot_data_orig, ax=ax[0])
            ax[0].set_title("Original Trace Spectrum")

            # Processed trace spectrum
            # Check if the processed trace is a dictionary (contains descriptors) or a numpy array
            processed_trace_data = qc_results["processed_traces"][trace_index]
            if isinstance(processed_trace_data, dict) and 'data' in processed_trace_data:
                plot_data_proc = processed_trace_data['data']
            else:
                plot_data_proc = processed_trace_data
            proc_spec = plot_amplitude_spectrum(plot_data_proc, ax=ax[1])
            ax[1].set_title("Processed Trace Spectrum")

            plt.tight_layout()
            st.pyplot(fig)

        with tab3:
            st.write("### Statistical Comparison")

            # Calculate statistics for original and processed traces
            orig_stats = []
            proc_stats = []

            for i in range(len(qc_results["original_traces"])):
                # Check if the original trace is a dictionary (shouldn't be, but for safety) or a numpy array
                original_trace_data = qc_results["original_traces"][i]
                if isinstance(original_trace_data, dict) and 'data' in original_trace_data:
                    orig_trace = original_trace_data['data']
                else:
                    orig_trace = original_trace_data

                # Check if the processed trace is a dictionary (contains descriptors) or a numpy array
                processed_trace_data = qc_results["processed_traces"][i]
                if isinstance(processed_trace_data, dict) and 'data' in processed_trace_data:
                    proc_trace = processed_trace_data['data']
                else:
                    proc_trace = processed_trace_data

                orig_stats.append({
                    "Trace Index": qc_results["trace_indices"][i],
                    "Min": np.min(orig_trace),
                    "Max": np.max(orig_trace),
                    "Mean": np.mean(orig_trace),
                    "Std Dev": np.std(orig_trace),
                    "RMS": np.sqrt(np.mean(np.square(orig_trace)))
                })

                proc_stats.append({
                    "Trace Index": qc_results["trace_indices"][i],
                    "Min": np.min(proc_trace),
                    "Max": np.max(proc_trace),
                    "Mean": np.mean(proc_trace),
                    "Std Dev": np.std(proc_trace),
                    "RMS": np.sqrt(np.mean(np.square(proc_trace)))
                })

            # Create DataFrames
            orig_df = pd.DataFrame(orig_stats)
            proc_df = pd.DataFrame(proc_stats)

            # Display statistics
            col1, col2 = st.columns(2)

            with col1:
                st.write("Original Traces Statistics")
                st.dataframe(orig_df.style.format({
                    "Min": "{:.4f}",
                    "Max": "{:.4f}",
                    "Mean": "{:.4f}",
                    "Std Dev": "{:.4f}",
                    "RMS": "{:.4f}"
                }))

            with col2:
                st.write("Processed Traces Statistics")
                st.dataframe(proc_df.style.format({
                    "Min": "{:.4f}",
                    "Max": "{:.4f}",
                    "Mean": "{:.4f}",
                    "Std Dev": "{:.4f}",
                    "RMS": "{:.4f}"
                }))

        # Navigation buttons
        st.markdown("---")
        col1, col2 = st.columns(2)
        with col1:
            if st.button("⬅️ Back to Select Area (Step 3)"):
                st.session_state.current_step = "select_mode"
                st.rerun()
        with col2:
            if st.button("Next to Analyze Data (Step 4) ➡️", type="primary"):
                st.session_state.current_step = "analyze_data"
                st.rerun()

    # Add a "Start New Analysis" button to the sidebar
    st.sidebar.markdown("---")
    if st.sidebar.button("🔄 Start New Analysis", use_container_width=True):
        reset_state()
        st.success("Starting new analysis. All temporary data has been cleared.")
        st.rerun()
