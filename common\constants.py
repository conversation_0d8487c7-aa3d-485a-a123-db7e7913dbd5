"""
Constants for the WOSS Seismic Analysis Tool.
This module contains all the constants used throughout the application.
"""

# Application title
APP_TITLE = "WOSS Seismic Analysis Tool"

# Outputs for single trace analysis (Mode 1, Plot 1)
AVAILABLE_OUTPUTS_SINGLE = [
    "Input Signal", "Magnitude Spectrogram", "Magnitude * Voice",
    "Normalized dominant frequencies", "Spectral Slope", "Spectral Bandwidth",
    "Spectral Rolloff", "Mag*Voice Slope", "Spectral Decrease", "HFC", "WOSS"
]

# Outputs for multi-trace/section analysis (Modes 1(Plot 2), 2, 3, 4(Plot), 5)
AVAILABLE_OUTPUTS_MULTI = [
    "Input Signal",
    "Magnitude Spectrogram", # Added for comparative plots
    "Magnitude * Voice",     # Added for comparative plots
    "Normalized dominant frequencies",
    "Spectral Slope", "Spectral Bandwidth",
    "Spectral Rolloff", "Mag*Voice Slope", "Spectral Decrease", "HFC", "WOSS"
]

# Outputs for section/polyline modes (Modes 2, 3, 4(Plot), 5) - excluding spectral images
AVAILABLE_OUTPUTS_SECTION = [
    "Input Signal",
    # "Magnitude Spectrogram", # Excluded for section modes
    # "Magnitude * Voice",     # Excluded for section modes
    "Normalized dominant frequencies",
    "Spectral Slope", "Spectral Bandwidth",
    "Spectral Rolloff", "Mag*Voice Slope", "Spectral Decrease", "HFC", "WOSS"
]

# Internal names used in descriptor dicts and for export keys
EXPORTABLE_ATTR_INTERNAL_NAMES = [
    "data", # Original seismic data
    "mag_voice_slope",
    "spec_decrease",
    "hfc",
    "spec_bandwidth",
    "spec_rolloff",
    "WOSS", # Calculated attribute
    "norm_fdom" # Note: This is frequency domain, export might need care
]

# Display names corresponding to EXPORTABLE_ATTR_INTERNAL_NAMES
EXPORTABLE_ATTR_DISPLAY_NAMES = [
    "Original Seismic Amplitude",
    "Magnitude*Voice Slope",
    "Spectral Decrease",
    "High Frequency Content (HFC)",
    "Spectral Bandwidth",
    "Spectral Rolloff",
    "Weighted Optimum Spectral Shape",
    "Normalized Dominant Frequency" # Display name for export
]

# Mapping for easy lookup
ATTR_NAME_MAP = dict(zip(EXPORTABLE_ATTR_DISPLAY_NAMES, EXPORTABLE_ATTR_INTERNAL_NAMES))
REVERSE_ATTR_NAME_MAP = dict(zip(EXPORTABLE_ATTR_INTERNAL_NAMES, EXPORTABLE_ATTR_DISPLAY_NAMES))

# Descriptor Colormap/Amplitude Limits
DESCRIPTOR_LIMITS = {
    "Input Signal": {"min": -1.0, "max": 1.0},
    "HFC": {"min": 0.0, "max": 1.0},
    "Spectral Decrease": {"min": 0.0, "max": 1.0},
    "Spectral Slope": {"min": -1.0, "max": 1.0},
    "Mag*Voice Slope": {"min": -1.0, "max": 1.0},
    "Spectral Bandwidth": {"min": 0.0, "max": 50.0},
    "Spectral Rolloff": {"min": 0.0, "max": 50.0},
    "Magnitude Spectrogram": {"min": 0.0, "max": 1.0},
    "Magnitude * Voice": {"min": 0.0, "max": 1.0},
    "Normalized Dominant Frequency": {"min": 0.0, "max": 1.0},
    "WOSS": {"min": -1.0, "max": 1.0},
}

# GPU availability flag - will be set during initialization
GPU_AVAILABLE = False
