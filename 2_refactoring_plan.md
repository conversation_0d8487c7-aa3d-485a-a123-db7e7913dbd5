# Refactoring Guideline: Integrating Pre-computation & QC Step

This document outlines the steps to refactor the WOSS Seismic Analysis Tool to include a new "Pre-computation & QC" step (Step 3.5) and adjust existing steps.

## Summary of Changes:

1.  **Introduce New Step "3.5. Pre-computation & QC":**
    *   This step will allow users to perform preliminary computations and quality control on the selected seismic data area before full analysis.
2.  **Update Step 3 "Select Area":**
    *   This step will now flag when an area selection is complete and make the selected data/parameters available for the new pre-computation step.
3.  **Update Step 4 "Analyze Data":**
    *   This step will now depend on the completion of the "Pre-computation & QC" step and use its output.
4.  **Update Session State Management:**
    *   New session state variables will be added to manage the status and data related to the pre-computation step.
5.  **Create Utility Modules:**
    *   New utility modules will be created to house the logic for pre-computation tasks and QC processes.

## Detailed File Modifications and Creations:

### 1. `app.py` Modifications

*   **File Path:** `c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\08d_WOSS_Streamlit\1c_Modular_deep\app.py`
*   **Summary of Changes:**
    *   Import the new `render_precompute_qc` function.
    *   Add "3.5. Pre-computation & QC" to the `APP_STEPS` dictionary.
    *   Update the sidebar navigation logic to correctly identify and switch to the "3.5. Pre-computation & QC" step.
    *   Add routing logic for the "precompute_qc" step, including prerequisite checks (data loaded, display configured, area selected).
    *   Update the prerequisite check for the "analyze_data" step to ensure "precomputation_complete" is true.

```python
# filepath: c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\08d_WOSS_Streamlit\1c_Modular_deep\app.py
"""
WOSS Seismic Analysis Tool - Main Application Router

This module serves as the primary router for the WOSS Seismic Analysis Tool.
It imports page modules and calls their render() functions based on the current step
in the application workflow.
"""

import streamlit as st
import logging
import torch

# Import common modules
from common.constants import APP_TITLE, GPU_AVAILABLE
from common.session_state import initialize_session_state, reset_state

# Import page modules
from pages.load_data_page import render as render_load_data
from pages.configure_display_page import render as render_configure_display
from pages.select_area_page import render as render_select_area
from pages.analyze_data_page import render as render_analyze_data
from pages.export_results_page import render as render_export_results
# Add import for the new precompute_qc page
from pages.precompute_qc_page import render as render_precompute_qc

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Configure page
st.set_page_config(page_title=APP_TITLE, layout="wide")

# Initialize session state
initialize_session_state()

# Set application title
st.title(APP_TITLE)

# Define application steps for sidebar navigation
APP_STEPS = {
    "1. Load Data": "load_data",
    "2. Configure Display": "configure_display",
    "3. Select Area": "select_area",
    "3.5. Pre-computation & QC": "precompute_qc", # New Step
    "4. Analyze Data": "analyze_data",
    "5. Export Results": "export_results",
}
# Get the current step's display name for the radio button default
# This handles cases where current_step might be a sub-step like 'select_traces'
current_display_step = "1. Load Data" # Default
if st.session_state.current_step == "load_data":
    current_display_step = "1. Load Data"
elif st.session_state.current_step == "configure_display":
    current_display_step = "2. Configure Display"
elif st.session_state.current_step in ["select_mode", "select_traces", "select_area"]:
    current_display_step = "3. Select Area"
elif st.session_state.current_step == "precompute_qc": # New Step
    current_display_step = "3.5. Pre-computation & QC"
elif st.session_state.current_step == "analyze_data":
    current_display_step = "4. Analyze Data"
elif st.session_state.current_step in ["view_results", "export_process", "download_export", "export_results"]:
    current_display_step = "5. Export Results"


st.sidebar.header("Navigation")
selected_display_step = st.sidebar.radio(
    "Go to step:",
    options=list(APP_STEPS.keys()),
    index=list(APP_STEPS.keys()).index(current_display_step), # Set default based on current step
    key="sidebar_navigation"
)

# Update current_step if navigation changes
new_step = APP_STEPS[selected_display_step]
if st.session_state.current_step != new_step:
    # For steps that have sub-states, ensure we go to the primary state of that step
    if new_step == "select_area" and st.session_state.current_step not in ["select_mode", "select_traces"]:
         st.session_state.current_step = "select_mode" # Default to first sub-step or main step
    elif new_step == "precompute_qc": # New Step handling
        # Add logic if precompute_qc has sub-steps, otherwise:
        st.session_state.current_step = new_step
    elif new_step == "export_results" and st.session_state.current_step not in ["view_results", "export_process", "download_export"]:
        st.session_state.current_step = "view_results" # Default to first sub-step or main step
    else:
        st.session_state.current_step = new_step
    st.rerun()


# Add "Start New Analysis" button to sidebar (visible on all pages)
st.sidebar.markdown("---")  # Add a separator
if st.sidebar.button("🔄 Start New Analysis", use_container_width=True, key="app_start_new_analysis_button"): # Added unique key
    reset_state() # This will set current_step back to 'load_data'
    st.success("Starting new analysis. All temporary data has been cleared.")
    st.rerun()

# Check GPU availability
try:
    if torch.cuda.is_available():
        st.sidebar.success("GPU functions loaded successfully.")
        logging.info("Successfully imported GPU spectral descriptor functions")
        GPU_AVAILABLE = True
    else:
        st.sidebar.warning("GPU functions not available. Processing will be slower.")
        logging.warning("GPU not available")
        GPU_AVAILABLE = False
except ImportError as e:
    st.sidebar.warning(f"GPU functions not available: {e}. Processing will be slower.")
    logging.warning(f"Could not import GPU functions: {e}")
    GPU_AVAILABLE = False

# Route to the appropriate page based on the current step
# The st.session_state.current_step is now managed by the sidebar navigation or page logic

if st.session_state.current_step == "load_data":
    render_load_data()
elif st.session_state.current_step == "configure_display":
    render_configure_display()
elif st.session_state.current_step in ["select_mode", "select_traces", "select_area"]: # "select_area" is the key from APP_STEPS
    # Ensure sub-steps are handled correctly if direct navigation to "select_area" occurs
    if st.session_state.current_step == "select_area": # Navigated via sidebar to the general step
        if not st.session_state.get('segy_file_info'): # Check if data is loaded
             st.session_state.current_step = "load_data"
             st.rerun()
        elif not st.session_state.get('display_params_configured'): # Check if display is configured
             st.session_state.current_step = "configure_display"
             st.rerun()
        else: # Default to 'select_mode' if prerequisites met
            st.session_state.current_step = "select_mode"
            # No rerun here, render_select_area will handle 'select_mode'
    render_select_area()
elif st.session_state.current_step == "precompute_qc": # New Step Routing
    # Prerequisite checks for Pre-computation & QC
    if not st.session_state.get('segy_file_info'):
        st.session_state.current_step = "load_data"
        st.rerun()
    elif not st.session_state.get('display_params_configured'):
        st.session_state.current_step = "configure_display"
        st.rerun()
    elif not st.session_state.get('area_selected'): # Assumes 'area_selected' is set in select_area_page
        st.warning("Please select an area first (Step 3).")
        st.session_state.current_step = "select_mode" # or "select_area" to go to the main step
        st.rerun()
    else:
        render_precompute_qc()
elif st.session_state.current_step in ["view_results", "export_process", "download_export", "export_results"]: # "export_results" is the key from APP_STEPS
    # Ensure sub-steps are handled correctly
    if st.session_state.current_step == "export_results": # Navigated via sidebar
        if not st.session_state.get('analysis_complete'): # Check if analysis is done
            st.session_state.current_step = "analyze_data" # Or a previous step if appropriate
            st.rerun()
        else: # Default to 'view_results'
            st.session_state.current_step = "view_results"
            # No rerun here, render_export_results will handle 'view_results'
    render_export_results()
elif st.session_state.current_step == "analyze_data":
    # Prerequisite checks for Analyze Data
    if not st.session_state.get('segy_file_info'):
        st.session_state.current_step = "load_data"
        st.rerun()
    elif not st.session_state.get('display_params_configured'):
        st.session_state.current_step = "configure_display"
        st.rerun()
    elif not st.session_state.get('area_selected'):
        st.session_state.current_step = "select_mode" # or "select_area"
        st.rerun()
    elif not st.session_state.get('precomputation_complete'): # New prerequisite
        st.warning("Please complete Pre-computation & QC (Step 3.5) first.")
        st.session_state.current_step = "precompute_qc"
        st.rerun()
    else:
        render_analyze_data()
else:
    # Default to load_data if current_step is not recognized (should be rare with radio button)
    st.session_state.current_step = "load_data"
    render_load_data()
```

### 2. Create `pages/precompute_qc_page.py`

*   **File Path:** `c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\08d_WOSS_Streamlit\1c_Modular_deep\pages\precompute_qc_page.py`
*   **Summary:** This new file will contain the UI and logic for the "Pre-computation & QC" step.
    *   It will check if an area has been selected (prerequisite).
    *   Provide UI elements for users to set pre-computation parameters.
    *   Include a button to trigger the pre-computation and QC process.
    *   Display QC results (e.g., plots, metrics).
    *   Set `st.session_state.precomputation_complete = True` upon successful completion.
    *   Provide navigation to the next step ("Analyze Data") or back to "Select Area".

```python
# filepath: c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\08d_WOSS_Streamlit\1c_Modular_deep\pages\precompute_qc_page.py
import streamlit as st
import logging
# from utils.precomputation_utils import run_precomputation # Example import
# from utils.qc_utils import display_qc_results # Example import

def render():
    st.header("3.5. Pre-computation & Quality Control")

    if not st.session_state.get('area_selected'):
        st.warning("Please complete Step 3: Select Area first.")
        if st.button("Go to Select Area"):
            st.session_state.current_step = "select_mode" # Or "select_area"
            st.rerun()
        return

    st.write("This section allows for pre-computation tasks on the selected seismic data area and quality control checks.")

    # Placeholder for pre-computation parameters
    st.subheader("Pre-computation Parameters")
    # Example: param = st.slider("Smoothing Factor", 0.1, 1.0, 0.5)
    # st.session_state.precomputation_params = {'smoothing': param}


    if st.button("Run Pre-computation & QC"):
        with st.spinner("Performing pre-computation and QC..."):
            try:
                # selected_area_data = st.session_state.get('selected_data_for_precompute') # Data from select_area_page
                # if selected_area_data is None:
                #     st.error("No data available from area selection for pre-computation.")
                #     return

                # Call utility functions
                # precomputed_data = run_precomputation(selected_area_data, st.session_state.precomputation_params)
                # qc_info = display_qc_results(precomputed_data) # Or this could be part of the UI below

                # Store results in session state
                # st.session_state.precomputed_data_output = precomputed_data
                # st.session_state.qc_results_info = qc_info
                st.session_state.precomputation_complete = True # CRITICAL: Set this flag
                st.success("Pre-computation and QC completed successfully!")
                logging.info("Pre-computation and QC completed.")
            except Exception as e:
                st.error(f"An error occurred during pre-computation/QC: {e}")
                logging.error(f"Pre-computation/QC error: {e}")
                st.session_state.precomputation_complete = False

    if st.session_state.get('precomputation_complete'):
        st.subheader("QC Results")
        st.write("Display QC plots and metrics here.")
        # Example: st.image(st.session_state.qc_results_info.get('qc_plot'))
        # st.metric("Data Quality Score", st.session_state.qc_results_info.get('quality_score', "N/A"))

        if st.button("Proceed to Analyze Data (Step 4)"):
            st.session_state.current_step = "analyze_data"
            st.rerun()
    else:
        st.info("Run pre-computation and QC to view results and proceed.")

    # Navigation buttons
    col1, col2 = st.columns(2)
    with col1:
        if st.button("⬅️ Back to Select Area (Step 3)"):
            st.session_state.current_step = "select_area" # Or specific sub-step like "select_traces"
            st.rerun()
    with col2:
        if st.session_state.get('precomputation_complete'):
            if st.button("Next to Analyze Data (Step 4) ➡️", type="primary"):
                st.session_state.current_step = "analyze_data"
                st.rerun()
```

### 3. Modify `pages/select_area_page.py`

*   **File Path:** `c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\08d_WOSS_Streamlit\1c_Modular_deep\pages\select_area_page.py`
*   **Summary of Changes (Conceptual - requires implementation within existing logic):**
    *   After the user successfully confirms an area selection (e.g., traces, polygon):
        *   Set `st.session_state.area_selected = True`.
        *   Store the data or parameters defining the selected area in `st.session_state` (e.g., `st.session_state.selected_trace_indices`, `st.session_state.selected_data_for_precompute`). This data will be consumed by the "Pre-computation & QC" step.
    *   The "Next" button or action in this page should navigate to `st.session_state.current_step = "precompute_qc"`.

    **Illustrative Snippet (to be integrated into existing `render` function):**
    ```python
    # ... inside render() function of select_area_page.py, after area selection logic ...
    # if area_is_confirmed_by_user:
    #     st.session_state.area_selected = True
    #     # Example: Store selected trace data or indices
    #     # st.session_state.selected_data_for_precompute = get_data_for_selected_area(...)
    #     st.success("Area selection complete.")
    #     if st.button("Next to Pre-computation & QC ➡️", type="primary"):
    #         st.session_state.current_step = "precompute_qc"
    #         st.rerun()
    ```

### 4. Modify `pages/analyze_data_page.py`

*   **File Path:** `c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\08d_WOSS_Streamlit\1c_Modular_deep\pages\analyze_data_page.py`
*   **Summary of Changes (Conceptual - requires implementation within existing logic):**
    *   The prerequisite check for `precomputation_complete` is handled in `app.py`.
    *   This page should now use the output from the pre-computation step (e.g., `st.session_state.precomputed_data_output`) as its primary input for analysis instead of directly using data from the "Select Area" step.

    **Illustrative Snippet (to be integrated into existing `render` function):**
    ```python
    # ... inside render() function of analyze_data_page.py ...
    # # Prerequisite check is largely handled by app.py, but a local check can be good
    # if not st.session_state.get('precomputation_complete'):
    #     st.warning("Please complete Step 3.5: Pre-computation & QC first.")
    #     if st.button("Go to Pre-computation & QC"):
    #         st.session_state.current_step = "precompute_qc"
    #         st.rerun()
    #     return
    #
    # precomputed_data = st.session_state.get('precomputed_data_output')
    # if precomputed_data is None:
    #    st.error("Pre-computed data not found. Please ensure Step 3.5 was completed successfully.")
    #    # Optionally, offer to go back
    #    if st.button("Go to Pre-computation & QC"):
    #        st.session_state.current_step = "precompute_qc"
    #        st.rerun()
    #    return
    #
    # # ... proceed with analysis using precomputed_data ...
    # st.write("Analyzing pre-computed data...")
    ```

### 5. Modify `common/session_state.py`

*   **File Path:** `c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\08d_WOSS_Streamlit\1c_Modular_deep\common\session_state.py`
*   **Summary of Changes:**
    *   Add new keys to `initialize_session_state` for the pre-computation step:
        *   `area_selected` (boolean, to be set by `select_area_page.py`)
        *   `precomputation_params` (dictionary)
        *   `precomputation_complete` (boolean)
        *   `precomputed_data_output` (to store the result of pre-computation)
        *   `qc_results` (to store QC metrics or plot data)
    *   Add these new keys to the `keys_to_reset` list in the `reset_state` function.

```python
# filepath: c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\08d_WOSS_Streamlit\1c_Modular_deep\common\session_state.py
import streamlit as st
import logging # Added logging import if not present

def initialize_session_state():
    default_states = {
        'current_step': "load_data",
        'segy_file_info': None,
        'segy_data': None,
        'seismic_metadata': None,
        'display_params': None,
        'display_params_configured': False,
        'selected_area_mode': None,
        'selected_traces_input': None,
        'selected_traces_indices': None,
        'selected_polygon_coords': None,
        'area_selected': False,  # For Step 3 completion
        'precomputation_params': {}, # For Step 3.5
        'precomputation_complete': False, # For Step 3.5
        'precomputed_data_output': None, # For Step 3.5
        'qc_results': None, # For Step 3.5
        'analysis_params': None,
        'analysis_results': None,
        'analysis_complete': False,
        'export_format': None,
        'export_path': None,
        'export_status': None
        # Add any other default states your application needs
    }
    for key, value in default_states.items():
        if key not in st.session_state:
            st.session_state[key] = value

def reset_state():
    # List of all keys managed by initialize_session_state
    # This ensures that when reset, all keys are cleared and then re-initialized to their defaults
    keys_to_clear = [
        'current_step', 'segy_file_info', 'segy_data', 'seismic_metadata',
        'display_params', 'display_params_configured',
        'selected_area_mode', 'selected_traces_input', 'selected_traces_indices',
        'selected_polygon_coords', 'area_selected',
        'precomputation_params', 'precomputation_complete',
        'precomputed_data_output', 'qc_results',
        'analysis_params', 'analysis_results', 'analysis_complete',
        'export_format', 'export_path', 'export_status'
        # Add any other session state keys used by your application
    ]
    for key in keys_to_clear:
        if key in st.session_state:
            del st.session_state[key]

    initialize_session_state() # Re-initialize all states to their default values
    # current_step is set to 'load_data' by initialize_session_state
    logging.info("Session state has been reset.")

# Ensure initialize_session_state is called once at the beginning if not already handled by app.py
# if 'current_step' not in st.session_state: # This check might be redundant if app.py calls it first
#    initialize_session_state()
```

### 6. Create Utility Modules (Conceptual)

*   **`utils/precomputation_utils.py`**:
    *   **File Path:** `c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\08d_WOSS_Streamlit\1c_Modular_deep\utils\precomputation_utils.py`
    *   **Purpose:** This module will house functions related to data pre-processing.
    *   **Example Content (Illustrative):**
        ```python
        # filepath: c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\08d_WOSS_Streamlit\1c_Modular_deep\utils\precomputation_utils.py
        import numpy as np

        def apply_smoothing(data, window_size=5):
            # Example smoothing function
            if data is None:
                return None
            # Placeholder for actual smoothing logic
            return np.convolve(data, np.ones(window_size)/window_size, mode='same')

        def run_precomputation(seismic_data_subset, params):
            """
            Main function to run all pre-computation steps.
            seismic_data_subset: The data selected in Step 3.
            params: Dictionary of parameters from Step 3.5 UI.
            """
            processed_data = seismic_data_subset.copy() # Start with a copy

            # Example: Apply filtering if specified in params
            # if params.get('apply_filter'):
            #     processed_data = apply_filter(processed_data, params.get('filter_type'), params.get('filter_params'))

            # Example: Apply normalization
            # if params.get('normalize'):
            #     processed_data = normalize_data(processed_data)

            # Example: Calculate an attribute
            # if params.get('calculate_envelope'):
            #     envelope = calculate_envelope(processed_data)
            #     # Store or return multiple outputs as needed, perhaps in a dictionary
            #     return {'processed_traces': processed_data, 'envelope': envelope}

            # For now, just a placeholder
            # processed_data = apply_smoothing(processed_data, params.get('smoothing_window', 5))
            print(f"Running precomputation with params: {params}")
            # Simulate processing
            return processed_data # Or a dictionary of results
        ```

*   **`utils/qc_utils.py`**:
    *   **File Path:** `c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\08d_WOSS_Streamlit\1c_Modular_deep\utils\qc_utils.py`
    *   **Purpose:** This module will contain functions for generating Quality Control plots, metrics, or visualizations.
    *   **Example Content (Illustrative):**
        ```python
        # filepath: c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\08d_WOSS_Streamlit\1c_Modular_deep\utils\qc_utils.py
        import matplotlib.pyplot as plt
        import streamlit as st
        import numpy as np

        def plot_amplitude_spectrum(trace_data, title="Amplitude Spectrum"):
            """Generates an amplitude spectrum plot for a seismic trace."""
            if trace_data is None or len(trace_data) == 0:
                fig, ax = plt.subplots()
                ax.text(0.5, 0.5, "No data to display", ha='center', va='center')
                return fig

            fig, ax = plt.subplots()
            amp_spec = np.abs(np.fft.fft(trace_data))
            freqs = np.fft.fftfreq(len(trace_data))
            ax.plot(freqs[:len(freqs)//2], amp_spec[:len(amp_spec)//2]) # Plot positive frequencies
            ax.set_title(title)
            ax.set_xlabel("Frequency")
            ax.set_ylabel("Amplitude")
            return fig

        def display_qc_results(original_data_subset, precomputed_data_output):
            """
            Displays QC information, potentially comparing original and processed data.
            This function would be called from precompute_qc_page.py.
            """
            st.subheader("Quality Control Visualizations")

            # Example: Display spectrum of the first trace before and after
            # Assuming precomputed_data_output might be a dictionary or the processed traces directly
            # And original_data_subset is available (might need to be passed or retrieved from session state)

            # For simplicity, let's assume we have one trace to compare
            # trace_original = original_data_subset[0] if original_data_subset is not None and len(original_data_subset) > 0 else None
            # trace_processed = precomputed_data_output[0] if precomputed_data_output is not None and len(precomputed_data_output) > 0 else None

            # if trace_original is not None:
            #     st.pyplot(plot_amplitude_spectrum(trace_original, "Original Data Spectrum (First Selected Trace)"))
            # if trace_processed is not None:
            #     st.pyplot(plot_amplitude_spectrum(trace_processed, "Processed Data Spectrum (First Selected Trace)"))

            # Placeholder for more complex QC
            if precomputed_data_output is not None:
                st.write("Pre-computed data details:")
                # st.write(f"Shape: {precomputed_data_output.shape}") # Example if it's a numpy array
                # st.line_chart(precomputed_data_output[:, 0] if precomputed_data_output.ndim > 1 else precomputed_data_output) # Plot first trace
            else:
                st.write("No pre-computed data to display QC for.")

            # Return any metrics if needed
            # qc_metrics = {"mean_amplitude_change": ...}
            # return qc_metrics
        ```

This guideline provides a comprehensive plan for the refactoring. Remember to implement the actual pre-computation and QC logic within the utility functions and call them appropriately from the `precompute_qc_page.py`.
