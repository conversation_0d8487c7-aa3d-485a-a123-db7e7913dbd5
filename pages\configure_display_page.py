"""
Configure Display Page for the WOSS Seismic Analysis Tool.

This module handles the UI rendering for configuring display settings.
It follows the principles outlined in rules.md, particularly regarding
the separation of concerns between UI and backend logic.
"""

import streamlit as st
import segyio
import logging
import plotly.colors as pcolors
import numpy as np # Added for nyquist_freq calculation if dt is not available

# Import common modules
# from common.constants import DESCRIPTOR_LIMITS # DESCRIPTOR_LIMITS will be defined locally as in app_ref.py
from common.session_state import initialize_session_state, reset_state
from common.ui_elements import slider_with_number_input

# Import utility functions
from utils.visualization import plot_basemap_with_wells
from utils.data_utils import get_surfaces
from utils.processing import calculate_stats_and_defaults

def render():
    """Render the configure display page UI."""
    # Initialize session state if needed
    initialize_session_state()

    st.header("Step 2: Configure Analysis Parameters")
    st.sidebar.header("Display Configuration")

    # Check if data is loaded
    if not st.session_state.get('header_loader'): # Use .get for safety
        st.warning("Please load data first in Step 1.")
        # Option to navigate back or disable this step until data is loaded
        if st.button("Go to Load Data"):
            st.session_state.current_step = "load_data"
            st.rerun()
        return

    # --- Basemap Expander ---
    with st.expander("Basemap & SEGY Info", expanded=True):
        st.subheader("SEGY Information")
        if st.session_state.header_loader:
            time_range_str = "N/A"
            inline_range_str = "N/A"
            xline_range_str = "N/A"
            num_samples = 0

            try:
                if st.session_state.dt and st.session_state.segy_temp_file_path:
                    with segyio.open(st.session_state.segy_temp_file_path, ignore_geometry=True) as f:
                        try:
                            if f.tracecount > 0:
                                first_trace = f.trace[0]
                                if hasattr(first_trace, 'shape'):
                                    num_samples = first_trace.shape[0]
                                else:
                                    num_samples = len(first_trace)
                            else:
                                num_samples = len(f.samples)
                            
                            if num_samples > 0:
                                time_max = (num_samples - 1) * st.session_state.dt
                                time_range_str = f"0.00 - {time_max:.3f} s"
                            else:
                                time_range_str = "Error reading samples"
                        except Exception as e:
                            logging.warning(f"Error getting trace samples: {e}", exc_info=True)
                            time_range_str = "Error reading samples"
                            try:
                                num_samples = len(f.samples)
                                if num_samples > 0:
                                    time_max = (num_samples - 1) * st.session_state.dt
                                    time_range_str = f"0.00 - {time_max:.3f} s"
                            except Exception:
                                num_samples = 0
                
                range_dict = st.session_state.header_loader.get_inline_crossline_range()
                if (range_dict and isinstance(range_dict, dict) and
                    all(key in range_dict for key in ['inline_min', 'inline_max', 'xline_min', 'xline_max'])):
                    inline_range_str = f"{range_dict['inline_min']} - {range_dict['inline_max']}"
                    xline_range_str = f"{range_dict['xline_min']} - {range_dict['xline_max']}"
                else:
                    logging.warning("Invalid range dictionary from get_inline_crossline_range")

            except Exception as e:
                logging.warning(f"Could not retrieve full SEGY info: {e}", exc_info=True)
                st.warning(f"Could not retrieve full SEGY info: {e}")

            st.info(f"""
            **SEGY File Information:**
            - Sampling Interval: {st.session_state.dt*1000:.2f} ms
            - Number of Samples: {num_samples if num_samples > 0 else 'N/A'}
            - Time Range: {time_range_str}
            - Number of Traces: {st.session_state.trace_count}
            - Inline Range: {inline_range_str}
            - Crossline Range: {xline_range_str}
            """)

            st.subheader("Basemap")
            show_basemap_controls = st.checkbox("Show Basemap Controls", value=st.session_state.get('show_basemap', False), key="p2_show_basemap_controls_checkbox")
            st.session_state.show_basemap = show_basemap_controls # Update session state

            if show_basemap_controls:
                well_df_basemap = None
                if st.session_state.well_df is not None and not st.session_state.well_df.empty:
                    available_surfaces = get_surfaces(st.session_state.well_df)
                    
                    previous_selection = st.session_state.get('selected_surfaces_for_basemap', []).copy()
                    
                    selected_surfaces = st.multiselect(
                        "Select Surfaces for Basemap",
                        options=available_surfaces,
                        default=st.session_state.get('selected_surfaces_for_basemap', available_surfaces[:min(len(available_surfaces), 3)]),
                        key="p2_selected_surfaces_basemap"
                    )
                    if set(previous_selection) != set(selected_surfaces):
                        st.session_state.basemap_needs_update = True
                    st.session_state.selected_surfaces_for_basemap = selected_surfaces

                    st.session_state.basemap_detail_level = st.select_slider(
                        "Basemap Detail Level",
                        options=["low", "medium", "high"],
                        value=st.session_state.get('basemap_detail_level', 'low'),
                        help="Lower detail uses less memory but shows fewer features",
                        key="p2_basemap_detail_level"
                    )

                    if st.button("Generate Basemap", key="p2_generate_basemap_button"):
                        st.session_state.basemap_needs_update = True
                
                if st.session_state.get('basemap_needs_update', False) or (st.session_state.show_basemap and st.session_state.get('basemap_figure') is None):
                    try:
                        with st.spinner("Generating basemap..."):
                            if st.session_state.well_df is not None and not st.session_state.well_df.empty and st.session_state.get('selected_surfaces_for_basemap'):
                                well_df_basemap = st.session_state.well_df[
                                    st.session_state.well_df["Surface"].isin(st.session_state.selected_surfaces_for_basemap)
                                ]
                            detail_level = st.session_state.get('basemap_detail_level', 'low')
                            st.session_state.basemap_figure = plot_basemap_with_wells(
                                st.session_state.header_loader,
                                well_df_basemap,
                                detail_level=detail_level
                            )
                            st.session_state.basemap_needs_update = False
                    except Exception as e:
                        st.error(f"Could not generate basemap: {e}")
                        logging.error(f"Basemap generation failed: {e}", exc_info=True)
                        st.session_state.basemap_figure = None
                
                if st.session_state.show_basemap and st.session_state.get('basemap_figure') is not None:
                    st.plotly_chart(st.session_state.basemap_figure, use_container_width=True)
                    if st.button("Hide Basemap", key="p2_hide_basemap_button"):
                        st.session_state.show_basemap = False
                        st.rerun() # Rerun to hide the basemap immediately
            elif not show_basemap_controls and st.session_state.get('show_basemap'): # If checkbox unchecked, hide basemap
                 st.session_state.show_basemap = False
                 st.rerun()


        else:
            st.warning("SEG-Y headers not loaded yet.")
    # --- End Basemap Expander ---

    # --- Parameter Configuration Expander ---
    with st.expander("Calculate Defaults & Configure Display", expanded=st.session_state.get('stats_defaults') is not None):
        st.subheader("Parameter Configuration")
        col1, col2 = st.columns([1, 1])

        with col1:
            st.subheader("Statistics Settings")
            if st.session_state.header_loader:
                st.session_state.plot_settings['sample_percent'] = slider_with_number_input(
                    "Sample Percent for Statistics", 0.1, 100.0,
                    st.session_state.plot_settings.get('sample_percent', 1.0), 0.1,
                    "p2_sample_percent", format="%.1f"
                )
                st.session_state.plot_settings['max_traces_for_stats'] = st.number_input(
                    "Max Traces for Statistics", 1, 10000,
                    st.session_state.plot_settings.get('max_traces_for_stats', 50), 1,
                    key="p2_max_traces_stats"
                )
            else:
                st.warning("SEG-Y headers not loaded yet.")

        with col2:
            st.subheader("Spectral Parameters")
            sampling_freq = 1.0 / st.session_state.dt if hasattr(st.session_state, 'dt') and st.session_state.dt > 0 else 250.0

            st.session_state.plot_settings['use_band_limited'] = st.checkbox(
                "Use Band-Limited Descriptors", st.session_state.plot_settings.get('use_band_limited', False),
                key="p2_use_band_limited"
            )
            st.session_state.plot_settings['shape'] = slider_with_number_input(
                "Shape Parameter", 0.0, 2.0, st.session_state.plot_settings.get('shape', 0.35), 0.01,
                "p2_shape", format="%.2f"
            )
            st.session_state.plot_settings['kmax'] = slider_with_number_input(
                "Kmax Parameter", 0.0, sampling_freq, st.session_state.plot_settings.get('kmax', 120.0), 1.0,
                "p2_kmax", format="%.1f"
            )
            st.session_state.plot_settings['int_val'] = slider_with_number_input(
                "Int Val Parameter", 0.0, 60.0, st.session_state.plot_settings.get('int_val', 35.0), 0.1,
                "p2_int_val", format="%.1f"
            )
            st.session_state.plot_settings['b1'] = slider_with_number_input(
                "B1 Parameter (Hz)", 1.0, sampling_freq, st.session_state.plot_settings.get('b1', 5.0), 0.1,
                "p2_b1", format="%.1f"
            )
            st.session_state.plot_settings['b2'] = slider_with_number_input(
                "B2 Parameter (Hz)", 1.0, sampling_freq, st.session_state.plot_settings.get('b2', 40.0), 0.1,
                "p2_b2", format="%.1f"
            )
            st.session_state.plot_settings['p_bandwidth'] = slider_with_number_input(
                "P Bandwidth Parameter", 1.0, 3.0, st.session_state.plot_settings.get('p_bandwidth', 2.0), 0.01,
                "p2_p_bandwidth", format="%.2f"
            )
            st.session_state.plot_settings['roll_percent'] = slider_with_number_input(
                "Roll Percent Parameter", 0.0, 0.95, st.session_state.plot_settings.get('roll_percent', 0.80), 0.01,
                "p2_roll_percent", format="%.2f"
            )
            st.session_state.plot_settings['epsilon'] = st.number_input(
                "Epsilon (WOSS Numerical Stability)", 1e-10, 1e-2,
                st.session_state.plot_settings.get('epsilon', 1e-4), format="%.1e",
                help="Small value added for numerical stability in WOSS calculation. Default: 1e-4",
                key="p2_epsilon"
            )
            st.session_state.plot_settings['hfc_percentile'] = st.number_input(
                "HFC Percentile for WOSS Calculation", 0.0, 100.0,
                st.session_state.plot_settings.get('hfc_percentile', 95.0), 1.0, format="%.1f",
                help="Percentile of High Frequency Content (HFC) used for normalization in WOSS calculations. Default: 95.0",
                key="p2_hfc_percentile"
            )
            st.session_state.plot_settings['fdom_exponent'] = st.number_input(
                "Dominant Frequency Exponent (WOSS)", 0.0, 5.0,
                st.session_state.plot_settings.get('fdom_exponent', 2.0), 0.1, format="%.1f",
                help="Exponent applied to normalized dominant frequency in WOSS calculation. Default: 2.0",
                key="p2_fdom_exponent"
            )

        if st.button("Calculate Defaults & Configure Display", key="p2_calc_defaults"):
            if st.session_state.header_loader:
                try:
                    segy_path = st.session_state.segy_temp_file_path
                    with st.spinner("Calculating statistics and default parameters..."):
                        # Prepare spectral_params, ensuring all necessary keys are present
                        current_plot_settings = st.session_state.plot_settings
                        spectral_params = {
                            'use_band_limited': current_plot_settings.get('use_band_limited', False),
                            'shape': current_plot_settings.get('shape', 0.35),
                            'kmax': current_plot_settings.get('kmax', 120.0),
                            'int_val': current_plot_settings.get('int_val', 35.0),
                            'b1': current_plot_settings.get('b1', 5.0),
                            'b2': current_plot_settings.get('b2', 40.0),
                            'p_bandwidth': current_plot_settings.get('p_bandwidth', 2.0),
                            'roll_percent': current_plot_settings.get('roll_percent', 0.80),
                            'epsilon': current_plot_settings.get('epsilon', 1e-4),
                            'fdom_exponent': current_plot_settings.get('fdom_exponent', 2.0),
                            'hfc_percentile': current_plot_settings.get('hfc_percentile', 95.0),
                            'freq_max': current_plot_settings.get('freq_max', (0.5 / st.session_state.dt if st.session_state.dt > 0 else 125.0))
                        }
                        
                        defaults_data = calculate_stats_and_defaults(
                            segy_path,
                            st.session_state.header_loader,
                            st.session_state.dt,
                            current_plot_settings.get('sample_percent', 1.0),
                            current_plot_settings.get('max_traces_for_stats', 500),
                            **spectral_params
                        )
                    if defaults_data:
                        st.session_state.stats_defaults = defaults_data
                        # The 'defaults' dict from calculate_stats_and_defaults now has _cmap_min/_cmap_max keys
                        st.session_state.plot_settings.update(defaults_data.get('defaults', {}))
                        st.success("Default parameters calculated based on sample trace.")
                        logging.info("Calculated default parameters and statistics.")
                        # Store actual HFC percentile threshold value
                        user_hfc_percentile = current_plot_settings.get('hfc_percentile', 95.0)
                        actual_hfc_value = defaults_data['stats']['hfc'].get(f'p{int(user_hfc_percentile)}', None)
                        st.session_state.hfc_p95 = actual_hfc_value
                        st.session_state.display_params_configured = True # Set flag here
                        st.rerun()
                    else:
                        st.error("Failed to calculate default parameters.")
                except Exception as e:
                    st.error(f"Error calculating defaults: {e}")
                    logging.error(f"Default calculation failed: {e}", exc_info=True)
            else:
                st.warning("Load SEG-Y data first to calculate defaults.")

        if st.session_state.get('stats_defaults') is not None:
            if not st.session_state.get('display_params_configured'): # Ensure flag is set if stats_defaults exists
                 st.session_state.display_params_configured = True
                 logging.info("Display parameters flag set as stats_defaults exists.")

            tab_general, tab_spectral, tab_statistic = st.tabs(["General", "Spectral Descriptors", "Statistic"])

            with tab_general:
                st.subheader("Plot Limits Settings")
                st.markdown("### Time (Y-axis) Limits")
                col1_gen, col2_gen = st.columns(2)
                trace_length = st.session_state.stats_defaults.get('trace_length', 4.0)

                with col1_gen:
                    st.session_state.plot_settings['time_min'] = st.number_input(
                        "Time Min (seconds)", 0.0, trace_length,
                        st.session_state.plot_settings.get('time_min', 0.0), 0.1, "%.2f", key="p2_time_min_input"
                    )
                with col2_gen:
                    st.session_state.plot_settings['time_max'] = st.number_input(
                        "Time Max (seconds)", 0.0, trace_length * 1.5,
                        st.session_state.plot_settings.get('time_max', trace_length), 0.1, "%.2f", key="p2_time_max_input"
                    )

                st.markdown("### Frequency Limits for Spectrograms")
                st.markdown("These settings control the frequency range displayed in Magnitude Spectrogram and Magnitude*Voice plots.")
                nyquist_freq = 0.5 / st.session_state.dt if st.session_state.dt > 0 else 125.0
                col1_freq, col2_freq = st.columns(2)
                with col1_freq:
                    st.session_state.plot_settings['freq_min'] = st.number_input(
                        "Frequency Min (Hz)", 0.0, nyquist_freq,
                        st.session_state.plot_settings.get('freq_min', 0.0), 1.0, "%.1f", key="p2_freq_min_input"
                    )
                with col2_freq:
                    st.session_state.plot_settings['freq_max'] = st.number_input(
                        "Frequency Max (Hz)", 0.0, nyquist_freq,
                        st.session_state.plot_settings.get('freq_max', nyquist_freq), 1.0, "%.1f", key="p2_freq_max_input"
                    )
                st.info("These time and frequency limit settings will be transferred to the next step for plotting spectral descriptors.")
                st.session_state.plot_settings['Time (Y-axis)'] = (
                    st.session_state.plot_settings.get('time_min', 0.0),
                    st.session_state.plot_settings.get('time_max', trace_length)
                )
                st.session_state.plot_settings['Frequency'] = (
                    st.session_state.plot_settings.get('freq_min', 0.0),
                    st.session_state.plot_settings.get('freq_max', nyquist_freq)
                )

            with tab_spectral:
                st.subheader("Descriptor and Colormap Limits")
                st.info("Adjust display ranges and colormap scaling for each descriptor.")

                descriptor_settings_ref = {
                    "Input Signal": {"default": (-1, 1), "colormap": "RdBu"},
                    "High Frequency Content": {"default": (0, 2), "colormap": "rainbow"}, # Renamed from HFC
                    "Spectral Decrease": {"default": (0, 1), "colormap": "rainbow"},
                    "Spectral Slope": {"default": (0, 1), "colormap": "RdBu"},
                    "Magnitude Voice Slope": {"default": (-1, 1), "colormap": "RdBu"}, # Renamed from Mag*Voice Slope
                    "Spectral Bandwidth": {"default": (0, 50), "colormap": "rainbow"},
                    "Spectral Rolloff": {"default": (0, 50), "colormap": "rainbow"},
                    "Magnitude Spectrogram": {"default": (0, 1), "colormap": "rainbow"},
                    "Magnitude * Voice": {"default": (-1, 1), "colormap": "rainbow"},
                    "Normalized Dominant Frequency": {"default": (0, 100), "colormap": "rainbow"},
                    "Weighted Optimum Spectral Shape": {"default": (-3, 3), "colormap": "RdBu"} # Renamed from WOSS
                }
                available_colormaps = pcolors.named_colorscales()
                
                stats_data_map = st.session_state.stats_defaults.get('stats', {})

                # Refine descriptor_settings_ref defaults using stats
                for display_name_setting, settings_val in descriptor_settings_ref.items():
                    # Find corresponding key in stats_data_map (e.g., "Input Signal" in settings maps to "data" in stats)
                    internal_stat_key = None
                    # This mapping needs to be robust
                    if display_name_setting == "Input Signal": internal_stat_key = "data"
                    elif display_name_setting == "High Frequency Content": internal_stat_key = "hfc"
                    elif display_name_setting == "Spectral Decrease": internal_stat_key = "spec_decrease"
                    elif display_name_setting == "Spectral Slope": internal_stat_key = "spec_slope"
                    elif display_name_setting == "Magnitude Voice Slope": internal_stat_key = "mag_voice_slope"
                    elif display_name_setting == "Spectral Bandwidth": internal_stat_key = "spec_bandwidth"
                    elif display_name_setting == "Spectral Rolloff": internal_stat_key = "spec_rolloff"
                    elif display_name_setting == "Magnitude Spectrogram": internal_stat_key = "mag"
                    elif display_name_setting == "Magnitude * Voice": internal_stat_key = "mag_voice"
                    elif display_name_setting == "Normalized Dominant Frequency": internal_stat_key = "norm_fdom"
                    elif display_name_setting == "Weighted Optimum Spectral Shape": internal_stat_key = "WOSS"


                    if internal_stat_key and internal_stat_key in stats_data_map:
                        stat_values = stats_data_map[internal_stat_key]
                        try:
                            min_stat = float(stat_values.get('p05', stat_values.get('min', settings_val['default'][0])))
                            max_stat = float(stat_values.get('p95', stat_values.get('max', settings_val['default'][1])))

                            if display_name_setting in ['High Frequency Content', 'Spectral Decrease', 'Spectral Bandwidth', 'Spectral Rolloff']:
                                min_stat = 0.0
                            elif display_name_setting in ['Weighted Optimum Spectral Shape', 'Input Signal', 'Magnitude Voice Slope']:
                                abs_max_stat = float(stat_values.get('p95_abs', max(abs(min_stat), abs(max_stat))))
                                min_stat = -abs_max_stat
                                max_stat = abs_max_stat
                            settings_val['default'] = (min_stat, max_stat)
                        except (ValueError, KeyError, TypeError) as e:
                            logging.warning(f"Could not use stats to update default limits for {display_name_setting} (stat key: {internal_stat_key}): {e}")
                
                cols_spectral = st.columns(3)
                for idx, (descriptor, settings) in enumerate(descriptor_settings_ref.items()):
                    col = cols_spectral[idx % 3]
                    with col:
                        st.write(f"**{descriptor}**")
                        key_base = descriptor.lower().replace(' ', '_').replace('*', '').replace('/', '_')
                        if descriptor == "Weighted Optimum Spectral Shape": # New display name
                            key_base = "woss" # Standardized key_base for WOSS
                        
                        cmap_min_key_state = f"{key_base}_cmap_min"
                        cmap_max_key_state = f"{key_base}_cmap_max"
                        cmap_name_key_state = f"{key_base}_cmap_name"

                        default_min, default_max = settings['default']
                        current_min = st.session_state.plot_settings.get(cmap_min_key_state, default_min)
                        current_max = st.session_state.plot_settings.get(cmap_max_key_state, default_max)
                        # Determine the initial candidate for current_cmap
                        current_cmap = st.session_state.plot_settings.get(cmap_name_key_state, settings['colormap'])

                        # Ensure current_cmap is a valid choice from available_colormaps
                        if current_cmap not in available_colormaps:
                            # Try the default from 'settings' as a first fallback
                            current_cmap = settings['colormap']
                        
                        if current_cmap not in available_colormaps:
                            # If still not valid after trying settings['colormap'],
                            # and if there are available options, default to the first one.
                            if available_colormaps:
                                current_cmap = available_colormaps[0]
                            else:
                                # If there are no available_colormaps, selectbox will be empty.
                                # Set current_cmap to None; Streamlit's selectbox behavior with empty options
                                # and index=None would then apply. This avoids .index() error on a non-existent value,
                                # though the selectbox itself might error if options are empty.
                                # Given the error "X not in list", available_colormaps is not empty.
                                current_cmap = None

                        st.write("Display/Colormap Limits:")
                        st.session_state.plot_settings[cmap_min_key_state] = st.number_input(
                            "Min", float(current_min), key=f"p2_numin_{key_base}", format="%.3f"
                        )
                        st.session_state.plot_settings[cmap_max_key_state] = st.number_input(
                            "Max", float(current_max), key=f"p2_numax_{key_base}", format="%.3f"
                        )
                        st.session_state.plot_settings[cmap_name_key_state] = st.selectbox(
                            f"Colormap##{key_base}_cmap_select", available_colormaps, 
                            index=available_colormaps.index(current_cmap), key=f"p2_selcmap_{key_base}"
                        )
                        st.markdown("---")

            with tab_statistic:
                st.info("Percentiles calculated from sample trace:")
                stats_display = st.session_state.stats_defaults.get('stats', {})
                st.json(stats_display, expanded=False)
                st.session_state.plot_settings['section_colormap'] = st.selectbox(
                    "Colormap for Seismic Section Background", 
                    options=['gray', 'seismic', 'coolwarm', 'RdBu'],
                    index=['gray', 'seismic', 'coolwarm', 'RdBu'].index(st.session_state.plot_settings.get('section_colormap', 'gray')),
                    key="p2_section_colormap_selector"
                )

            st.markdown("---")
            if st.button("Confirm Settings & Proceed", key="p2_confirm_settings_button"):
                st.session_state.current_step = "select_mode" 
                st.session_state.display_params_configured = True
                logging.info("Settings confirmed, proceeding to mode selection.")
                st.rerun()
        else:
            st.info("Click 'Calculate Defaults & Configure Display' to proceed.")
    # --- End Parameter Configuration Expander ---

    # Add a "Start New Analysis" button to the sidebar
    st.sidebar.markdown("---")
    if st.sidebar.button("🔄 Start New Analysis", use_container_width=True, key="p2_start_new_analysis_sidebar_button"):
        reset_state()
        st.success("Starting new analysis. All temporary data has been cleared.")
        st.rerun()
