# WOSS Seismic Analysis Tool: Option 5 Workflow (By Polyline File Import)

This document details the Python scripts and code flow for the WOSS Seismic Analysis Tool when using Option 5: "By Polyline File Import" for selecting data. It focuses on the transition from Step 3 (Select Mode/Traces) to Step 4 (View Results) and the spectral descriptor calculation process.

## Key Python Files and Their Roles

The primary Python files involved in this workflow include:

1.  **`app_ref.py`**:
    *   **Role**: The main Streamlit application file. Manages UI, state (`st.session_state`), step-by-step flow, and orchestrates calls to other modules.
    *   **Key Functions**: Handles UI for polyline file upload, tolerance input, initiating trace finding along the polyline, triggering calculations, and displaying results as a 2D section along the polyline.

2.  **`data_utils.py`**:
    *   **Role**: Contains utility functions for data loading and preparation.
    *   **Key Functions**:
        *   `SegyHeaderLoader`: Essential for accessing trace coordinates (X, Y) and indices from the SEG-Y file.
        *   `load_trace_sample`: Loads seismic amplitude data for individual trace indices.

3.  **`utils.py`**:
    *   **Role**: Contains general utility functions, including those specific to polyline processing.
    *   **Key Functions**:
        *   `parse_polyline_string`: Parses a string (from an uploaded file) containing polyline coordinates (X, Y pairs) into a list of vertices.
        *   `find_traces_near_polyline`: Identifies SEG-Y traces that fall within a specified tolerance distance from the defined polyline. This is the core of trace selection for this mode.

4.  **`dlogst_spec_descriptor_gpu.py`**:
    *   **Role**: Contains core functions for calculating spectral descriptors using GPU acceleration.
    *   **Key Functions**:
        *   `dlogst_spec_descriptor_gpu_2d_chunked`: Processes a 2D array of traces (the collection of traces along the polyline) in chunks on the GPU. This is vital for efficient computation.

5.  **`processing.py`**:
    *   **Role**: Contains functions for data processing tasks.
    *   **Key Functions**:
        *   `calculate_woss`: Calculates the WOSS attribute from its constituent spectral descriptors.

6.  **`visualization.py`**:
    *   **Role**: Handles the creation of plots.
    *   **Key Functions**:
        *   `plot_multi_trace_section`: Generates 2D plots for the seismic section along the polyline, displaying selected spectral attributes.

## Workflow: Step 3 (Select Mode) to Step 4 (View Results) - Option 5

The workflow for analyzing data along a user-defined polyline is managed within `app_ref.py`.

### 1. Step 3: Select Analysis Mode (`app_ref.py`)

*   The application is at `st.session_state.current_step == "select_mode"`.
*   The user selects `"By Polyline File Import"` from the "Select Mode" dropdown.
    ```python
    # In app_ref.py, within the "select_mode" step:
    st.session_state.selection_mode = st.sidebar.selectbox(
        "Select Mode",
        options=[
            # ... other options ...
            "By Polyline File Import"
        ],
        # ...
    )
    ```
*   **Polyline File Upload and Tolerance**:
    *   A file uploader allows the user to select a polyline file (e.g., `.txt`, `.csv` containing X, Y coordinates). The file info is stored in `st.session_state.polyline_file_info`.
    *   A slider allows the user to set `st.session_state.polyline_tolerance`, which defines the maximum distance from the polyline for a trace to be included.
*   The user clicks the "Proceed" button:
    ```python
    # In app_ref.py, at the end of the "select_mode" step:
    if st.button("Proceed", key="proceed_button"):
        st.session_state.current_step = "select_traces"
        st.rerun()
    ```

### 2. Step 3.5: Select Traces (Process Polyline and Prepare for Analysis) (`app_ref.py`)

The application is at `st.session_state.current_step == "select_traces"`.

*   **Parse Polyline File**:
    *   The content of the uploaded polyline file (`st.session_state.polyline_file_info['buffer']`) is read and decoded.
    *   `parse_polyline_string(polyline_content)` (from `utils.py`) is called to convert the text-based coordinates into a list of (X, Y) vertex tuples, stored in `st.session_state.polyline_vertices`.
    ```python
    # In app_ref.py, within "select_traces" for "By Polyline File Import":
    if st.session_state.polyline_file_info is not None:
        # ...
        polyline_content = st.session_state.polyline_file_info['buffer'].getvalue().decode('utf-8')
        polyline_vertices = parse_polyline_string(polyline_content)
        st.session_state.polyline_vertices = polyline_vertices
    ```

*   **Find Traces Near Polyline**:
    *   `find_traces_near_polyline(st.session_state.header_loader, st.session_state.polyline_vertices, st.session_state.polyline_tolerance)` (from `utils.py`) is called.
        *   **Inputs**: The `header_loader` (containing all trace locations), the parsed `polyline_vertices`, and the `polyline_tolerance`.
        *   **Process**: This function iterates through each trace in the `header_loader`. For each trace, it calculates the shortest distance from the trace's (X,Y) coordinate to any segment of the polyline. If this distance is less than or equal to the `tolerance`, the trace's index is selected.
        *   **Output**: A list of trace indices that are near the polyline. These are stored in `st.session_state.selected_indices`.
    ```python
    # In app_ref.py, within "select_traces" for "By Polyline File Import":
    # ... (after parsing polyline) ...
    selected_indices = find_traces_near_polyline(
        st.session_state.header_loader,
        polyline_vertices,
        st.session_state.polyline_tolerance
    )
    st.session_state.selected_indices = selected_indices
    ```

*   **Loading Trace Data for the Polyline Section**:
    *   A button "Load Traces for Processing" appears if traces are found.
    *   When clicked, the application iterates through `st.session_state.selected_indices`.
    *   For each `trace_idx` along the polyline:
        1.  `load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)` (from `data_utils.py`) loads the seismic amplitude data.
        2.  This data (as a dictionary item like `{'trace_sample': sample_array, 'trace_idx': idx}`) is collected into `st.session_state.loaded_trace_data`.
    *   A flag `st.session_state.traces_loaded_polyline` is set to `True`.
    ```python
    # In app_ref.py, within "select_traces" for "By Polyline File Import":
    if not st.session_state.traces_loaded_polyline:
        if st.button("Load Traces for Processing", key="load_traces_polyline"):
            with st.spinner(f"Loading {len(selected_indices)} traces near polyline..."):
                loaded_data_temp = []
                try:
                    for idx in st.session_state.selected_indices:
                        trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, idx)
                        loaded_data_temp.append({'trace_sample': trace_sample, 'trace_idx': idx})
                    st.session_state.loaded_trace_data = loaded_data_temp
                    st.session_state.traces_loaded_polyline = True
                    # ... logging and rerun ...
    ```

*   **Select Outputs for Display**:
    *   A multiselect widget allows the user to choose which spectral descriptors to calculate and display for the section along the polyline. Options are from `AVAILABLE_OUTPUTS_SECTION`.
    *   Choices are stored in `st.session_state.selected_outputs`.

### 3. Spectral Descriptor Calculation for the Polyline Section (`app_ref.py` within "select_traces")

*   The "Calculate Descriptors" button becomes active once traces are loaded and outputs are selected.
*   When clicked:
    1.  Spectral parameters are retrieved from `st.session_state.plot_settings`.
    2.  The `st.session_state.loaded_trace_data` (list of 1D trace arrays) is stacked into a 2D numpy array, representing the seismic section along the polyline. Care is taken to pad traces if they have varying lengths.
    3.  If `GPU_AVAILABLE` and `st.session_state.batch_size` is set, `dlogst_spec_descriptor_gpu_2d_chunked` (from `dlogst_spec_descriptor_gpu.py`) is called.
        *   **Inputs**: The 2D section data, `st.session_state.dt`, `st.session_state.batch_size`, a list of internal names for the `st.session_state.selected_outputs`, and spectral parameters.
        *   **Process**: This function processes the 2D section in batches on the GPU, computing each selected attribute across all traces along the polyline.
        *   **Output**: A dictionary where keys are internal descriptor names (e.g., `'WOSS'`, `'spec_slope'`) and values are 2D numpy arrays (shape: `num_samples` x `num_traces_along_polyline`) representing the calculated attribute section.
    4.  The WOSS attribute is explicitly calculated using its components if "WOSS" is selected.
    5.  The resulting dictionary of 2D attribute sections is stored in `st.session_state.calculated_descriptors`.
    6.  `st.session_state.analysis_complete` is set to `True`.

    ```python
    # In app_ref.py, within "select_traces" for "By Polyline File Import":
    # (Similar to the calculation block for Option 2/3, but using traces_loaded_polyline flag)
    if st.button("Calculate Descriptors", key="calculate_descriptors_polyline", ...):
        with st.spinner("Calculating spectral descriptors for the polyline section..."):
            # ... (Prepare 2D array from st.session_state.loaded_trace_data as in Option 2/3)
            # trace_samples_list = [item['trace_sample'] for item in st.session_state.loaded_trace_data]
            # max_len = max(len(t) for t in trace_samples_list)
            # padded_traces = [np.pad(t, (0, max_len - len(t)), 'constant') for t in trace_samples_list]
            # section_data_2d = np.stack(padded_traces, axis=-1)

            # ... (Get spectral_params and internal_outputs_to_calculate as in Option 2/3) ...
            
            if GPU_AVAILABLE and st.session_state.batch_size:
                calculated_attribute_sections = dlogst_spec_descriptor_gpu_2d_chunked(
                    section_data_2d,
                    st.session_state.dt,
                    st.session_state.batch_size,
                    attributes_to_calculate=outputs_for_gpu_calc, # Internal names
                    **spectral_params
                )
            else:
                # ... (Fallback to trace-by-trace processing as in Option 2/3) ...
            
            # ... (Add original data if selected, as in Option 2/3) ...
            # ... (Explicitly calculate WOSS if selected, as in Option 2/3) ...

            st.session_state.calculated_descriptors = calculated_attribute_sections
            st.session_state.analysis_complete = True
            # ... success message and rerun ...
    ```

### 4. Transition to Step 4: View Results (`app_ref.py`)

*   Once `st.session_state.analysis_complete` is `True`, navigation (e.g., "View Results" button if implemented, or automatic progression if `st.rerun()` was called after calculation) leads to the results display.
*   The application state changes: `st.session_state.current_step = "view_results"`.
*   **Displaying Polyline Section Plots**:
    *   The user can select which calculated attribute sections (from `st.session_state.selected_outputs`) to display.
    *   `plot_multi_trace_section` (from `visualization.py`) is called for each selected attribute.
    *   **Inputs**:
        *   The 2D attribute array (e.g., `st.session_state.calculated_descriptors['WOSS']`).
        *   `st.session_state.dt` (sampling interval).
        *   Plot settings from `st.session_state.plot_settings` (e.g., colormaps, amplitude limits, time limits).
        *   Trace header information for X-axis labeling (e.g., sequential trace number along the polyline, distance along polyline, or actual X/Y coordinates if a map-view projection is used for the x-axis). This information can be derived from `st.session_state.header_loader` using `st.session_state.selected_indices`.
    *   **Output**: A Plotly figure object showing the 2D attribute section along the polyline, rendered using `st.plotly_chart()`.

## Data Flow Summary (Option 5)

1.  **Initial Data (Step 1)**:
    *   SEG-Y -> `st.session_state.header_loader`, `st.session_state.dt`, `st.session_state.segy_temp_file_path`.

2.  **Parameter Configuration (Step 2)**:
    *   User inputs -> `st.session_state.plot_settings`.

3.  **Mode and Trace Selection (Step 3 & 3.5)**:
    *   User selects "By Polyline File Import", uploads file, sets tolerance -> `st.session_state.selection_mode`, `st.session_state.polyline_file_info`, `st.session_state.polyline_tolerance`.
    *   Polyline file content -> `parse_polyline_string` -> `st.session_state.polyline_vertices`.
    *   `header_loader` + `polyline_vertices` + `polyline_tolerance` -> `find_traces_near_polyline` -> `st.session_state.selected_indices`.
    *   `selected_indices` + `segy_temp_file_path` -> `load_trace_sample` (loop) -> `st.session_state.loaded_trace_data`.
    *   User selects outputs -> `st.session_state.selected_outputs`.

4.  **Descriptor Calculation for Polyline Section (Step 3.5)**:
    *   `st.session_state.loaded_trace_data` (stacked to 2D array) + `dt` + `plot_settings` + `batch_size` + `selected_outputs` -> `dlogst_spec_descriptor_gpu_2d_chunked`.
    *   Result -> `st.session_state.calculated_descriptors` (dictionary of 2D attribute arrays).

5.  **Viewing Results (Step 4)**:
    *   `st.session_state.calculated_descriptors` (specific 2D attribute array) + `selected_outputs` + `plot_settings` + `dt` + header info -> `plot_multi_trace_section` -> Plotly Figure (2D section plot along polyline).

This workflow enables flexible analysis along arbitrary paths defined by the user, leveraging GPU capabilities for efficient computation of spectral attributes on the selected traces.
