# -*- coding: utf-8 -*-
"""
Created on Tue Mar 11 16:03:13 2025

@author: devri.agustianto

WOSS (Weighted-Optimum Spectral Shape) Seismic Analysis Tool

This script provides a comprehensive GUI-based application for analyzing 3D seismic data using
spectral decomposition and the WOSS (Weighted-Optimum Spectral Shape) attribute. The tool supports
GPU-accelerated processing for improved performance and includes functionality for:

1. Loading and visualizing SEG-Y seismic data with proper header mapping
2. Interactive basemap visualization with inline/crossline navigation
3. Well marker integration and correlation with seismic data
4. Spectral attribute calculation including:
   - Dominant frequency analysis
   - High Frequency Content (HFC)
   - Spectral decrease/slope
   - Magnitude*Voice analysis
   - WOSS attribute calculation
5. Flexible trace selection methods (manual, well-based, polyline, inline/crossline)
6. Batch processing capabilities for large datasets
7. Export functionality for calculated attributes

The application leverages GPU acceleration through the dlogst_spec_descriptor_gpu module
for efficient spectral decomposition of large 3D seismic volumes.

------------------------------------------------------------------------------------------
Code Structure Overview:
------------------------------------------------------------------------------------------
1. Imports and Setup (Lines 1-90):
   - Core libraries, GUI components, and custom exception handling
   - GPU-accelerated spectral descriptor module import

2. Data Management (Lines 90-300):
   - SEG-Y file merging utility
   - SEG-Y header loading and processing
   - Well data management functions

3. Visualization Functions (Lines 300-700):
   - Basemap plotting with well markers
   - Interactive basemap with trace selection
   - Polyline selection interface

4. Data Processing (Lines 700-1500):
   - Trace loading and sampling
   - Spectral descriptor calculation
   - Parameter configuration dialogs
   - Statistical analysis

5. Plotting and Display (Lines 1500-2600):
   - Seismic section visualization
   - Spectral attribute plotting
   - Interactive UI components

6. Utility Functions (Lines 2600-3100):
   - Polyline handling
   - Distance calculations
   - GPU memory management
   - Attribute selection

7. Main Application Flow (Lines 3100-end):
   - User interface initialization
   - File loading and parameter setting
   - Trace selection options
   - Processing and visualization loop
   - Export functionality
"""
# Core libraries for numerical processing, data handling, and visualization
import torch  # PyTorch for GPU acceleration
import numpy as np  # Numerical operations and array handling
import segyio  # SEG-Y seismic data file I/O

# GUI components
import tkinter as tk
from tkinter import filedialog, simpledialog, ttk, messagebox

# Data processing and visualization
from tqdm import tqdm  # Progress bars
import pandas as pd  # Data manipulation and analysis
import plotly.graph_objects as go  # Interactive plotting
from plotly.subplots import make_subplots

# System utilities
import sys
import math
import os

# Custom exception handler to ensure proper error display in GUI environments
def custom_excepthook(exc_type, exc_value, exc_traceback):
    """Custom exception handler that sanitizes error messages for GUI display.

    This function replaces backticks with single quotes in error messages to prevent
    formatting issues in GUI message boxes and ensures all exceptions are properly logged.

    Args:
        exc_type: Exception type
        exc_value: Exception value/message
        exc_traceback: Exception traceback object
    """
    try:
        # Replace any backticks in error messages with quotes
        if isinstance(exc_value, str):
            exc_value = exc_value.replace("`", "'")
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
    except Exception as e:
        print(f"Error in custom exception handler: {e}")
        sys.__excepthook__(exc_type, exc_value, exc_traceback)

# Register the custom exception handler
sys.excepthook = custom_excepthook

# Import GPU-accelerated spectral descriptor functions from merged module
try:
    # 1D GPU descriptor function
    from dlogst_spec_descriptor_gpu_init import dlogst_spec_descriptor_gpu as dlogst_spec_descriptor
    # 2D chunked descriptor function for default batch processing
    from dlogst_spec_descriptor_gpu_init import dlogst_spec_descriptor_gpu_2d_chunked as dlogst_spec_descriptor_batch
except ImportError as e:
    print(f"Error: Could not import descriptor functions from 'dlogst_spec_descriptor_gpu_2d_merged_f32_pinmem.py': {e}")
    exit(1)

# SEG-Y File Merging Utility
def merge_segy_batch_files(batch_files, output_file):
    """
    Merge multiple SEG-Y batch files into a single SEG-Y file.

    This function combines multiple SEG-Y files (typically from batch processing)
    into a single consolidated SEG-Y file while preserving header information.
    It verifies compatibility between files and handles trace concatenation.

    Args:
        batch_files: List of batch SEG-Y file paths to merge
        output_file: Path to the output merged SEG-Y file

    Returns:
        bool: True if merge was successful, False otherwise
    """
    if not batch_files:
        print("No batch files provided for merging.")
        return False

    print(f"Merging {len(batch_files)} batch files into {output_file}...")

    # Get total trace count and ensure all files have compatible specifications
    total_traces = 0
    specs = None

    for batch_file in batch_files:
        try:
            with segyio.open(batch_file, 'r', ignore_geometry=True) as f:
                if specs is None:
                    # Store specifications from first file
                    specs = segyio.tools.metadata(f)
                else:
                    # Verify compatibility with first file
                    new_specs = segyio.tools.metadata(f)
                    if (len(new_specs.samples) != len(specs.samples) or
                        not np.array_equal(new_specs.samples, specs.samples)):
                        print(f"Error: Incompatible sample specifications in {batch_file}")
                        return False

                # Add trace count from this file
                file_traces = f.tracecount
                total_traces += file_traces
        except Exception as e:
            print(f"Error reading batch file {batch_file}: {e}")
            return False

    if specs is None:
        print("Could not obtain specifications from batch files.")
        return False

    # Set the trace count for the merged file
    specs.tracecount = total_traces

    try:
        with segyio.create(output_file, specs) as dst:
            # Copy binary header from first file
            with segyio.open(batch_files[0], 'r', ignore_geometry=True) as src:
                dst.bin = src.bin

                # Copy text header if available
                try:
                    dst.text[0] = src.text[0]
                except:
                    pass

            # Copy traces and headers from all batch files
            dst_trace_index = 0
            for batch_file in tqdm(batch_files, desc=f"Merging batch files"):
                with segyio.open(batch_file, 'r', ignore_geometry=True) as src:
                    for src_trace_index in range(src.tracecount):
                        dst.header[dst_trace_index] = src.header[src_trace_index]
                        dst.trace[dst_trace_index] = src.trace[src_trace_index]
                        dst_trace_index += 1

        print(f"Successfully merged {len(batch_files)} batch files into {output_file} with {total_traces} traces.")
        return True

    except Exception as e:
        print(f"Error during merge: {e}")
        return False

# SEG-Y Header Management
class SegyHeaderLoader:
    """
    Handles loading and processing of SEG-Y file headers.

    This class extracts header information from SEG-Y files, including inline/crossline
    numbers and coordinate data. It provides functionality to customize header byte
    locations and scaling factors through a GUI interface.
    """
    def __init__(self, segy_path):
        """Initialize with path to SEG-Y file and load headers.

        Args:
            segy_path: Path to the SEG-Y file
        """
        self.segy_path = segy_path
        self.load_headers()

    def get_custom_bytes_and_scaler(self):
        """
        Display a dialog for the user to specify SEG-Y header byte locations and scaling factor.

        This method creates a GUI dialog allowing users to customize the byte locations
        for inline, crossline, X and Y coordinates, and the coordinate scaling factor.

        Returns:
            tuple: (inline_byte, crossline_byte, x_byte, y_byte, scaler) as integers
        """
        dialog = tk.Toplevel(root)
        dialog.title("Enter Header Bytes and Scaler")
        labels = ["Inline Byte", "Crossline Byte", "X Byte", "Y Byte", "Scaler"]
        defaults = ["189", "193", "181", "185", "1"]  # Standard SEG-Y header byte positions
        entries = []
        for i, label_text in enumerate(labels):
            tk.Label(dialog, text=label_text).grid(row=i, column=0, padx=10, pady=5, sticky="e")
            entry = tk.Entry(dialog)
            entry.insert(0, defaults[i])
            entry.grid(row=i, column=1, padx=10, pady=5, sticky="w")
            entries.append(entry)
        def on_ok():
            dialog.custom_values = [entry.get() for entry in entries]
            dialog.destroy()
        tk.Button(dialog, text="OK", command=on_ok).grid(row=len(labels), column=0, columnspan=2, pady=10)
        dialog.wait_window()
        try:
            return map(int, dialog.custom_values)
        except:
            print("Error parsing input, using defaults: 189,193,181,185,1")
            return 189, 193, 181, 185, 1

    def load_headers(self):
        """
        Load and process SEG-Y header information.

        This method extracts inline/crossline numbers and coordinates from the SEG-Y file,
        applies appropriate scaling, and identifies unique trace locations to handle
        duplicate coordinates.
        """
        with segyio.open(self.segy_path, 'r', ignore_geometry=True) as segyfile:
            print("\nLoading SEG-Y header information...")
            # Get header byte positions from user input
            inline_byte, crossline_byte, x_byte, y_byte, scaler = self.get_custom_bytes_and_scaler()

            # Extract header values with progress bars
            self.inlines = np.array([h[inline_byte] for h in tqdm(segyfile.header, desc="Loading inlines")])
            self.crosslines = np.array([h[crossline_byte] for h in tqdm(segyfile.header, desc="Loading crosslines")])
            self.x_coords = np.array([h[x_byte] for h in tqdm(segyfile.header, desc="Loading X coordinates")])
            self.y_coords = np.array([h[y_byte] for h in tqdm(segyfile.header, desc="Loading Y coordinates")])

            # Apply coordinate scaling factor
            if scaler >= 0:
                self.x_coords *= scaler
                self.y_coords *= scaler
            else:
                self.x_coords /= abs(scaler)
                self.y_coords /= abs(scaler)

        # Identify unique trace locations (handle duplicate coordinates)
        xy = np.column_stack((self.x_coords, self.y_coords))
        _, unique_idx = np.unique(xy, axis=0, return_index=True)
        self.unique_indices = unique_idx

        # Filter arrays to keep only unique trace locations
        self.x_coords = self.x_coords[unique_idx]
        self.y_coords = self.y_coords[unique_idx]
        self.inlines = self.inlines[unique_idx]
        self.crosslines = self.crosslines[unique_idx]

# Well Data Management Functions
def load_excel_data(excel_file_path):
    """
    Load well marker data from Excel file.

    This function loads well marker data from an Excel file and validates
    that it contains the required columns for well-seismic integration.

    Args:
        excel_file_path: Path to Excel file containing well marker data

    Returns:
        pandas.DataFrame: DataFrame containing well marker data

    Raises:
        ValueError: If required columns are missing from the Excel file
    """
    df = pd.read_excel(excel_file_path)
    expected_cols = ["X", "Y", "Z", "MD", "Surface", "Well"]
    for col in expected_cols:
        if col not in df.columns:
            raise ValueError(f"Missing expected column '{col}' in Excel file.")
    return df

def select_surfaces(df, title="Select Surfaces"):
    """
    Display a dialog for selecting geological surfaces from well data.

    This function creates a GUI dialog with a multi-select listbox allowing
    users to choose which geological surfaces to include in the analysis.

    Args:
        df: DataFrame containing well marker data
        title: Title for the selection window

    Returns:
        list: List of selected surface names
    """
    unique_surfaces = sorted(df["Surface"].unique().astype(str))
    selection_window = tk.Toplevel(root)
    selection_window.title(title)
    selection_window.geometry("300x400")
    tk.Label(selection_window, text="Select surface(s) to include:").pack(pady=5)
    frame = tk.Frame(selection_window)
    frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    scrollbar = tk.Scrollbar(frame, orient="vertical")
    listbox = tk.Listbox(frame, selectmode=tk.MULTIPLE, yscrollcommand=scrollbar.set, width=40, height=15)
    scrollbar.config(command=listbox.yview)
    scrollbar.pack(side="right", fill="y")
    listbox.pack(side="left", fill="both", expand=True)
    for surf in unique_surfaces:
        listbox.insert(tk.END, surf)
    selected_indices = []
    def on_ok():
        nonlocal selected_indices
        selected_indices = listbox.curselection()
        selection_window.destroy()
    tk.Button(selection_window, text="OK", command=on_ok).pack(pady=10)
    selection_window.wait_window()
    return [unique_surfaces[i] for i in selected_indices]

# Visualization Functions
def plot_basemap_with_wells(header_loader, well_df=None, segy_path=None):
    """
    Create a basemap visualization of the seismic survey with well locations.

    This function generates a Plotly figure showing the seismic survey geometry
    with inline/crossline grid lines and well marker positions. It also displays
    survey statistics such as trace count and inline/crossline ranges.

    Args:
        header_loader: SegyHeaderLoader object containing survey geometry
        well_df: DataFrame containing well marker information (optional)
        segy_path: Path to SEG-Y file for additional metadata (optional)

    Returns:
        Plotly Figure object with the basemap visualization
    """
    # Create base figure
    fig = go.Figure()

    # Add inline grid lines (decimated for performance)
    unique_inlines = np.unique(header_loader.inlines)
    for il in unique_inlines[::5]:  # Plot every 5th inline
        mask = header_loader.inlines == il
        fig.add_trace(go.Scatter(
            x=header_loader.x_coords[mask],
            y=header_loader.y_coords[mask],
            mode='lines',
            line=dict(color='grey', width=0.5),
            opacity=0.3,
            showlegend=False
        ))

    # Add crossline grid lines (decimated for performance)
    unique_crosslines = np.unique(header_loader.crosslines)
    for xl in unique_crosslines[::5]:  # Plot every 5th crossline
        mask = header_loader.crosslines == xl
        fig.add_trace(go.Scatter(
            x=header_loader.x_coords[mask],
            y=header_loader.y_coords[mask],
            mode='lines',
            line=dict(color='grey', width=0.5),
            opacity=0.3,
            showlegend=False
        ))

    # Add all trace locations as markers
    fig.add_trace(go.Scatter(
        x=header_loader.x_coords,
        y=header_loader.y_coords,
        mode='markers',
        marker=dict(color='grey', size=1),
        opacity=0.5,
        name='Seismic Trace Locations'
    ))
    if well_df is not None and not well_df.empty:
        for _, row in well_df.iterrows():
            fig.add_trace(go.Scatter(x=[row["X"]], y=[row["Y"]], mode='markers+text', marker=dict(color='red', size=8), text=f"{row['Well']} ({row['Surface']})", textposition="bottom right", name=f"{row['Well']} ({row['Surface']})"))

    # Calculate statistics
    total_traces = len(header_loader.x_coords)
    inline_min, inline_max = np.min(header_loader.inlines), np.max(header_loader.inlines)
    crossline_min, crossline_max = np.min(header_loader.crosslines), np.max(header_loader.crosslines)

    # Get time range if segy_path is provided
    time_stats = ""
    if segy_path:
        try:
            with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
                dt = segyfile.bin[segyio.BinField.Interval] / 1_000_000  # Convert to seconds
                num_samples = segyfile.samples.size
                time_range = (0, (num_samples - 1) * dt)
                time_stats = f"Time Range: {time_range[0]:.3f}s - {time_range[1]:.3f}s<br>"
        except Exception as e:
            print(f"Could not read time range from SEG-Y: {e}")

    # Create statistics annotation
    stats_text = (
        f"Survey Statistics:<br>"
        f"Total Traces: {total_traces:,}<br>"
        f"Inline Range: {inline_min} - {inline_max}<br>"
        f"Crossline Range: {crossline_min} - {crossline_max}<br>"
        f"{time_stats}"
    )

    # Add annotation with statistics
    fig.add_annotation(
        x=0.02,
        y=0.98,
        xref="paper",
        yref="paper",
        text=stats_text,
        showarrow=False,
        font=dict(size=12),
        bgcolor="rgba(255, 255, 255, 0.8)",
        bordercolor="black",
        borderwidth=1,
        borderpad=4,
        align="left"
    )

    x_range = np.ptp(header_loader.x_coords)
    y_range = np.ptp(header_loader.y_coords)
    aspect_ratio = x_range / y_range if y_range != 0 else 1
    fig.update_layout(title="Seismic Survey Geometry with Well Markers (Raw Coordinates)", xaxis_title="X Coordinate", yaxis_title="Y Coordinate", showlegend=True, width=800, height=600, yaxis=dict(scaleanchor="x", scaleratio=1/aspect_ratio))
    fig.show()

def plot_interactive_basemap(header_loader, well_df=None, segy_path=None, highlight_indices=None, polyline_vertices=None):
    """
    Create an interactive basemap that shows trace indices on hover and click.

    Args:
        header_loader: The SegyHeaderLoader object
        well_df: DataFrame containing well information
        segy_path: Path to the SEG-Y file
        highlight_indices: List of trace indices to highlight
        polyline_vertices: List of (x,y) tuples for polyline vertices
    """
    # Create the base figure
    fig = go.Figure()

    # Add inline/crossline grid for reference (reduced opacity)
    unique_inlines = np.unique(header_loader.inlines)
    for il in unique_inlines[::5]:
        mask = header_loader.inlines == il
        fig.add_trace(go.Scatter(
            x=header_loader.x_coords[mask],
            y=header_loader.y_coords[mask],
            mode='lines',
            line=dict(color='grey', width=0.5),
            opacity=0.2,  # Reduced opacity
            showlegend=False,
            hoverinfo='skip'  # Disable hover for grid lines
        ))

    unique_crosslines = np.unique(header_loader.crosslines)
    for xl in unique_crosslines[::5]:
        mask = header_loader.crosslines == xl
        fig.add_trace(go.Scatter(
            x=header_loader.x_coords[mask],
            y=header_loader.y_coords[mask],
            mode='lines',
            line=dict(color='grey', width=0.5),
            opacity=0.2,  # Reduced opacity
            showlegend=False,
            hoverinfo='skip'  # Disable hover for grid lines
        ))

    # --- Add Decimation Logic ---
    num_unique_traces = len(header_loader.x_coords)
    # Aim for a maximum number of points to display for performance, e.g., 50k-100k
    target_points = 75000
    step = max(1, num_unique_traces // target_points)
    decimated_indices = np.arange(0, num_unique_traces, step)
    print(f"Basemap: Displaying {len(decimated_indices)} out of {num_unique_traces} trace locations (decimation step={step}) for performance.")
    # --- End Decimation Logic ---

    # Create hover text only for decimated points
    hover_text = []
    for i in decimated_indices: # Iterate over decimated indices
        trace_idx = header_loader.unique_indices[i]
        inline = header_loader.inlines[i]
        crossline = header_loader.crosslines[i]
        hover_text.append(f"Trace: {trace_idx}<br>Inline: {inline}<br>Crossline: {crossline}<br>X: {header_loader.x_coords[i]:.2f}<br>Y: {header_loader.y_coords[i]:.2f}") # Added X,Y

    # Add decimated trace points with hover info
    fig.add_trace(go.Scatter(
        x=header_loader.x_coords[decimated_indices], # Use decimated coordinates
        y=header_loader.y_coords[decimated_indices], # Use decimated coordinates
        mode='markers',
        marker=dict(color='blue', size=2), # Smaller markers might be better
        opacity=0.6,
        name=f'Seismic Traces (1/{step} shown)', # Update name
        hoverinfo='text',
        hovertext=hover_text, # Use decimated hover text
        customdata=header_loader.unique_indices[decimated_indices] # Store original indices for decimated points
    ))

    # Highlight specific trace indices if provided
    if highlight_indices and len(highlight_indices) > 0:
        # Find positions of these indices within unique_indices
        highlight_positions = []
        valid_highlight_indices = []

        for idx in highlight_indices:
            positions = np.where(header_loader.unique_indices == idx)[0]
            if len(positions) > 0:
                highlight_positions.append(positions[0])
                valid_highlight_indices.append(idx)

        if highlight_positions:
            highlight_x = [header_loader.x_coords[pos] for pos in highlight_positions]
            highlight_y = [header_loader.y_coords[pos] for pos in highlight_positions]

            # Create hover text for highlighted points
            highlight_hover = [f"Selected Trace: {idx}<br>Inline: {header_loader.inlines[pos]}<br>Crossline: {header_loader.crosslines[pos]}"
                              for idx, pos in zip(valid_highlight_indices, highlight_positions)]

            fig.add_trace(go.Scatter(
                x=highlight_x,
                y=highlight_y,
                mode='markers+text',
                marker=dict(color='green', size=10, symbol='circle-open', line=dict(width=2)),
                text=[str(idx) for idx in valid_highlight_indices],
                textposition="top center",
                textfont=dict(size=10, color="darkgreen"),
                name='Selected Indices',
                hoverinfo='text',
                hovertext=highlight_hover
            ))

    # Add well information if provided
    if well_df is not None and not well_df.empty:
        for _, row in well_df.iterrows():
            fig.add_trace(go.Scatter(
                x=[row["X"]],
                y=[row["Y"]],
                mode='markers+text',
                marker=dict(color='red', size=8),
                text=f"{row['Well']} ({row['Surface']})",
                textposition="bottom right",
                name=f"{row['Well']} ({row['Surface']})"
            ))

    # Calculate statistics
    total_traces = len(header_loader.x_coords)
    inline_min, inline_max = np.min(header_loader.inlines), np.max(header_loader.inlines)
    crossline_min, crossline_max = np.min(header_loader.crosslines), np.max(header_loader.crosslines)

    # Get time range if segy_path is provided
    time_stats = ""
    if segy_path:
        try:
            with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
                dt = segyfile.bin[segyio.BinField.Interval] / 1_000_000  # Convert to seconds
                num_samples = segyfile.samples.size
                time_range = (0, (num_samples - 1) * dt)
                time_stats = f"Time Range: {time_range[0]:.3f}s - {time_range[1]:.3f}s<br>"
        except Exception as e:
            print(f"Could not read time range from SEG-Y: {e}")

    # Create statistics annotation
    stats_text = (
        f"Survey Statistics:<br>"
        f"Total Traces: {total_traces:,}<br>"
        f"Inline Range: {inline_min} - {inline_max}<br>"
        f"Crossline Range: {crossline_min} - {crossline_max}<br>"
        f"{time_stats}"
    )

    # Add polyline coordinates to statistics if provided
    if polyline_vertices and len(polyline_vertices) > 0:
        stats_text += "<br><br>Polyline Vertices:<br>"
        for i, (x, y) in enumerate(polyline_vertices):
            stats_text += f"pt{i+1} x: {x:.2f} y: {y:.2f}<br>"

    # Add annotation with statistics
    fig.add_annotation(
        x=0.02,
        y=0.98,
        xref="paper",
        yref="paper",
        text=stats_text,
        showarrow=False,
        font=dict(size=12),
        bgcolor="rgba(255, 255, 255, 0.8)",
        bordercolor="black",
        borderwidth=1,
        borderpad=4,
        align="left"
    )

    # --- Modify Instructions Annotation ---
    instructions_text = (
        "Interactive Controls:<br>"
        "• Hover over points to see trace info (X, Y, Index)<br>"
        "• Click on points to display trace index<br>"
        "• Use X,Y coordinates to define a polyline (option 5)" # Updated instruction
    )

    fig.add_annotation(
        x=0.02,
        y=0.82,
        xref="paper",
        yref="paper",
        text=instructions_text,
        showarrow=False,
        font=dict(size=12),
        bgcolor="rgba(255, 255, 255, 0.8)",
        bordercolor="black",
        borderwidth=1,
        borderpad=4,
        align="left"
    )
    # --- End Instructions Modification ---

    # Calculate aspect ratio for proper display
    x_range = np.ptp(header_loader.x_coords)
    y_range = np.ptp(header_loader.y_coords)
    aspect_ratio = x_range / y_range if y_range != 0 else 1

    # Set up the layout with customizations
    fig.update_layout(
        title="Interactive Seismic Survey Basemap (Hover/Click for Trace Information)",
        xaxis_title="X Coordinate",
        yaxis_title="Y Coordinate",
        showlegend=True,
        width=900,
        height=700,
        yaxis=dict(scaleanchor="x", scaleratio=1/aspect_ratio),
        # Add a text annotation that will be updated on click
        annotations=[
            dict(
                x=0.5,
                y=0.02,
                xref="paper",
                yref="paper",
                text="Click on a trace point to display its index",
                showarrow=False,
                font=dict(size=14),
                bgcolor="rgba(255, 255, 255, 0.8)",
                bordercolor="black",
                borderwidth=1,
                borderpad=4,
                align="center"
            )
        ],
        # Add click event handling for trace points
        # Note: This part would rely on Plotly's FigureWidget in an interactive environment
        hovermode="closest"
    )

    # Since we can't directly implement click events in a static Plotly figure output,
    # we'll add code to handle this when the figure is displayed in a browser environment
    config = {
        'displayModeBar': True,
        'scrollZoom': True,
        'modeBarButtonsToAdd': ['drawline', 'eraseshape'],
    }

    # Display the figure
    fig.show(config=config)

    # Return additional information that would help with trace selection
    return fig

def import_coordinates_from_file(file_path):
    """
    Import X,Y coordinates from various text file formats.
    Supports CSV, space-delimited, and tab-delimited formats.

    Args:
        file_path: Path to the ASCII file

    Returns:
        List of (x,y) coordinate tuples
    """
    coordinates = []

    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()

        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):  # Skip empty lines and comments
                continue

            # Try different delimiters
            if ',' in line:
                parts = line.split(',')
            elif '\t' in line:
                parts = line.split('\t')
            else:
                parts = line.split()

            # Clean and convert to float
            if len(parts) >= 2:
                try:
                    x = float(parts[0].strip())
                    y = float(parts[1].strip())
                    coordinates.append((x, y))
                except ValueError:
                    print(f"Warning: Could not parse line: {line}")

    except Exception as e:
        messagebox.showerror("Import Error", f"Error importing coordinates: {str(e)}")
        return []

    print(f"Successfully imported {len(coordinates)} coordinates from {file_path}")
    return coordinates

def create_polyline_selection_ui():
    """Create a custom interactive UI for polyline selection"""
    polyline_window = tk.Toplevel(root)
    polyline_window.title("Polyline Selection")
    polyline_window.geometry("500x500")

    # Frame for vertices list
    vertices_frame = tk.Frame(polyline_window)
    vertices_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    # Create a listbox to show selected vertices
    tk.Label(vertices_frame, text="Selected Polyline Vertices:").pack(anchor='w')
    vertices_list = tk.Text(vertices_frame, height=10, width=50)
    vertices_list.pack(fill=tk.BOTH, expand=True)

    # Stats display
    stats_var = tk.StringVar(value="Vertices: 0 | Total Length: 0.0 | Selected Traces: 0")
    stats_label = tk.Label(polyline_window, textvariable=stats_var)
    stats_label.pack(pady=5)

    # Control buttons
    button_frame = tk.Frame(polyline_window)
    button_frame.pack(fill=tk.X, padx=10, pady=5)

    vertices = []

    def add_vertex():
        dialog = tk.Toplevel(polyline_window)
        dialog.title("Add Vertex")

        tk.Label(dialog, text="X Coordinate:").grid(row=0, column=0, padx=5, pady=5)
        x_entry = tk.Entry(dialog)
        x_entry.grid(row=0, column=1, padx=5, pady=5)

        tk.Label(dialog, text="Y Coordinate:").grid(row=1, column=0, padx=5, pady=5)
        y_entry = tk.Entry(dialog)
        y_entry.grid(row=1, column=1, padx=5, pady=5)

        def on_ok():
            try:
                x = float(x_entry.get())
                y = float(y_entry.get())
                vertices.append((x, y))
                update_vertices_list()
                update_stats()
                dialog.destroy()
            except ValueError:
                messagebox.showerror("Invalid Input", "Please enter valid numeric coordinates")

        tk.Button(dialog, text="OK", command=on_ok).grid(row=2, column=0, columnspan=2, pady=10)

    def import_vertices():
        file_path = filedialog.askopenfilename(
            title="Select coordinate file",
            filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if not file_path:
            return

        imported_vertices = import_coordinates_from_file(file_path)
        if imported_vertices:
            vertices.clear()
            vertices.extend(imported_vertices)
            update_vertices_list()
            update_stats()
            messagebox.showinfo("Import Successful", f"Imported {len(imported_vertices)} vertices")

    def update_vertices_list():
        vertices_list.delete(1.0, tk.END)
        for i, (x, y) in enumerate(vertices):
            vertices_list.insert(tk.END, f"{i+1}. X: {x:.2f}, Y: {y:.2f}\n")

    def update_stats():
        length = 0
        for i in range(len(vertices) - 1):
            x1, y1 = vertices[i]
            x2, y2 = vertices[i + 1]
            length += math.sqrt((x2-x1)**2 + (y2-y1)**2)

        vertex_info = ""
        if vertices:
            vertex_info = " | Vertices: "
            vertex_info += ", ".join([f"({x:.1f},{y:.1f})" for x, y in vertices[:3]])
            if len(vertices) > 3:
                vertex_info += "..."

        stats_var.set(f"Vertices: {len(vertices)} | Total Length: {length:.2f} | Selected Traces: {len(selected_indices)}{vertex_info}")

    def update_map():
        # Show basemap with current polyline
        plot_interactive_basemap(header_loader, well_df_basemap, segy_path,
                                highlight_indices=selected_indices,
                                polyline_vertices=vertices)

    def calculate_selection():
        nonlocal selected_indices
        if len(vertices) < 2:
            messagebox.showwarning("Warning", "Need at least 2 vertices to define a polyline")
            return

        # Ask for distance tolerance
        tolerance = simpledialog.askfloat("Distance Tolerance",
                                         "Enter maximum distance from polyline:",
                                         minvalue=0.1, initialvalue=10.0)
        if tolerance is None:
            return

        # Find traces near polyline
        selected_indices = find_traces_near_polyline(header_loader, vertices, tolerance)
        update_stats()
        update_map()

        # Update display with count
        messagebox.showinfo("Selection Complete",
                           f"Found {len(selected_indices)} traces within {tolerance} units of polyline")

    # Add buttons
    tk.Button(button_frame, text="Add Vertex", command=add_vertex).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Import from File", command=import_vertices).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Update Map", command=update_map).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Calculate Selection", command=calculate_selection).pack(side=tk.LEFT, padx=5)

    # OK/Cancel buttons
    final_button_frame = tk.Frame(polyline_window)
    final_button_frame.pack(fill=tk.X, padx=10, pady=10)

    def on_ok():
        if len(selected_indices) == 0 and len(vertices) >= 2:
            if messagebox.askyesno("No Selection",
                                  "No traces have been selected yet. Calculate selection now?"):
                calculate_selection()
                if len(selected_indices) > 0:
                    polyline_window.destroy()
            else:
                polyline_window.destroy()
        else:
            polyline_window.destroy()

    def on_cancel():
        nonlocal selected_indices
        selected_indices = []
        polyline_window.destroy()

    tk.Button(final_button_frame, text="OK", command=on_ok).pack(side=tk.LEFT, padx=5)
    tk.Button(final_button_frame, text="Cancel", command=on_cancel).pack(side=tk.RIGHT, padx=5)

    # Initialize
    selected_indices = []
    update_vertices_list()
    update_stats()

    polyline_window.wait_window()
    return vertices, selected_indices

def plot_interactive_basemap(header_loader, well_df=None, segy_path=None, highlight_indices=None, polyline_vertices=None):
    """
    Create an interactive basemap that shows trace indices on hover and click.

    Args:
        header_loader: The SegyHeaderLoader object
        well_df: DataFrame containing well information
        segy_path: Path to the SEG-Y file
        highlight_indices: List of trace indices to highlight
        polyline_vertices: List of (x,y) tuples for polyline vertices
    """
    # Create the base figure
    fig = go.Figure()

    # Add inline/crossline grid for reference (reduced opacity)
    unique_inlines = np.unique(header_loader.inlines)
    for il in unique_inlines[::5]:
        mask = header_loader.inlines == il
        fig.add_trace(go.Scatter(
            x=header_loader.x_coords[mask],
            y=header_loader.y_coords[mask],
            mode='lines',
            line=dict(color='grey', width=0.5),
            opacity=0.2,  # Reduced opacity
            showlegend=False,
            hoverinfo='skip'  # Disable hover for grid lines
        ))

    unique_crosslines = np.unique(header_loader.crosslines)
    for xl in unique_crosslines[::5]:
        mask = header_loader.crosslines == xl
        fig.add_trace(go.Scatter(
            x=header_loader.x_coords[mask],
            y=header_loader.y_coords[mask],
            mode='lines',
            line=dict(color='grey', width=0.5),
            opacity=0.2,  # Reduced opacity
            showlegend=False,
            hoverinfo='skip'  # Disable hover for grid lines
        ))

    # --- Add Decimation Logic ---
    num_unique_traces = len(header_loader.x_coords)
    # Aim for a maximum number of points to display for performance, e.g., 50k-100k
    target_points = 75000
    step = max(1, num_unique_traces // target_points)
    decimated_indices = np.arange(0, num_unique_traces, step)
    print(f"Basemap: Displaying {len(decimated_indices)} out of {num_unique_traces} trace locations (decimation step={step}) for performance.")
    # --- End Decimation Logic ---

    # Create hover text only for decimated points
    hover_text = []
    for i in decimated_indices: # Iterate over decimated indices
        trace_idx = header_loader.unique_indices[i]
        inline = header_loader.inlines[i]
        crossline = header_loader.crosslines[i]
        hover_text.append(f"Trace: {trace_idx}<br>Inline: {inline}<br>Crossline: {crossline}<br>X: {header_loader.x_coords[i]:.2f}<br>Y: {header_loader.y_coords[i]:.2f}") # Added X,Y

    # Add decimated trace points with hover info
    fig.add_trace(go.Scatter(
        x=header_loader.x_coords[decimated_indices], # Use decimated coordinates
        y=header_loader.y_coords[decimated_indices], # Use decimated coordinates
        mode='markers',
        marker=dict(color='blue', size=2), # Smaller markers might be better
        opacity=0.6,
        name=f'Seismic Traces (1/{step} shown)', # Update name
        hoverinfo='text',
        hovertext=hover_text, # Use decimated hover text
        customdata=header_loader.unique_indices[decimated_indices] # Store original indices for decimated points
    ))

    # Highlight specific trace indices if provided
    if highlight_indices and len(highlight_indices) > 0:
        # Find positions of these indices within unique_indices
        highlight_positions = []
        valid_highlight_indices = []

        for idx in highlight_indices:
            positions = np.where(header_loader.unique_indices == idx)[0]
            if len(positions) > 0:
                highlight_positions.append(positions[0])
                valid_highlight_indices.append(idx)

        if highlight_positions:
            highlight_x = [header_loader.x_coords[pos] for pos in highlight_positions]
            highlight_y = [header_loader.y_coords[pos] for pos in highlight_positions]

            # Create hover text for highlighted points
            highlight_hover = [f"Selected Trace: {idx}<br>Inline: {header_loader.inlines[pos]}<br>Crossline: {header_loader.crosslines[pos]}"
                              for idx, pos in zip(valid_highlight_indices, highlight_positions)]

            fig.add_trace(go.Scatter(
                x=highlight_x,
                y=highlight_y,
                mode='markers+text',
                marker=dict(color='green', size=10, symbol='circle-open', line=dict(width=2)),
                text=[str(idx) for idx in valid_highlight_indices],
                textposition="top center",
                textfont=dict(size=10, color="darkgreen"),
                name='Selected Indices',
                hoverinfo='text',
                hovertext=highlight_hover
            ))

    # Add well information if provided
    if well_df is not None and not well_df.empty:
        for _, row in well_df.iterrows():
            fig.add_trace(go.Scatter(
                x=[row["X"]],
                y=[row["Y"]],
                mode='markers+text',
                marker=dict(color='red', size=8),
                text=f"{row['Well']} ({row['Surface']})",
                textposition="bottom right",
                name=f"{row['Well']} ({row['Surface']})"
            ))

    # Calculate statistics
    total_traces = len(header_loader.x_coords)
    inline_min, inline_max = np.min(header_loader.inlines), np.max(header_loader.inlines)
    crossline_min, crossline_max = np.min(header_loader.crosslines), np.max(header_loader.crosslines)

    # Get time range if segy_path is provided
    time_stats = ""
    if segy_path:
        try:
            with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
                dt = segyfile.bin[segyio.BinField.Interval] / 1_000_000  # Convert to seconds
                num_samples = segyfile.samples.size
                time_range = (0, (num_samples - 1) * dt)
                time_stats = f"Time Range: {time_range[0]:.3f}s - {time_range[1]:.3f}s<br>"
        except Exception as e:
            print(f"Could not read time range from SEG-Y: {e}")

    # Create statistics annotation
    stats_text = (
        f"Survey Statistics:<br>"
        f"Total Traces: {total_traces:,}<br>"
        f"Inline Range: {inline_min} - {inline_max}<br>"
        f"Crossline Range: {crossline_min} - {crossline_max}<br>"
        f"{time_stats}"
    )

    # Add polyline coordinates to statistics if provided
    if polyline_vertices and len(polyline_vertices) > 0:
        stats_text += "<br><br>Polyline Vertices:<br>"
        for i, (x, y) in enumerate(polyline_vertices):
            stats_text += f"pt{i+1} x: {x:.2f} y: {y:.2f}<br>"

    # Add annotation with statistics
    fig.add_annotation(
        x=0.02,
        y=0.98,
        xref="paper",
        yref="paper",
        text=stats_text,
        showarrow=False,
        font=dict(size=12),
        bgcolor="rgba(255, 255, 255, 0.8)",
        bordercolor="black",
        borderwidth=1,
        borderpad=4,
        align="left"
    )

    # --- Modify Instructions Annotation ---
    instructions_text = (
        "Interactive Controls:<br>"
        "• Hover over points to see trace info (X, Y, Index)<br>"
        "• Click on points to display trace index<br>"
        "• Use X,Y coordinates to define a polyline (option 5)" # Updated instruction
    )

    fig.add_annotation(
        x=0.02,
        y=0.82,
        xref="paper",
        yref="paper",
        text=instructions_text,
        showarrow=False,
        font=dict(size=12),
        bgcolor="rgba(255, 255, 255, 0.8)",
        bordercolor="black",
        borderwidth=1,
        borderpad=4,
        align="left"
    )
    # --- End Instructions Modification ---

    # Calculate aspect ratio for proper display
    x_range = np.ptp(header_loader.x_coords)
    y_range = np.ptp(header_loader.y_coords)
    aspect_ratio = x_range / y_range if y_range != 0 else 1

    # Set up the layout with customizations
    fig.update_layout(
        title="Interactive Seismic Survey Basemap (Hover/Click for Trace Information)",
        xaxis_title="X Coordinate",
        yaxis_title="Y Coordinate",
        showlegend=True,
        width=900,
        height=700,
        yaxis=dict(scaleanchor="x", scaleratio=1/aspect_ratio),
        # Add a text annotation that will be updated on click
        annotations=[
            dict(
                x=0.5,
                y=0.02,
                xref="paper",
                yref="paper",
                text="Click on a trace point to display its index",
                showarrow=False,
                font=dict(size=14),
                bgcolor="rgba(255, 255, 255, 0.8)",
                bordercolor="black",
                borderwidth=1,
                borderpad=4,
                align="center"
            )
        ],
        # Add click event handling for trace points
        # Note: This part would rely on Plotly's FigureWidget in an interactive environment
        hovermode="closest"
    )

    # Since we can't directly implement click events in a static Plotly figure output,
    # we'll add code to handle this when the figure is displayed in a browser environment
    config = {
        'displayModeBar': True,
        'scrollZoom': True,
        'modeBarButtonsToAdd': ['drawline', 'eraseshape'],
    }

    # Display the figure
    fig.show(config=config)

    # Return additional information that would help with trace selection
    return fig

def add_click_handler_code():
    """This returns JavaScript code that could be used to add click handlers in a proper JavaScript environment"""
    js_code = """
    // This code would need to be executed in a JavaScript environment with the plotly figure

    let tracePointsLayer;
    let lastAnnotation = null;

    // Find the trace points layer in the plotly figure
    for (let i = 0; i < data.length; i++) {
        if (data[i].name === 'Seismic Traces') {
            tracePointsLayer = i;
            break;
        }
    }

    // Add click event handler
    plotDiv.on('plotly_click', function(data) {
        if (data.points.length > 0 && data.points[0].curveNumber === tracePointsLayer) {
            const pt = data.points[0];
            const traceIdx = pt.customdata;

            // Remove previous annotation if it exists
            if (lastAnnotation !== null) {
                Plotly.relayout(plotDiv, {
                    'annotations[' + lastAnnotation + ']': null
                });
            }

            // Add new annotation at the clicked point
            const newAnnotation = {
                x: pt.x,
                y: pt.y,
                text: 'Trace Index: ' + traceIdx,
                showarrow: true,
                arrowhead: 2,
                arrowsize: 1,
                arrowwidth: 2,
                arrowcolor: '#FF0000',
                font: {
                    size: 12,
                    color: 'black'
                },
                bgcolor: 'white',
                bordercolor: 'black',
                borderwidth: 1,
                borderpad: 4
            };

            // Update the annotation at the bottom with the clicked trace index
            Plotly.relayout(plotDiv, {
                'annotations[0].text': 'Selected Trace Index: ' + traceIdx + ' (Use this for manual selection)'
            });

            // Add the new annotation
            Plotly.relayout(plotDiv, {
                annotations: [...layout.annotations, newAnnotation]
            });

            // Store the index of the new annotation
            lastAnnotation = layout.annotations.length - 1;
        }
    });
    """
    return js_code

def enhance_manual_selection_with_interactive_map(header_loader, well_df_basemap, segy_path, dt, plot_settings):
    """Enhanced manual selection with interactive map instead of static trace indices"""
    stored_indices = []

    # Always show the interactive basemap without asking
    plot_interactive_basemap(header_loader, well_df_basemap, segy_path)

    # Immediately show the input dialog after displaying the basemap
    messagebox.showinfo(
        "Interactive Basemap Usage",
        "The interactive basemap is now displayed.\n\n"
        "• Hover over points to see trace information\n"
        "• Click on points to display trace index\n"
        "• Note the indices you want to analyze\n\n"
        "After reviewing the map, you'll be prompted to enter the trace indices."
    )

    # Show the trace selection dialog
    manual_input = simpledialog.askstring(
        "Manual Selection",
        "Enter trace indices (comma-separated, e.g., 1, 2, 3):"
    )
    if manual_input is None:
        print("Manual selection cancelled.")
        return None

    try:
        chosen_indices = [int(idx.strip()) for idx in manual_input.split(',')]
        if not all(0 <= idx < len(header_loader.unique_indices) for idx in chosen_indices):
            messagebox.showwarning(
                "Warning",
                "One or more indices are out of range. Only valid indices will be processed."
            )
            # Filter to keep only valid indices
            chosen_indices = [idx for idx in chosen_indices if 0 <= idx < len(header_loader.unique_indices)]

            if not chosen_indices:
                print("No valid indices provided.")
                return None

        # Show basemap with the selected indices highlighted
        if messagebox.askyesno(
            "Highlight Selection",
            "Would you like to highlight your selected indices on the basemap?"
        ):
            plot_interactive_basemap(header_loader, well_df_basemap, segy_path, highlight_indices=chosen_indices)

        # Continue with the processing of the selected indices
        trace_data = []
        for trace_idx in tqdm(chosen_indices, desc="Fetching traces"):
            trace = load_trace_sample(segy_path, trace_idx)
            trace_data.append({'trace_sample': trace, 'trace_idx': trace_idx})

        # Select outputs
        selected_outputs = select_outputs(is_2d_3d_mode=True)
        print("\nCalculating spectral descriptors...")

        display_keys_to_exclude = [
            'Frequency', 'Dominant Frequency', 'Time (Y-axis)',
            'hfc_p95', 'spec_decrease_p95', 'hfc_percentile', 'spec_decrease_percentile',
            'Input Signal', 'Magnitude * Voice', 'Spectral Slope',
            'Spectral Bandwidth', 'Spectral Rolloff', 'Mag*Voice Slope',
            'WOSS', 'Spectral Decrease', 'HFC', 'epsilon', 'sample_percent',
            'max_traces', 'fdom_exponent', 'use_band_limited',
            'colormap', 'colormap_mag_voice', 'colormap_dom_freq',
            'colormap_spec_decrease', 'colormap_hfc', 'colormap_bandwidth',
            'colormap_rolloff', 'colormap_woss', 'section_colormap',
            'colormap_mag_voice_slope'
        ]
        descriptor_settings = {k: v for k, v in plot_settings.items()
                             if k not in display_keys_to_exclude}

        descriptors_list = [
            dlogst_spec_descriptor(t['trace_sample'], dt, fmax=len(t['trace_sample'])//2, use_band_limited=plot_settings['use_band_limited'], **descriptor_settings)
            for t in tqdm(trace_data, desc="Analyzing traces")
        ]

        # Store the selected indices for future use
        stored_indices = chosen_indices

        # Plot the seismic section
        plot_seismic_section(trace_data, descriptors_list, dt, plot_settings, selected_outputs, header_loader, 7)

        return trace_data, stored_indices
    except ValueError as e:
        messagebox.showerror("Input Error", f"Invalid input format: {e}. Please enter valid integers separated by commas.")
        print(f"Invalid input format: {e}. Please enter valid integers separated by commas.")
        return None
    except Exception as e:
        messagebox.showerror("Error", f"An error occurred during trace processing: {e}")
        print(f"An error occurred during trace processing: {e}")
        return None

def select_well_marker_pairs(df_wells):
    well_marker_list = [f"{row['Well']} - {row['Surface']}" for _, row in df_wells.iterrows()]
    selection_window = tk.Toplevel(root)
    selection_window.title("Select Well-Marker Pairs for Sample Trace")
    selection_window.geometry("400x450")  # Increased height to fit filter controls

    # Add a frame for the filter controls
    filter_frame = tk.Frame(selection_window)
    filter_frame.pack(fill=tk.X, padx=10, pady=5)

    tk.Label(filter_frame, text="Filter by Surface:").pack(side=tk.LEFT)
    filter_entry = tk.Entry(filter_frame)
    filter_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

    # Initialize displayed indices with all items
    displayed_indices = list(range(len(well_marker_list)))

    def apply_filter():
        nonlocal displayed_indices
        keyword = filter_entry.get().strip().lower()
        if keyword:
            # Filter by surface (second part after " - ")
            displayed_indices = [
                i for i, item in enumerate(well_marker_list)
                if keyword in item.split(" - ")[1].lower()
            ]
        else:
            # Show all items if keyword is empty
            displayed_indices = list(range(len(well_marker_list)))
        # Update listbox
        listbox.delete(0, tk.END)
        for idx in displayed_indices:
            listbox.insert(tk.END, well_marker_list[idx])

    def clear_filter():
        filter_entry.delete(0, tk.END)
        apply_filter()

    # Add filter and clear buttons
    tk.Button(filter_frame, text="Filter", command=apply_filter).pack(side=tk.LEFT, padx=5)
    tk.Button(filter_frame, text="Clear Filter", command=clear_filter).pack(side=tk.LEFT, padx=5)

    # Instruction label
    tk.Label(selection_window, text="Select well-marker pair(s) for sample trace:").pack(pady=5)

    # Frame for listbox and scrollbar
    frame = tk.Frame(selection_window)
    frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    scrollbar = tk.Scrollbar(frame, orient="vertical")
    listbox = tk.Listbox(frame, selectmode=tk.MULTIPLE, yscrollcommand=scrollbar.set, width=50, height=15)
    scrollbar.config(command=listbox.yview)
    scrollbar.pack(side="right", fill="y")
    listbox.pack(side="left", fill="both", expand=True)

    # Initially populate the listbox with all items
    for idx in displayed_indices:
        listbox.insert(tk.END, well_marker_list[idx])

    selected_indices = []
    def on_ok():
        nonlocal selected_indices
        selected_positions = [int(x) for x in listbox.curselection()]
        # Map listbox positions to original indices
        selected_indices = [displayed_indices[pos] for pos in selected_positions]
        selection_window.destroy()

    tk.Button(selection_window, text="OK", command=on_ok).pack(pady=10)
    selection_window.wait_window()
    return [df_wells.iloc[i] for i in selected_indices]

def get_nearest_trace_index(header_loader, well_x, well_y):
    """
    Find the nearest seismic trace index to a given X,Y coordinate.

    This function calculates the Euclidean distance from a specified point
    to all trace locations and returns the index of the closest trace.
    Used for correlating well locations with seismic data.

    Args:
        header_loader: SegyHeaderLoader object containing trace coordinates
        well_x: X-coordinate of the point (typically a well location)
        well_y: Y-coordinate of the point

    Returns:
        int: Index of the nearest trace in the SEG-Y file
    """
    # Stack X and Y coordinates for vectorized distance calculation
    coords = np.column_stack((header_loader.x_coords, header_loader.y_coords))

    # Calculate Euclidean distances from the point to all trace locations
    distances = np.linalg.norm(coords - np.array([well_x, well_y]), axis=1)

    # Return the trace index with the minimum distance
    return header_loader.unique_indices[np.argmin(distances)]

def load_trace_sample(segy_path, trace_index):
    """
    Load a single trace from a SEG-Y file.

    This function opens a SEG-Y file and extracts the amplitude data
    for a specific trace index.

    Args:
        segy_path: Path to the SEG-Y file
        trace_index: Index of the trace to load

    Returns:
        numpy.ndarray: Amplitude data for the requested trace
    """
    with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
        return segyfile.trace[trace_index]

def get_sampling_interval(segy_path):
    """
    Extract the time sampling interval from a SEG-Y file.

    This function reads the binary header of a SEG-Y file to determine
    the time sampling interval (dt) in seconds. If the header cannot be read,
    it falls back to a default value of 4 ms.

    Args:
        segy_path: Path to the SEG-Y file

    Returns:
        float: Sampling interval in seconds
    """
    try:
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            # Convert microseconds to seconds
            dt = segyfile.bin[segyio.BinField.Interval] / 1_000_000
        print(f"Sampling interval (dt): {dt} seconds")
        return dt
    except Exception as e:
        print(f"Error retrieving sampling interval: {e}")
        # Fall back to default 4 ms sampling rate
        return 0.004

# Parameter Configuration Functions
def get_general_and_spectral_settings(dt, num_samples):
    """
    Display a dialog for configuring general and spectral analysis parameters.

    This function creates a GUI dialog with tabs for configuring various parameters
    related to spectral analysis, including numerical stability settings, sampling
    parameters, frequency bands, and logistic transform settings.

    Args:
        dt: Sampling interval in seconds
        num_samples: Number of samples in each trace

    Returns:
        dict: Dictionary containing all configured parameters
    """
    dialog = tk.Toplevel(root)
    dialog.title("Set General and Spectral Analysis Parameters")
    settings = {}
    limit_entries = {}
    spectral_entries = {}
    settings_set = tk.BooleanVar(value=False)

    notebook = ttk.Notebook(dialog)
    notebook.pack(fill='both', expand=True, padx=10, pady=10)

    # General Settings Tab
    general_tab = ttk.Frame(notebook)
    notebook.add(general_tab, text="General Settings")

    row = 0
    ttk.Label(general_tab, text="Numerical Stability Settings:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=4, pady=(10, 0), sticky='w')
    row += 1
    ttk.Label(general_tab, text="Epsilon:").grid(row=row, column=0, sticky="e")
    epsilon_entry = ttk.Entry(general_tab)
    epsilon_entry.insert(0, "1e-10")
    epsilon_entry.grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
    row += 1

    ttk.Label(general_tab, text="Sampling Settings:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=4, pady=(10, 0), sticky='w')
    row += 1
    ttk.Label(general_tab, text="Sampling Percentage (0.1-100):").grid(row=row, column=0, sticky="e")
    sample_percent_entry = ttk.Entry(general_tab)
    sample_percent_entry.insert(0, "1.0")
    sample_percent_entry.grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
    row += 1
    ttk.Label(general_tab, text="Maximum Traces:").grid(row=row, column=0, sticky="e")
    max_traces_entry = ttk.Entry(general_tab)
    max_traces_entry.insert(0, "50")  # Set default maximum traces to 50
    max_traces_entry.grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
    row += 1

    ttk.Label(general_tab, text="Plot Limits:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=4, pady=(10, 0), sticky='w')
    row += 1
    fields = [("Frequency (Hz)", (0, 100)), ("Time (Y-axis) (s)", (0, (num_samples - 1) * dt))]
    for name, default in fields:
        ttk.Label(general_tab, text=f"{name}:").grid(row=row, column=0, sticky="e")
        min_entry = ttk.Entry(general_tab, width=8)
        min_entry.insert(0, str(default[0]))
        min_entry.grid(row=row, column=1, padx=5, pady=2)
        ttk.Label(general_tab, text="to").grid(row=row, column=2)
        max_entry = ttk.Entry(general_tab, width=8)
        max_entry.insert(0, str(default[1]))
        max_entry.grid(row=row, column=3, padx=5, pady=2)
        limit_entries[name] = (min_entry, max_entry)
        row += 1

    # Spectral Analysis Tab
    spectral_tab = ttk.Frame(notebook)
    notebook.add(spectral_tab, text="Spectral Analysis")

    row = 0
    ttk.Label(spectral_tab, text="Frequency Band Settings:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=4, pady=(10, 0), sticky='w')
    row += 1
    freq_band_fields = [("b1 (Hz)", 5), ("b2 (Hz)", 40)]
    for name, default in freq_band_fields:
        ttk.Label(spectral_tab, text=name).grid(row=row, column=0, sticky="e")
        entry = ttk.Entry(spectral_tab)
        entry.insert(0, str(default))
        entry.grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
        spectral_entries[name.split()[0]] = entry
        row += 1

    ttk.Label(spectral_tab, text="Spectral Slope/Decrease Mode:").grid(row=row, column=0, sticky="e")
    band_limited_var = tk.StringVar(spectral_tab)
    band_limited_var.set("Full-Band")
    ttk.OptionMenu(spectral_tab, band_limited_var, "Full-Band", "Full-Band", "Band-Limited").grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
    row += 1

    ttk.Label(spectral_tab, text="Logistic Transform Settings:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=4, pady=(10, 0), sticky='w')
    row += 1
    logistic_fields = [("kmax", 120), ("shape", 0.35), ("int_val", 35)]
    for name, default in logistic_fields:
        ttk.Label(spectral_tab, text=name).grid(row=row, column=0, sticky="e")
        entry = ttk.Entry(spectral_tab)
        entry.insert(0, str(default))
        entry.grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
        spectral_entries[name] = entry
        row += 1

    ttk.Label(spectral_tab, text="Bandwidth and Rolloff Settings:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=4, pady=(10, 0), sticky='w')
    row += 1
    bandwidth_fields = [("p_bandwidth", 2), ("roll_percent", 0.85)]
    for name, default in bandwidth_fields:
        ttk.Label(spectral_tab, text=name).grid(row=row, column=0, sticky="e")
        entry = ttk.Entry(spectral_tab)
        entry.insert(0, str(default))
        entry.grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
        spectral_entries[name] = entry
        row += 1

    ttk.Label(spectral_tab, text="WOSS Formula Settings:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=4, pady=(10, 0), sticky='w')
    row += 1
    ttk.Label(spectral_tab, text="Dominant Frequency Exponent:").grid(row=row, column=0, sticky="e")
    fdom_exp_entry = ttk.Entry(spectral_tab)
    fdom_exp_entry.insert(0, "2.0")
    fdom_exp_entry.grid(row=row, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
    spectral_entries["fdom_exponent"] = fdom_exp_entry

    def on_ok():
        try:
            settings['Frequency'] = (float(limit_entries['Frequency (Hz)'][0].get()), float(limit_entries['Frequency (Hz)'][1].get()))
            settings['Time (Y-axis)'] = (float(limit_entries['Time (Y-axis) (s)'][0].get()), float(limit_entries['Time (Y-axis) (s)'][1].get()))
            settings['epsilon'] = float(epsilon_entry.get())
            settings['sample_percent'] = float(sample_percent_entry.get())
            settings['max_traces'] = int(max_traces_entry.get())
            settings['b1'] = float(spectral_entries['b1'].get())
            settings['b2'] = float(spectral_entries['b2'].get())
            settings['kmax'] = float(spectral_entries['kmax'].get())
            settings['shape'] = float(spectral_entries['shape'].get())
            settings['int_val'] = float(spectral_entries['int_val'].get())
            settings['p_bandwidth'] = float(spectral_entries['p_bandwidth'].get())
            settings['roll_percent'] = float(spectral_entries['roll_percent'].get())
            settings['fdom_exponent'] = float(spectral_entries['fdom_exponent'].get())
            settings['use_band_limited'] = band_limited_var.get() == "Band-Limited"
            settings_set.set(True)
            dialog.destroy()
        except ValueError:
            messagebox.showerror("Input Error", "Please enter valid numerical values.")
            return

    ttk.Button(dialog, text="OK", command=on_ok).pack(pady=10)
    dialog.wait_window()

    if not settings_set.get():
        settings = {
            'Frequency': (0, 100),
            'Time (Y-axis)': (0, (num_samples - 1) * dt),
            'epsilon': 1e-10,
            'sample_percent': 1.0,
            'max_traces': 1000,
            'b1': 5, 'b2': 40,
            'kmax': 120, 'shape': 0.35, 'int_val': 35,
            'p_bandwidth': 2, 'roll_percent': 0.85,
            'fdom_exponent': 2.0,
            'use_band_limited': False
        }

    return settings

def get_statistics_percentiles_and_display_settings(descriptors, general_spectral_settings):
    dialog = tk.Toplevel(root)
    dialog.title("Set Statistics, Percentile, and Display Settings")
    settings = {}
    limit_entries = {}
    settings_set = tk.BooleanVar(value=False)

    notebook = ttk.Notebook(dialog)
    notebook.pack(fill='both', expand=True, padx=10, pady=10)

    # Statistics Tab
    stats_tab = ttk.Frame(notebook)
    notebook.add(stats_tab, text="Statistics")

    stats_text = tk.Text(stats_tab, wrap=tk.WORD, height=25, width=50)
    stats_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # Helper function to clean array of inf/nan and check if valid
    def clean_array(arr, name="data"):
        if arr.size == 0:
            print(f"Warning: {name} array is empty.")
            return np.array([])

        if np.iscomplexobj(arr):
            print(f"Warning: {name} contains complex values. Converting to real part only.")
            arr = np.real(arr)

        cleaned = arr[np.isfinite(arr)]

        if not np.isrealobj(cleaned):
            print(f"Warning: {name} still contains non-real values after cleaning. Converting to float.")
            cleaned = cleaned.astype(float)

        if cleaned.size == 0:
            print(f"Warning: {name} contains no valid values after cleaning.")
        else:
            print(f"Cleaned {name}: {arr.size - cleaned.size} invalid values removed ({cleaned.size} values remain).")

        return cleaned

    # Extract and clean input signal data
    input_signal_raw = np.concatenate([d['data'] for d in descriptors if isinstance(d['data'], np.ndarray) and d['data'].size > 0]) if descriptors else np.array([])
    input_signal = clean_array(input_signal_raw, "Input Signal")

    # Calculate percentiles for all data arrays for use as default min/max values
    data_arrays = {
        "Input Signal": input_signal,
        "HFC": clean_array(np.concatenate([d['hfc'] for d in descriptors if isinstance(d['hfc'], np.ndarray) and d['hfc'].size > 0]) if descriptors else np.array([]), "HFC"),
        "Spectral Decrease": clean_array(np.concatenate([d['spec_decrease'] for d in descriptors if isinstance(d['spec_decrease'], np.ndarray) and d['spec_decrease'].size > 0]) if descriptors else np.array([]), "Spectral Decrease"),
        "Spectral Slope": clean_array(np.concatenate([d['spec_slope'] for d in descriptors if isinstance(d['spec_slope'], np.ndarray) and d['spec_slope'].size > 0]) if descriptors else np.array([]), "Spectral Slope"),
        "Mag*Voice Slope": clean_array(np.concatenate([d['mag_voice_slope'] for d in descriptors if isinstance(d['mag_voice_slope'], np.ndarray) and d['mag_voice_slope'].size > 0]) if descriptors else np.array([]), "Mag*Voice Slope"),
        "Spectral Bandwidth": clean_array(np.concatenate([d['spec_bandwidth'] for d in descriptors if isinstance(d['spec_bandwidth'], np.ndarray) and d['spec_bandwidth'].size > 0]) if descriptors else np.array([]), "Spectral Bandwidth"),
        "Spectral Rolloff": clean_array(np.concatenate([d['spec_rolloff'] for d in descriptors if isinstance(d['spec_rolloff'], np.ndarray) and d['spec_rolloff'].size > 0]) if descriptors else np.array([]), "Spectral Rolloff"),
        # Add Magnitude Spectrogram data extraction
        "Magnitude Spectrogram": clean_array(np.concatenate([d['mag'].flatten() for d in descriptors if 'mag' in d and isinstance(d['mag'], np.ndarray) and d['mag'].size > 0]) if descriptors else np.array([]), "Magnitude Spectrogram"),
        "Magnitude * Voice": clean_array(np.concatenate([d['mag_voice'].flatten() for d in descriptors if 'mag_voice' in d and isinstance(d['mag_voice'], np.ndarray) and d['mag_voice'].size > 0]) if descriptors else np.array([]), "Magnitude * Voice") # Use actual data if available
    }

    # Calculate percentiles and store for later use as default min/max values
    percentile_defaults = {}
    for name, arr in data_arrays.items():
        if arr.size > 0:
            try:
                # Use P1 and P99 for spectrograms as they often have wider dynamic range
                if name in ["Magnitude Spectrogram", "Magnitude * Voice"]:
                     p_low, p_high = 1, 99
                else:
                     p_low, p_high = 5, 95

                val_low = np.percentile(arr, p_low)
                val_high = np.percentile(arr, p_high)
                percentile_defaults[name] = (val_low, val_high)
                print(f"Calculated {name} P{p_low}/P{p_high} defaults: ({val_low:.3f}, {val_high:.3f})")


                # Keep symmetric calculation for relevant attributes
                if name in ["Input Signal", "Mag*Voice Slope", "WOSS"]:
                    abs_p95 = np.percentile(np.abs(arr), 95)
                    percentile_defaults[f"{name} (symmetric)"] = (-abs_p95, abs_p95)
            except Exception as e:
                print(f"Error calculating percentiles for {name}: {e}")
                percentile_defaults[name] = (0, 1) # Fallback
        else:
             # Set specific fallbacks if no data
            if name == "Magnitude Spectrogram":
                percentile_defaults[name] = (0, 1) # Sensible default for magnitude
            elif name == "Magnitude * Voice":
                percentile_defaults[name] = (-1, 1) # Symmetric default
            else:
                percentile_defaults[name] = (0, 1)
            print(f"No valid data for {name}, using default limits: {percentile_defaults[name]}")


    if "WOSS" not in percentile_defaults:
        percentile_defaults["WOSS"] = (-3, 3)
        percentile_defaults["WOSS (symmetric)"] = (-3, 3)

    if input_signal.size > 0:
        try:
            signal_percentiles = np.percentile(input_signal, [1, 5, 25, 50, 75, 95, 99])
            stats_text.insert(tk.END, "Input Signal Statistics:\n\n")
            stats_text.insert(tk.END, f"  Mean: {np.mean(input_signal):.4f}\n")
            stats_text.insert(tk.END, f"  Median: {np.median(input_signal):.4f}\n")
            stats_text.insert(tk.END, f"  Std Dev: {np.std(input_signal):.4f}\n")
            stats_text.insert(tk.END, f"  Min: {np.min(input_signal):.4f}\n")
            stats_text.insert(tk.END, f"  Max: {np.max(input_signal):.4f}\n")
            stats_text.insert(tk.END, f"  1st Percentile: {signal_percentiles[0]:.4f}\n")
            stats_text.insert(tk.END, f"  5th Percentile: {signal_percentiles[1]:.4f}\n")
            stats_text.insert(tk.END, f"  25th Percentile: {signal_percentiles[2]:.4f}\n")
            stats_text.insert(tk.END, f"  75th Percentile: {signal_percentiles[4]:.4f}\n")
            stats_text.insert(tk.END, f"  95th Percentile: {signal_percentiles[5]:.4f}\n")
            stats_text.insert(tk.END, f"  99th Percentile: {signal_percentiles[6]:.4f}\n")
            stats_text.insert(tk.END, "\n")
        except (TypeError, ValueError) as e:
            print(f"Error calculating statistics for Input Signal: {e}")
            stats_text.insert(tk.END, "Input Signal Statistics:\n\n")
            stats_text.insert(tk.END, f"  Error calculating statistics: {e}\n\n")
    else:
        stats_text.insert(tk.END, "Input Signal Statistics:\n\n")
        stats_text.insert(tk.END, "  No valid finite data available after cleaning inf/nan.\n\n")

    # Add spectral descriptor statistics with cleaning
    hfc = data_arrays["HFC"]
    spec_decrease = data_arrays["Spectral Decrease"]
    spec_slope = data_arrays["Spectral Slope"]
    mag_voice_slope = data_arrays["Mag*Voice Slope"]
    spec_bandwidth = data_arrays["Spectral Bandwidth"]
    spec_rolloff = data_arrays["Spectral Rolloff"]

    stats_text.insert(tk.END, "Spectral Descriptors Statistics:\n\n")
    for name, values in [
        ("HFC", hfc),
        ("Spectral Decrease", spec_decrease),
        ("Spectral Slope", spec_slope),
        ("Mag*Voice Slope", mag_voice_slope),
        ("Spectral Bandwidth", spec_bandwidth),
        ("Spectral Rolloff", spec_rolloff)
    ]:
        if values.size > 0:
            try:
                descriptor_percentiles = np.percentile(values, [5, 10, 25, 50, 75, 90, 95])
                stats_text.insert(tk.END, f"{name}:\n"
                                f"  Mean: {np.mean(values):.2f}\n"
                                f"  Median: {np.median(values):.2f}\n"
                                f"  Std Dev: {np.std(values):.2f}\n"
                                f"  Min: {np.min(values):.2f}\n"
                                f"  Max: {np.max(values):.2f}\n"
                                f"  5th Percentile: {descriptor_percentiles[0]:.2f}\n"
                                f"  10th Percentile: {descriptor_percentiles[1]:.2f}\n"
                                f"  25th Percentile: {descriptor_percentiles[2]:.2f}\n"
                                f"  50th Percentile: {descriptor_percentiles[3]:.2f}\n"
                                f"  75th Percentile: {descriptor_percentiles[4]:.2f}\n"
                                f"  90th Percentile: {descriptor_percentiles[5]:.2f}\n"
                                f"  95th Percentile: {descriptor_percentiles[6]:.2f}\n\n")
            except (TypeError, ValueError) as e:
                print(f"Error calculating statistics for {name}: {e}")
                stats_text.insert(tk.END, f"{name}:\n  Error calculating statistics: {e}\n\n")
        else:
            stats_text.insert(tk.END, f"{name}:\n  No valid data available after cleaning.\n\n")

    # Add WOSS statistics calculation and display (for plot limit estimation)
    epsilon = general_spectral_settings.get('epsilon', 1e-10)
    fdom_exponent = general_spectral_settings.get('fdom_exponent', 2.0)

    # Calculate WOSS using hfc_p95 instead of hardcoded 1.0 for normalization
    woss_values = []
    # Get the 95th percentile of HFC values if available, otherwise fallback to 1.0
    hfc_p95 = np.percentile(data_arrays["HFC"], 95) if data_arrays["HFC"].size > 0 else 1.0
    for d in descriptors:
        if (isinstance(d['hfc'], np.ndarray) and d['hfc'].size > 0 and
            isinstance(d['norm_fdom'], np.ndarray) and d['norm_fdom'].size > 0 and
            isinstance(d['mag_voice_slope'], np.ndarray) and d['mag_voice_slope'].size > 0):

            hfc_norm = d['hfc'] / hfc_p95  # Now using hfc_p95 instead of hardcoded 1.0
            norm_fdom = d['norm_fdom']  # Normalized dominant frequency
            mv_slope = d['mag_voice_slope']    # Magnitude*Voice slope

            # Calculate denominator with protection against division by zero
            denominator = hfc_norm * (norm_fdom**fdom_exponent + epsilon)

            # Calculate WOSS with protection against invalid values
            with np.errstate(divide='ignore', invalid='ignore'):
                woss = np.where(denominator > epsilon, mv_slope / denominator, 0.0)
                woss[~np.isfinite(woss)] = 0.0
                woss = np.clip(woss, -1e6, 1e6)
                woss_values.append(woss)

    # Combine all WOSS values
    woss_combined = np.concatenate(woss_values) if woss_values else np.array([])

    # Add WOSS to data_arrays for percentile calculation
    data_arrays["WOSS"] = clean_array(woss_combined, "WOSS")

    # Display WOSS statistics
    if woss_combined.size > 0:
        try:
            woss_percentiles = np.percentile(woss_combined, [5, 10, 25, 50, 75, 90, 95])
            stats_text.insert(tk.END, f"WOSS (fdom^{fdom_exponent:.1f}, using HFC p95={hfc_p95:.4f}):\n"
                           f"  Mean: {np.mean(woss_combined):.2f}\n"
                           f"  Median: {np.median(woss_combined):.2f}\n"
                           f"  Std Dev: {np.std(woss_combined):.2f}\n"
                           f"  Min: {np.min(woss_combined):.2f}\n"
                           f"  Max: {np.max(woss_combined):.2f}\n"
                           f"  5th Percentile: {woss_percentiles[0]:.2f}\n"
                           f"  10th Percentile: {woss_percentiles[1]:.2f}\n"
                           f"  25th Percentile: {woss_percentiles[2]:.2f}\n"
                           f"  50th Percentile: {woss_percentiles[3]:.2f}\n"
                           f"  75th Percentile: {woss_percentiles[4]:.2f}\n"
                           f"  90th Percentile: {woss_percentiles[5]:.2f}\n"
                           f"  95th Percentile: {woss_percentiles[6]:.2f}\n\n")

            # Update symmetric percentile defaults for WOSS
            abs_p95 = np.percentile(np.abs(woss_combined), 95)
            percentile_defaults["WOSS"] = (woss_percentiles[0], woss_percentiles[-1])
            percentile_defaults["WOSS (symmetric)"] = (-abs_p95, abs_p95)
        except Exception as e:
            print(f"Error calculating statistics for WOSS: {e}")
            stats_text.insert(tk.END, f"WOSS (fdom^{fdom_exponent:.1f}, using HFC p95={hfc_p95:.4f}):\n  Error calculating statistics: {e}\n\n")
    else:
        stats_text.insert(tk.END, f"WOSS (fdom^{fdom_exponent:.1f}, using HFC p95={hfc_p95:.4f}):\n  No valid data available after cleaning.\n\n")

    stats_text.config(state=tk.DISABLED)

    # Plot Limits Tab
    limits_tab = ttk.Frame(notebook)
    notebook.add(limits_tab, text="Plot Limits")

    limits_frame = ttk.Frame(limits_tab, padding=10)
    limits_frame.pack(fill=tk.BOTH, expand=True)

    ttk.Label(limits_frame, text="Parameter", font=("Arial", 10, "bold")).grid(row=0, column=0, padx=5, pady=5, sticky='w')
    ttk.Label(limits_frame, text="Min (P5)", font=("Arial", 10, "bold")).grid(row=0, column=1, padx=5, pady=5)
    ttk.Label(limits_frame, text="Max (P95)", font=("Arial", 10, "bold")).grid(row=0, column=2, padx=5, pady=5)

    limit_pairs = [
        ("Input Signal", "Input Signal"),
        ("Magnitude Spectrogram", "Magnitude Spectrogram"), # Add Magnitude Spectrogram
        ("Magnitude * Voice", "Magnitude * Voice"),
        ("Spectral Slope", "Spectral Slope"),
        ("Spectral Bandwidth", "Spectral Bandwidth"),
        ("Spectral Rolloff", "Spectral Rolloff"),
        ("Mag*Voice Slope", "Mag*Voice Slope"),
        ("WOSS", "WOSS")
    ]

    for idx, (key, label) in enumerate(limit_pairs, 1):
        ttk.Label(limits_frame, text=label).grid(row=idx, column=0, padx=5, pady=5, sticky='w')

        # Use calculated P1/P99 or P5/P95 defaults
        if label in percentile_defaults:
            default_min, default_max = percentile_defaults[label]
        else: # Fallback if calculation failed
            if label == "Magnitude Spectrogram": default_min, default_max = (0, 1)
            elif label == "Magnitude * Voice": default_min, default_max = (-1, 1)
            elif label in ["Spectral Bandwidth", "Spectral Rolloff"]: default_min, default_max = (0, 1)
            else: default_min, default_max = (-1, 1)
            print(f"Warning: Using fallback limits for {label}")


        min_entry = ttk.Entry(limits_frame, width=10)
        min_entry.insert(0, f"{default_min:.3f}")
        min_entry.grid(row=idx, column=1, padx=5, pady=5)

        max_entry = ttk.Entry(limits_frame, width=10)
        max_entry.insert(0, f"{default_max:.3f}")
        max_entry.grid(row=idx, column=2, padx=5, pady=5)

        limit_entries[key] = (min_entry, max_entry)

    next_row = len(limit_pairs) + 1
    ttk.Label(limits_frame, text="Global Normalization", font=("Arial", 10, "bold")).grid(row=next_row, column=0, columnspan=3, padx=5, pady=5, sticky='w')
    next_row += 1

    percentile_entries = {}
    single_percentiles = [("hfc", "HFC Normalization", 95), ("spec_decrease", "Spec Decrease Normalization", 95)]
    for key, label, default in single_percentiles:
        ttk.Label(limits_frame, text=label).grid(row=next_row, column=0, padx=5, pady=5, sticky='w')
        entry = ttk.Entry(limits_frame, width=8)
        entry.insert(0, str(default))
        entry.grid(row=next_row, column=1, padx=5, pady=5)
        ttk.Label(limits_frame, text="%").grid(row=next_row, column=2, padx=0, pady=5, sticky='w')
        percentile_entries[key] = entry
        next_row += 1

    # Display Settings Tab
    display_tab = ttk.Frame(notebook)
    notebook.add(display_tab, text="Display Settings")

    row = 0
    plotly_colorscales = ['RdBu', 'viridis', 'plasma', 'inferno', 'magma', 'cividis', 'hot', 'jet', 'RdYlBu', 'RdYlGn', 'RdGy', 'Spectral', 'hsv', 'icefire', 'BrBG', 'rainbow']
    ttk.Label(display_tab, text="Colormap Settings:", font=("Arial", 10, "bold")).grid(row=row, column=0, columnspan=2, pady=(10, 0), sticky='w')
    row += 1

    colormap_settings = [
        ("Magnitude Spectrogram", "colormap", "rainbow"),
        ("Magnitude * Voice", "colormap_mag_voice", "rainbow"),
        ("Dominant Frequency", "colormap_dom_freq", "rainbow"),
        ("Normalized Spectral Decrease", "colormap_spec_decrease", "rainbow"),
        ("Normalized HFC", "colormap_hfc", "rainbow"),
        ("Spectral Bandwidth", "colormap_bandwidth", "rainbow"),
        ("Spectral Rolloff", "colormap_rolloff", "rainbow"),
        ("WOSS", "colormap_woss", "RdBu"),  # Use RdBu for WOSS
        ("Seismic Sections", "section_colormap", "RdBu"),  # Use RdBu for Input Signal
        ("Mag*Voice Slope", "colormap_mag_voice_slope", "RdBu")  # Use RdBu for Magnitude * Voice Slope
    ]

    colormap_vars = {}
    for label, key, default in colormap_settings:
        ttk.Label(display_tab, text=f"{label}:").grid(row=row, column=0, sticky="e")
        var = tk.StringVar(dialog)
        var.set(default)
        ttk.OptionMenu(display_tab, var, default, *plotly_colorscales).grid(row=row, column=1, sticky="ew", pady=2, padx=5)
        colormap_vars[key] = var
        row += 1

    def on_ok():
        try:
            for key, entry_pair in limit_entries.items():
                min_val, max_val = float(entry_pair[0].get()), float(entry_pair[1].get())
                if min_val > max_val:
                    raise ValueError(f"Min value must be less than or equal to max value for {key}.")
                settings[key] = (min_val, max_val)

            for key, entry in percentile_entries.items():
                val = float(entry.get())
                if not (0 <= val <= 100):
                    raise ValueError(f"Percentile value for {key} must be 0-100.")
                settings[f"{key}_percentile"] = val

            for key, var in colormap_vars.items():
                settings[key] = var.get()

            settings_set.set(True)
            dialog.destroy()
        except ValueError as e:
            error_msg = str(e).replace("`", "'")
            messagebox.showerror("Input Error", error_msg)
            return

    ttk.Button(dialog, text="OK", command=on_ok).pack(pady=10)
    dialog.wait_window()

    if not settings_set.get():
        settings = {
            'Input Signal': percentile_defaults.get("Input Signal", (-1, 1)),
            'Magnitude Spectrogram': percentile_defaults.get("Magnitude Spectrogram", (0, 1)), # Add fallback
            'Magnitude * Voice': percentile_defaults.get("Magnitude * Voice", (-1, 1)),
            'Spectral Slope': percentile_defaults.get("Spectral Slope", (-1, 1)),
            'Spectral Bandwidth': percentile_defaults.get("Spectral Bandwidth", (0, 50)),
            'Spectral Rolloff': percentile_defaults.get("Spectral Rolloff", (0, 50)),
            'Mag*Voice Slope': percentile_defaults.get("Mag*Voice Slope", (-1, 1)),
            'WOSS': percentile_defaults.get("WOSS", (-3, 3)),
            'hfc_percentile': 95, 'spec_decrease_percentile': 95,
            'colormap': 'rainbow', 'colormap_mag_voice': 'rainbow', 'colormap_dom_freq': 'rainbow', # Default back to rainbow
            'colormap_spec_decrease': 'rainbow', 'colormap_hfc': 'rainbow', 'colormap_bandwidth': 'rainbow',
            'colormap_rolloff': 'rainbow', 'colormap_woss': 'RdBu', 'section_colormap': 'RdBu',
            'colormap_mag_voice_slope': 'RdBu', # Keep RdBu for slopes/WOSS/Sections
            'batched_mode': True  # Flag to indicate we're using batched mode
        }

    return settings

def get_plot_settings(dt, header_loader, segy_path, is_2d_3d_mode=False):
    """
    Configure all plot settings including spectral analysis parameters and display options.

    This function orchestrates the complete configuration process for spectral analysis
    and visualization. It collects general settings, computes global normalization
    parameters from sample data, and configures display settings.

    Args:
        dt: Sampling interval in seconds
        header_loader: SegyHeaderLoader object containing survey geometry
        segy_path: Path to the SEG-Y file
        is_2d_3d_mode: Flag indicating if operating in 2D/3D mode (optional)

    Returns:
        dict: Comprehensive dictionary of all plot and analysis settings
    """
    # Get number of samples from SEG-Y file
    with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
        num_samples = segyfile.samples.size
        general_spectral_settings = get_general_and_spectral_settings(dt, num_samples)

    print("\nComputing global normalization parameters from random samples...")
    total_traces = len(header_loader.unique_indices)
    desired_samples = int((general_spectral_settings['sample_percent'] / 100.0) * total_traces)
    num_samples = min(max(1, desired_samples), general_spectral_settings['max_traces'])
    print(f"Sampling {general_spectral_settings['sample_percent']}% of {total_traces} traces, limited to {general_spectral_settings['max_traces']}: using {num_samples} samples")

    sample_indices = np.random.choice(header_loader.unique_indices, num_samples, replace=False)

    # Always use batched version for statistical analysis
    from dlogst_spec_descriptor_gpu_init import dlogst_spec_descriptor_gpu_2d_chunked_mag as dlogst_spec_descriptor_batch

    # Load all traces
    trace_samples = []
    for trace_idx in tqdm(sample_indices, desc="Loading random traces"):
        trace_sample = load_trace_sample(segy_path, trace_idx)
        trace_samples.append(trace_sample)

    # Check for consistent trace lengths and pad if necessary
    trace_lengths = [len(t) for t in trace_samples]
    if min(trace_lengths) != max(trace_lengths):
        print("Traces have different lengths. Applying zero padding for batch processing...")
        max_len = max(trace_lengths)
        for i in range(len(trace_samples)):
            if len(trace_samples[i]) < max_len:
                trace_samples[i] = np.pad(trace_samples[i], (0, max_len - len(trace_samples[i])), mode='constant')

    # Stack traces into a batch
    trace_batch = np.stack(trace_samples)

    # Get suggested batch size using the helper function
    suggested_batch, free_mb = get_suggested_batch_size()

    print(f"Using batched processing with batch size {suggested_batch} for statistical analysis")

    # Process the batch
    descriptor_settings = {
        'use_band_limited': general_spectral_settings['use_band_limited'],
        'shape': general_spectral_settings['shape'],
        'kmax': general_spectral_settings['kmax'],
        'int_val': general_spectral_settings['int_val'],
        'b1': general_spectral_settings['b1'],
        'b2': general_spectral_settings['b2'],
        'p_bandwidth': general_spectral_settings.get('p_bandwidth', 2),
        'roll_percent': general_spectral_settings.get('roll_percent', 0.85)
    }

    try:
        batch_results = dlogst_spec_descriptor_batch(
            trace_batch, dt, fmax=trace_batch.shape[1]//2,
            batch_size=suggested_batch,
            **descriptor_settings
        )

        # Convert batch results to individual descriptors
        sample_descriptors = []
        for i in range(trace_batch.shape[0]):
            di = {}
            for key in batch_results:
                val = batch_results[key]
                if isinstance(val, np.ndarray) and val.shape[0] == trace_batch.shape[0]:
                    di[key] = val[i]
                else:
                    di[key] = val
            sample_descriptors.append(di)

    except Exception as e:
        print(f"Batched processing failed: {e}. Falling back to non-batched processing.")
        # Fall back to non-batched processing as a last resort
        sample_descriptors = []
        for trace_idx in tqdm(sample_indices, desc="Processing random traces"):
            trace_sample = load_trace_sample(segy_path, trace_idx)
            descriptor = dlogst_spec_descriptor(
                trace_sample, dt, fmax=len(trace_sample)//2,
                use_band_limited=general_spectral_settings['use_band_limited'],
                shape=general_spectral_settings['shape'],
                kmax=general_spectral_settings['kmax'], int_val=general_spectral_settings['int_val'],
                b1=general_spectral_settings['b1'], b2=general_spectral_settings['b2'],
                p_bandwidth=general_spectral_settings.get('p_bandwidth', 2), roll_percent=general_spectral_settings.get('roll_percent', 0.85)
            )
            sample_descriptors.append(descriptor)

    # Pass general_spectral_settings to get_statistics_percentiles_and_display_settings
    display_settings = get_statistics_percentiles_and_display_settings(sample_descriptors, general_spectral_settings)

    settings = {**general_spectral_settings, **display_settings}

    # Calculate normalization factors for HFC and spectral decrease
    all_hfc = np.concatenate([d['hfc'] for d in sample_descriptors])
    all_spec_decrease = np.concatenate([d['spec_decrease'] for d in sample_descriptors])
    settings['hfc_p95'] = np.percentile(all_hfc, settings['hfc_percentile']) if all_hfc.size > 0 else 1.0
    settings['spec_decrease_p95'] = np.percentile(all_spec_decrease, settings['spec_decrease_percentile']) if all_spec_decrease.size > 0 else 1.0

    # Set color limits for various descriptors from direct settings
    for descriptor in ['Input Signal', 'Magnitude * Voice', 'Spectral Slope',
                      'Spectral Bandwidth', 'Spectral Rolloff', 'Mag*Voice Slope', 'WOSS']:
        if descriptor in settings:
            # This is now a direct min/max tuple from the dialog
            continue
        else:
            # Set defaults for any missing descriptor
            if descriptor in ['Input Signal', 'Magnitude * Voice', 'Mag*Voice Slope', 'WOSS']:
                settings[descriptor] = (-1, 1)
            else:
                settings[descriptor] = (0, 1)

    # Compatibility settings for old references
    settings['Spectral Decrease'] = (0, 1)
    settings['HFC'] = (0, 1)
    settings['Dominant Frequency'] = (0, settings['Frequency'][1])

    return settings

def select_outputs(is_2d_3d_mode=False):
    outputs = [
        "Seismic Amplitude",
        "Time-Frequency Magnitude Spectrogram",
        "Magnitude * Voice Spectrogram",
        "Normalized dominant frequencies",
        "Magnitude * Voice Slope Attribute",
        "Normalized Spectral Decrease",
        "Normalized High Frequency Content",
        "Spectral bandwidth",
        "Spectral roll-off",
        "WOSS (Weighted Optimum Spectral Shape)"
    ]

    # Remove computationally intensive options for 2D/3D mode
    if is_2d_3d_mode:
        outputs.remove("Time-Frequency Magnitude Spectrogram")
        outputs.remove("Magnitude * Voice Spectrogram")
        info_msg = "Note: 'Time-Frequency Magnitude Spectrogram' and 'Magnitude * Voice Spectrogram' are disabled in 2D/3D mode for performance reasons"
    else:
        info_msg = "Select outputs to include:"

    selection_window = tk.Toplevel(root)
    selection_window.title("Select Outputs")
    selection_window.geometry("400x450")
    tk.Label(selection_window, text=info_msg, wraplength=350).pack(pady=5)
    frame = tk.Frame(selection_window)
    frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    scrollbar = tk.Scrollbar(frame, orient="vertical")
    listbox = tk.Listbox(frame, selectmode=tk.MULTIPLE, yscrollcommand=scrollbar.set, width=40, height=15)
    scrollbar.config(command=listbox.yview)
    scrollbar.pack(side="right", fill="y")
    listbox.pack(side="left", fill="both", expand=True)
    for output in outputs:
        listbox.insert(tk.END, output)
    # Default selection: Input Signal
    listbox.selection_set(0)

    selected_indices = []
    def on_ok():
        nonlocal selected_indices
        selected_indices = listbox.curselection()
        selection_window.destroy()
    tk.Button(selection_window, text="OK", command=on_ok).pack(pady=10)
    selection_window.wait_window()
    return [outputs[i] for i in selected_indices]

def analyze_and_plot_trace(trace_sample, trace_idx, dt, well_marker_name, plot_settings,
                           plot_marker=False, marker_value=None, hfc_normalized=None,
                           spec_decrease_normalized=None, descriptors=None, selected_outputs=None):
    if selected_outputs is None:
        selected_outputs = ["Seismic Amplitude"]  # Default to showing at least the input signal

    num_samples = len(trace_sample)
    time_vector = np.arange(num_samples) * dt

    if descriptors is None:
        descriptors = dlogst_spec_descriptor(
            trace_sample, dt, fmax=num_samples//2,
            use_band_limited=plot_settings['use_band_limited'],
            shape=plot_settings['shape'], kmax=plot_settings['kmax'],
            int_val=plot_settings['int_val'], b1=plot_settings['b1'], b2=plot_settings['b2'],
            p_bandwidth=plot_settings.get('p_bandwidth', 2), roll_percent=plot_settings.get('roll_percent', 0.85)
        )

    # Extract descriptors - only extract what we need and check if they exist
    # This is safer for batched mode where some keys might be missing
    freqst = descriptors.get('freqst', None)
    peak_freq = descriptors.get('peak_freq', None)
    spec_centroid = descriptors.get('spec_centroid', None)
    fdom = descriptors.get('fdom', None)
    spec_bandwidth = descriptors.get('spec_bandwidth', None)
    spec_rolloff = descriptors.get('spec_rolloff', None)
    mag_voice_slope = descriptors.get('mag_voice_slope', None)

    # Check if we're in batched mode
    batched_mode = plot_settings.get('batched_mode', False)

    if hfc_normalized is None or spec_decrease_normalized is None:
        raise ValueError("Global normalization for HFC and Spectral Decrease must be provided.")

    freq_limit_index = np.argmin(np.abs(freqst - plot_settings['Frequency'][1]))

    # Define subplot titles based on selected outputs with word wrapping
    subplot_titles = []
    for output in selected_outputs:
        if output == "Normalized Spectral Decrease":
            decrease_title = f"Normalized Spectral<br>Decrease<br>(Band [{plot_settings['b1']}-{plot_settings['b2']} Hz])" if plot_settings['use_band_limited'] else "Normalized<br>Spectral<br>Decrease<br>(Full-Band)"
            subplot_titles.append(decrease_title)
        else:
            subplot_titles.append(output.replace(" ", "<br>"))  # Wrap words with line breaks.

    fig = make_subplots(rows=1, cols=len(subplot_titles), subplot_titles=subplot_titles)

    # Define wider horizontal spacing
    colorbar_spacing = 0.025  # Increased from 0.01 to 0.025 for wider gaps
    initial_x = 1.02  # Increased from 1.005 to 1.02 for more space from plot edge

    # No need to calculate mag_voice here, it should be available directly in descriptors

    col_idx = 1
    for output in selected_outputs:
        if output == "Seismic Amplitude":
            fig.add_trace(go.Scatter(x=trace_sample, y=time_vector, mode='lines'), row=1, col=col_idx)
            fig.update_xaxes(title_text='Amplitude', row=1, col=col_idx,
                            range=plot_settings.get('Input Signal', [-1, 1]))
            col_idx += 1
        elif output == "Time-Frequency Magnitude Spectrogram":
            # Check if we're in batched mode
            batched_mode = plot_settings.get('batched_mode', False)

            # Check if mag is available in descriptors and we're not in batched mode
            if not batched_mode and 'mag' in descriptors and isinstance(descriptors['mag'], np.ndarray):
                mag = descriptors['mag']  # Get the mag array from descriptors
                fig.add_trace(go.Heatmap(
                    z=mag[:freq_limit_index, :].T,
                    x=freqst[:freq_limit_index],
                    y=time_vector,
                    colorscale=plot_settings['colormap'],
                    zmin=plot_settings.get("Magnitude Spectrogram", [None, None])[0], # Apply zmin from settings
                    zmax=plot_settings.get("Magnitude Spectrogram", [None, None])[1], # Apply zmax from settings
                    colorbar=dict(
                        title=dict(
                            text='Time-Frequency<br>Magnitude<br>Spectrogram',  # Wrapped colorbar title
                            side='right',
                            font=dict(size=12)  # Move titlefont into title dictionary as font
                        ),
                        x=initial_x + (col_idx - 2) * colorbar_spacing,
                        y=0.5,
                        len=0.4,
                        yanchor='middle',
                        xanchor='left',
                        tickfont=dict(size=10)
                    )
                ), row=1, col=col_idx)
                fig.update_xaxes(title_text='Frequency (Hz)', row=1, col=col_idx, range=plot_settings['Frequency'])
            else:
                # If mag is not available or we're in batched mode, display a message
                fig.add_annotation(
                    text="Time-Frequency Magnitude Spectrogram<br>not available in batched processing mode",
                    x=0.5, y=0.5,
                    xref=f"x{col_idx}", yref=f"y{col_idx}",
                    showarrow=False,
                    font=dict(size=12, color="red")
                )
                fig.update_xaxes(title_text='', row=1, col=col_idx)
            col_idx += 1
        elif output == "Magnitude * Voice Spectrogram":
            # Check if we're in batched mode
            batched_mode = plot_settings.get('batched_mode', False)

            # Check if mag_voice is available in descriptors and we're not in batched mode
            if not batched_mode and 'mag_voice' in descriptors and isinstance(descriptors['mag_voice'], np.ndarray):
                # Use mag_voice directly from descriptors
                fig.add_trace(go.Heatmap(
                    z=descriptors['mag_voice'][:freq_limit_index, :].T,
                    x=freqst[:freq_limit_index],
                    y=time_vector,
                    colorscale=plot_settings['colormap_mag_voice'],
                    zmin=plot_settings["Magnitude * Voice"][0],
                    zmax=plot_settings["Magnitude * Voice"][1],
                    colorbar=dict(
                        title=dict(
                            text='Magnitude<br>* Voice<br>Spectrogram',  # Wrapped colorbar title
                            side='right',
                            font=dict(size=12)  # Move titlefont into title dictionary as font
                        ),
                        x=initial_x + (col_idx - 2) * colorbar_spacing,
                        y=0.5,
                        len=0.4,
                        yanchor='middle',
                        xanchor='left',
                        tickfont=dict(size=10)
                    )
                ), row=1, col=col_idx)
                fig.update_xaxes(title_text='Frequency (Hz)', row=1, col=col_idx, range=plot_settings['Frequency'])
            else:
                # If mag_voice is not available or we're in batched mode, display a message
                fig.add_annotation(
                    text="Magnitude * Voice Spectrogram<br>not available in batched processing mode",
                    x=0.5, y=0.5,
                    xref=f"x{col_idx}", yref=f"y{col_idx}",
                    showarrow=False,
                    font=dict(size=12, color="red")
                )
                fig.update_xaxes(title_text='', row=1, col=col_idx)
            col_idx += 1
        elif output == "Normalized dominant frequencies":
            fig.add_trace(go.Scatter(x=peak_freq, y=time_vector, mode='lines', name='Peak Frequency'), row=1, col=col_idx)
            fig.add_trace(go.Scatter(x=spec_centroid, y=time_vector, mode='lines', name='Spectral Centroid'), row=1, col=col_idx)
            fig.add_trace(go.Scatter(x=fdom, y=time_vector, mode='lines', name='Dominant Frequency'), row=1, col=col_idx)
            fig.update_xaxes(title_text='Frequency (Hz)', row=1, col=col_idx, range=plot_settings['Frequency'])
            col_idx += 1
        elif output == "Magnitude * Voice Slope Attribute":
            fig.add_trace(go.Scatter(x=mag_voice_slope, y=time_vector, mode='lines'), row=1, col=col_idx)
            fig.update_xaxes(title_text='Slope', row=1, col=col_idx,
                            range=plot_settings.get('Mag*Voice Slope', [-1, 1]))
            col_idx += 1
        elif output == "Normalized Spectral Decrease":
            fig.add_trace(go.Scatter(x=spec_decrease_normalized, y=time_vector, mode='lines'), row=1, col=col_idx)
            fig.update_xaxes(title_text='Normalized Decrease', row=1, col=col_idx, range=plot_settings.get('Spectral Decrease', [0, 1]))
            col_idx += 1

        elif output == "Normalized High Frequency Content":
            fig.add_trace(go.Scatter(x=hfc_normalized, y=time_vector, mode='lines'), row=1, col=col_idx)
            fig.update_xaxes(title_text='Normalized Energy', row=1, col=col_idx, range=plot_settings.get('HFC', [0, 1]))
            col_idx += 1
        elif output == "Spectral bandwidth":
            fig.add_trace(go.Scatter(x=spec_bandwidth, y=time_vector, mode='lines', line=dict(color='green')), row=1, col=col_idx)
            fig.update_xaxes(title_text='Bandwidth (Hz)', row=1, col=col_idx, range=plot_settings.get('Spectral Bandwidth', [0, 50]))
            col_idx += 1
        elif output == "Spectral roll-off":
            fig.add_trace(go.Scatter(x=spec_rolloff, y=time_vector, mode='lines', line=dict(color='orange')), row=1, col=col_idx)
            fig.update_xaxes(title_text='Rolloff (Hz)', row=1, col=col_idx, range=plot_settings.get('Spectral Rolloff', [0, 50]))
            col_idx += 1
        elif output == "WOSS (Weighted Optimum Spectral Shape)":
            epsilon = plot_settings.get('epsilon', 1e-10)
            fdom_exponent = plot_settings.get('fdom_exponent', 2.0)
            # pull normalized dominant frequency from descriptors
            norm_fdom = descriptors.get("norm_fdom")
            denominator = hfc_normalized * (norm_fdom**fdom_exponent + epsilon)
            with np.errstate(divide='ignore', invalid='ignore'):
                woss = np.where(denominator > epsilon, mag_voice_slope / denominator, 0.0)
                woss[~np.isfinite(woss)] = 0.0
                woss = np.clip(woss, -1e6, 1e6)
            fig.add_trace(go.Scatter(x=woss, y=time_vector, mode='lines', line=dict(color='purple')), row=1, col=col_idx)
            fig.update_xaxes(title_text='WOSS Value', row=1, col=col_idx, range=plot_settings.get('WOSS', [-3, 3]))
            col_idx += 1

    if plot_marker and marker_value is not None:
        for i in range(1, len(subplot_titles) + 1):
            fig.add_hline(y=marker_value, line=dict(color='red', dash='dash'), row=1, col=i)

    for i in range(1, len(subplot_titles) + 1):
        fig.update_yaxes(title_text='Time (s)', row=1, col=i, range=plot_settings['Time (Y-axis)'][::-1])

    # Count the number of heatmap outputs that need colorbars
    heatmap_outputs = sum(1 for o in selected_outputs if o in ["Magnitude Spectrogram", "Magnitude * Voice"])

    fig.update_layout(
        title={
            'text': f"Spectral Descriptors for Trace from '{well_marker_name}'<br>(Trace Index {trace_idx}, fdom^{fdom_exponent:.1f})",
            'font': {'size': 15, 'family': 'Arial', 'color': 'black', 'weight': 'bold'},
            'x': 0,
            'y': 0.98,
            'xanchor': 'left',
            'yanchor': 'top',
        },
        height=850,  # Set to 850 for option 1, suboption 1
        width=max(800, 300 * len(subplot_titles) + 80 * heatmap_outputs),  # Width based on number of columns
        showlegend=False,
        margin=dict(r=70 + 80 * heatmap_outputs)  # Right margin for colorbars
    )
    fig.show()

def plot_seismic_section(trace_data, descriptors_list, dt, plot_settings, selected_outputs, header_loader, selection_mode, fixed_inline=None, fixed_crossline=None):
    """
    Plot seismic section with spectral attributes.

    This function creates a comprehensive visualization of seismic data and its
    spectral attributes. It can display multiple traces side-by-side with various
    spectral attributes including WOSS, dominant frequencies, spectral decrease, etc.

    Args:
        trace_data: List of dictionaries containing trace samples and metadata
        descriptors_list: List of dictionaries containing spectral descriptors for each trace
        dt: Sampling interval in seconds
        plot_settings: Dictionary of plot configuration parameters
        selected_outputs: List of attribute names to display
        header_loader: SegyHeaderLoader object containing survey geometry
        selection_mode: Integer indicating the trace selection mode used
        fixed_inline: Inline number for inline section display (optional)
        fixed_crossline: Crossline number for crossline section display (optional)

    Returns:
        None: Displays the plot using Plotly
    """
    # Calculate time vector based on trace sample length and sampling interval
    num_samples = len(trace_data[0]['trace_sample'])
    time_vector = np.arange(num_samples) * dt

    # Determine X-axis based on selection mode and 3D inline/crossline choice
    if selection_mode == 2 or (selection_mode == 6 and fixed_inline is not None):  # Specific inline or fixed inline in 3D
        x_axis = [header_loader.crosslines[np.where(header_loader.unique_indices == t['trace_idx'])[0][0]] for t in trace_data]
        x_label = "Crossline"
        title_suffix = f"Inline {fixed_inline}" if selection_mode == 6 else f"Inline {header_loader.inlines[np.where(header_loader.unique_indices == trace_data[0]['trace_idx'])[0][0]]}"
    elif selection_mode == 3 or (selection_mode == 6 and fixed_crossline is not None):  # Specific crossline or fixed crossline in 3D
        x_axis = [header_loader.inlines[np.where(header_loader.unique_indices == t['trace_idx'])[0][0]] for t in trace_data]
        x_label = "Inline"
        title_suffix = f"Crossline {fixed_crossline}" if selection_mode == 6 else f"Crossline {header_loader.crosslines[np.where(header_loader.unique_indices == trace_data[0]['trace_idx'])[0][0]]}"
    elif selection_mode == 4:  # Inline range
        x_axis = [header_loader.crosslines[np.where(header_loader.unique_indices == t['trace_idx'])[0][0]] for t in trace_data]
        x_label = "Crossline"
        title_suffix = f"Inline Range"
    elif selection_mode == 5:  # Polyline selection
        # For polyline selection, we'll use sequential numbering but add trace index labels
        x_axis = list(range(len(trace_data)))
        x_label = "Trace Position Along Polyline"
        title_suffix = f"Polyline Selection ({len(trace_data)} traces)"
    else:  # Default case for other selection modes (7 or any undefined mode)
        x_axis = list(range(len(trace_data)))  # Simple sequential numbering
        x_label = "Trace Index"
        title_suffix = "Selected Traces"

    # Prepare data matrices for each output with word-wrapped titles
    data_matrices = {}
    titles = {}
    unavailable_outputs = []

    # Check if we're in batched mode
    batched_mode = plot_settings.get('batched_mode', False)

    for output in selected_outputs:
        if output == "Seismic Amplitude":
            data_matrices[output] = np.array([t['trace_sample'] for t in trace_data]).T
            titles[output] = "Seismic<br>Amplitude"
        elif output == "Time-Frequency Magnitude Spectrogram":
            # In batched mode, Magnitude Spectrogram is not available
            if batched_mode or not any('mag' in d and isinstance(d['mag'], np.ndarray) for d in descriptors_list):
                unavailable_outputs.append(output)
                titles[output] = "Time-Frequency<br>Magnitude<br>Spectrogram"
            else:
                # This would be implemented if mag was available and we're not in batched mode
                unavailable_outputs.append(output)
                titles[output] = "Time-Frequency<br>Magnitude<br>Spectrogram"
        elif output == "Magnitude * Voice Spectrogram":
            # In batched mode, Magnitude * Voice is not available
            if batched_mode or not any('mag_voice' in d and isinstance(d['mag_voice'], np.ndarray) for d in descriptors_list):
                unavailable_outputs.append(output)
                titles[output] = "Magnitude<br>* Voice<br>Spectrogram"
            else:
                # This would be implemented if mag_voice was available and we're not in batched mode
                unavailable_outputs.append(output)
                titles[output] = "Magnitude<br>* Voice<br>Spectrogram"
        elif output == "Normalized dominant frequencies":
            data_matrices[output] = np.array([d['fdom'] for d in descriptors_list]).T  # Using fdom
            titles[output] = "Dominant<br>Frequency<br>(Hz)"
        elif output == "Magnitude * Voice Slope Attribute":
            data_matrices[output] = np.array([d['mag_voice_slope'] for d in descriptors_list]).T
            titles[output] = "Magnitude<br>* Voice<br>Slope"
        elif output == "Normalized Spectral Decrease":
            data_matrices[output] = np.array([d['spec_decrease'] / plot_settings['spec_decrease_p95'] for d in descriptors_list]).T
            titles[output] = f"Normalized<br>Spectral<br>Decrease<br>({'Band-Limited' if plot_settings['use_band_limited'] else 'Full-Band'})"
        elif output == "Normalized High Frequency Content":
            data_matrices[output] = np.array([d['hfc'] / plot_settings['hfc_p95'] for d in descriptors_list]).T
            titles[output] = "Normalized<br>HFC"
        elif output == "Spectral bandwidth":
            data_matrices[output] = np.array([d['spec_bandwidth'] for d in descriptors_list]).T
            titles[output] = "Spectral<br>Bandwidth<br>(Hz)"
        elif output == "Spectral roll-off":
            data_matrices[output] = np.array([d['spec_rolloff'] for d in descriptors_list]).T
            titles[output] = "Spectral<br>Rolloff<br>(Hz)"
        elif output == "WOSS (Weighted Optimum Spectral Shape)":
            epsilon = plot_settings.get('epsilon', 1e-10)
            fdom_exponent = plot_settings.get('fdom_exponent', 2.0)
            hfc_norm = np.array([d['hfc'] / plot_settings['hfc_p95'] for d in descriptors_list])
            norm_fdom = np.array([d['norm_fdom'] for d in descriptors_list])
            mag_voice_slope = np.array([d['mag_voice_slope'] for d in descriptors_list])
            denominator = hfc_norm * (norm_fdom**fdom_exponent + epsilon)
            # Apply a threshold to avoid division by very small values
            safe_denominator = np.where(np.abs(denominator) > epsilon, denominator, epsilon)
            # Now perform the division with the safe denominator
            with np.errstate(divide='ignore', invalid='ignore'):
                woss = np.where(
                    denominator > epsilon,
                    mag_voice_slope / safe_denominator,
                    0.0
                )
                woss[~np.isfinite(woss)] = 0.0
                woss = np.clip(woss, -1e6, 1e6)
            data_matrices[output] = woss.T
            titles[output] = f"WOSS<br>(fdom^{fdom_exponent:.1f})"

    # Create subplots
    num_outputs = len(selected_outputs)

    # Determine layout based on number of outputs
    if num_outputs <= 4:
        num_rows = 1
        num_cols = num_outputs
        vertical_spacing = None
        top_margin = 50  # default top margin
    else:
        num_rows = 2
        num_cols = min(4, (num_outputs + 1) // 2)
        vertical_spacing = 0.13  # moderate vertical spacing
        top_margin = 80  # moderate top margin

    # Create subplot titles in row-wise order
    subplot_titles = [titles[output] for output in selected_outputs]

    fig = make_subplots(rows=num_rows, cols=num_cols,
                        subplot_titles=subplot_titles,
                        vertical_spacing=vertical_spacing)

    # Define colorbar positioning parameters based on the new layout
    colorbar_spacing = 0.055  # Increased from 0.05 to widen horizontal gap between colorbars by ~10%
    initial_x = 1.02  # Starting position for the first colorbar

    # Define colormap mapping for each output
    colormap_mapping = {
        "Seismic Amplitude": plot_settings.get('section_colormap', 'viridis'),
        "Normalized dominant frequencies": plot_settings.get('colormap_dom_freq', 'rainbow'),
        "Magnitude * Voice Slope Attribute": plot_settings.get('colormap_mag_voice', 'plasma'),
        "Normalized Spectral Decrease": plot_settings.get('colormap_spec_decrease', 'rainbow'),
        "Normalized High Frequency Content": plot_settings.get('colormap_hfc', 'rainbow'),
        "Spectral bandwidth": plot_settings.get('colormap_bandwidth', 'rainbow'),
        "Spectral roll-off": plot_settings.get('colormap_rolloff', 'rainbow'),
        "WOSS (Weighted Optimum Spectral Shape)": plot_settings.get('colormap_woss', 'RdBu')
    }

    # Define colorscale ranges for each output
    colorscale_ranges = {
        "Seismic Amplitude": plot_settings.get('Input Signal', [-1, 1]),
        "Normalized dominant frequencies": plot_settings.get('Dominant Frequency', [0, 100]),
        "Magnitude * Voice Slope Attribute": plot_settings.get('Mag*Voice Slope', [-1, 1]),
        "Normalized Spectral Decrease": plot_settings.get('Spectral Decrease', [0, 1]),
        "Normalized High Frequency Content": plot_settings.get('HFC', [0, 1]),
        "Spectral bandwidth": plot_settings.get('Spectral Bandwidth', [0, 50]),
        "Spectral roll-off": plot_settings.get('Spectral Rolloff', [0, 50]),
        "WOSS (Weighted Optimum Spectral Shape)": plot_settings.get('WOSS', [-3, 3])
    }

    idx = 0
    for output in selected_outputs:
        # Calculate the row and column for each subplot
        if num_rows == 1:
            # Single row layout
            row = 1
            col = idx % num_cols + 1
        else:
            # Two-row layout
            row = idx // num_cols + 1
            col = idx % num_cols + 1

        # Skip if we've run out of grid positions
        if row > num_rows or col > num_cols:
            print(f"Warning: Not enough subplot positions for output {output}. Skipping.")
            continue

        # Handle unavailable outputs (like Magnitude * Voice in batched mode)
        if output in unavailable_outputs:
            # Add an empty trace to ensure the subplot is created
            fig.add_trace(go.Scatter(x=[None], y=[None], mode='lines', visible=False), row=row, col=col)
            # Add an annotation explaining why it's not available
            fig.add_annotation(
                text=f"{output} not available<br>in batched processing mode",
                x=0.5, y=0.5,
                xref=f"x{col}", yref=f"y{col}",
                showarrow=False,
                font=dict(size=12, color="red")
            )
            fig.update_xaxes(title_text=x_label, row=row, col=col)
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
            idx += 1
            continue

        # Get the appropriate colorscale for this output
        colorscale = colormap_mapping.get(output, 'viridis')

        # Get the colorscale range for this output
        zmin, zmax = colorscale_ranges.get(output, [None, None])

        colorbar_title = output.split('<br>')[0]  # Use first line for colorbar title, wrapped if needed
        if '<br>' in output:
            colorbar_title = '<br>'.join(output.split('<br>')[:2])  # Take first two lines for longer titles

        # Use idx instead of col to ensure unique x positions for each colorbar
        colorbar_x = initial_x + idx * colorbar_spacing

        fig.add_trace(
            go.Heatmap(
                z=data_matrices[output],
                x=x_axis,
                y=time_vector,
                colorscale=colorscale,  # Use the mapped colorscale
                zmin=zmin,  # Apply min value for color scale
                zmax=zmax,  # Apply max value for color scale
                colorbar=dict(
                    title=dict(
                        text=colorbar_title,  # Wrapped colorbar title
                        side='right',
                        font=dict(size=12)
                    ),
                    x=colorbar_x,
                    y=0.5,
                    len=0.8,  # Slightly longer for seismic sections
                    yanchor='middle',
                    xanchor='left',
                    tickfont=dict(size=10)
                )
            ),
            row=row, col=col
        )
        fig.update_xaxes(title_text=x_label, row=row, col=col)
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])

        idx += 1

    section_type = "2D" if selection_mode in [2, 3, 4, 5] else "3D"

    # Calculate appropriate height based on number of rows and section type
    if num_rows == 1:
        figure_height = 900  # Increased from 700 to make single-row subplots taller
    else:
        # Increase height for multi-row layouts to allocate more height to subplots
        figure_height = 1200  # Increased from 980 to make two-row subplots taller

    fig.update_layout(
        title=dict(
            text=f"{section_type} Seismic Section Analysis - {title_suffix}",
            y=0.98,
            x=0,
            xanchor='left',
            yanchor='top',
            font=dict(size=15, family='Arial', color='black', weight='bold')
        ),
        height=figure_height,
        width=max(800, 450 * num_cols + 80 * num_cols),
        showlegend=False,
        margin=dict(r=70 + 80 * num_outputs, t=top_margin)
    )
    # Set subplot (spectral descriptor) title font size to 17
    for annotation in fig['layout']['annotations']:
        annotation['font'] = dict(size=15, family='Arial', color='black', weight='bold')
    fig.show()

def get_range_input(title="Input Range", initial_values=(100, 150, 200, 250)):
    """
    Create a custom dialog with separate inputs for inline and crossline ranges.

    Args:
        title: Dialog window title
        initial_values: Tuple of (min_inline, max_inline, min_crossline, max_crossline)

    Returns:
        Tuple of (min_inline, max_inline, min_crossline, max_crossline) or None if canceled
    """
    dialog = tk.Toplevel(root)
    dialog.title(title)
    dialog.grab_set()  # Make dialog modal

    # Set dialog position to center of screen
    dialog.geometry("350x170")
    dialog.resizable(False, False)

    # Create a frame for the form
    form_frame = tk.Frame(dialog, padx=20, pady=10)
    form_frame.pack(fill=tk.BOTH, expand=True)

    # Create input fields with labels
    tk.Label(form_frame, text="Inline Range:").grid(row=0, column=0, sticky="w", pady=(5, 0))

    inline_frame = tk.Frame(form_frame)
    inline_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
    tk.Label(inline_frame, text="Min:").pack(side=tk.LEFT, padx=(0, 5))
    min_inline_entry = tk.Entry(inline_frame, width=8)
    min_inline_entry.pack(side=tk.LEFT, padx=(0, 10))
    min_inline_entry.insert(0, str(initial_values[0]))

    tk.Label(inline_frame, text="Max:").pack(side=tk.LEFT, padx=(0, 5))
    max_inline_entry = tk.Entry(inline_frame, width=8)
    max_inline_entry.pack(side=tk.LEFT)
    max_inline_entry.insert(0, str(initial_values[1]))

    tk.Label(form_frame, text="Crossline Range:").grid(row=2, column=0, sticky="w", pady=(5, 0))

    crossline_frame = tk.Frame(form_frame)
    crossline_frame.grid(row=3, column=0, sticky="ew", pady=(0, 10))

    tk.Label(crossline_frame, text="Min:").pack(side=tk.LEFT, padx=(0, 5))
    min_crossline_entry = tk.Entry(crossline_frame, width=8)
    min_crossline_entry.pack(side=tk.LEFT, padx=(0, 10))
    min_crossline_entry.insert(0, str(initial_values[2]))

    tk.Label(crossline_frame, text="Max:").pack(side=tk.LEFT, padx=(0, 5))
    max_crossline_entry = tk.Entry(crossline_frame, width=8)
    max_crossline_entry.pack(side=tk.LEFT)
    max_crossline_entry.insert(0, str(initial_values[3]))

    # Create button frame
    button_frame = tk.Frame(dialog)
    button_frame.pack(fill=tk.X, padx=20, pady=(0, 10))

    result = [None]  # Using a list to store the result since nonlocal is not needed for lists

    def on_ok():
        try:
            result[0] = (
                int(min_inline_entry.get()),
                int(max_inline_entry.get()),
                int(min_crossline_entry.get()),
                int(max_crossline_entry.get())
            )
            dialog.destroy()
        except ValueError:
            messagebox.showerror("Error", "Please enter valid integer values for all fields.", parent=dialog)

    def on_cancel():
        dialog.destroy()

    tk.Button(button_frame, text="OK", command=on_ok, width=10).pack(side=tk.LEFT, padx=(0, 10))
    tk.Button(button_frame, text="Cancel", width=10, command=on_cancel).pack(side=tk.LEFT)

    # Wait for the dialog to be closed
    dialog.wait_window()
    return result[0]

def get_3d_section_selection(min_inline, max_inline, min_crossline, max_crossline):
    """
    Create a custom dialog to select 3D section display type and specific inline/crossline number.

    Args:
        min_inline, max_inline: Range of inline values
        min_crossline, max_crossline: Range of crossline values

    Returns:
        Tuple of (line_choice, fixed_value) where:
        - line_choice: 1 for Specific Inline, 2 for Specific Crossline, or None if canceled
        - fixed_value: Selected inline or crossline number, or None if canceled
    """
    dialog = tk.Toplevel(root)
    dialog.title("3D Section Selection")
    dialog.grab_set()  # Make dialog modal
    dialog.geometry("350x150")
    dialog.resizable(False, False)

    # Create a frame for the form
    form_frame = tk.Frame(dialog, padx=20, pady=10)
    form_frame.pack(fill=tk.BOTH, expand=True)

    # Display type selection
    tk.Label(form_frame, text=f"Select display type for IL {min_inline}-{max_inline}, XL {min_crossline}-{max_crossline}:").pack(anchor="w", pady=(5, 0))
    display_type_var = tk.StringVar(value="Inline")
    display_frame = tk.Frame(form_frame)
    display_frame.pack(fill=tk.X, pady=(5, 10))

    tk.Radiobutton(display_frame, text="Specific Inline", variable=display_type_var, value="Inline").pack(side=tk.LEFT, padx=(0, 20))
    tk.Radiobutton(display_frame, text="Specific Crossline", variable=display_type_var, value="Crossline").pack(side=tk.LEFT)

    # Number input
    number_frame = tk.Frame(form_frame)
    number_frame.pack(fill=tk.X, pady=(0, 10))

    number_label = tk.Label(number_frame, text="Enter Inline Number:")
    number_label.pack(side=tk.LEFT, padx=(0, 5))
    number_entry = tk.Entry(number_frame, width=10)
    number_entry.pack(side=tk.LEFT)
    number_entry.insert(0, str(min_inline))

    # Update label and entry based on selection
    # The *args parameter is required by trace_add but not used directly
    def update_label(*args):
        # args contains the name, mode, and other info from the trace_add callback
        # but we don't need to use it directly
        if display_type_var.get() == "Inline":
            number_label.config(text="Enter Inline Number:")
            number_entry.delete(0, tk.END)
            number_entry.insert(0, str(min_inline))
        else:
            number_label.config(text="Enter Crossline Number:")
            number_entry.delete(0, tk.END)
            number_entry.insert(0, str(min_crossline))

    display_type_var.trace_add("write", update_label)

    # Buttons
    button_frame = tk.Frame(dialog)
    button_frame.pack(fill=tk.X, padx=20, pady=(0, 10))

    result = [None, None]  # [line_choice, fixed_value]

    def on_ok():
        try:
            value = int(number_entry.get())
            if display_type_var.get() == "Inline":
                if not (min_inline <= value <= max_inline):
                    messagebox.showerror("Error", f"Inline number must be between {min_inline} and {max_inline}.", parent=dialog)
                    return
                result[0] = 1  # Specific Inline
                result[1] = value
            else:
                if not (min_crossline <= value <= max_crossline):
                    messagebox.showerror("Error", f"Crossline number must be between {min_crossline} and {max_crossline}.", parent=dialog)
                    return
                result[0] = 2  # Specific Crossline
                result[1] = value
            dialog.destroy()
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid integer.", parent=dialog)

    def on_cancel():
        dialog.destroy()

    tk.Button(button_frame, text="OK", command=on_ok, width=10).pack(side=tk.LEFT, padx=(0, 10))
    tk.Button(button_frame, text="Cancel", width=10, command=on_cancel).pack(side=tk.RIGHT)

    dialog.protocol("WM_DELETE_WINDOW", on_cancel)  # Handle window close

    # Center dialog on parent window
    dialog.update_idletasks()
    width = dialog.winfo_width()
    height = dialog.winfo_height()
    x = root.winfo_rootx() + (root.winfo_width() // 2) - (width // 2)
    y = root.winfo_rooty() + (root.winfo_height() // 2) - (height // 2)
    dialog.geometry(f"{width}x{height}+{x}+{y}")

    dialog.wait_window()
    return tuple(result)

# Add this new helper function after other plotting functions

def add_output_to_subplot(fig, output, trace_data_item, descriptors, time_vector, row, col, plot_settings, hfc_p95, spec_decrease_p95):
    """Helper function to add a specific output to a subplot."""
    if output == "Seismic Amplitude":
        fig.add_trace(go.Scatter(x=trace_data_item['trace_sample'], y=time_vector, mode='lines'), row=row, col=col)
        fig.update_xaxes(title_text='Amplitude', row=row, col=col, range=plot_settings.get('Input Signal', [-1, 1]))
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])

    elif output == "Time-Frequency Magnitude Spectrogram":
        # Check if we're in batched mode
        batched_mode = plot_settings.get('batched_mode', False)

        # Check if mag is available in descriptors and we're not in batched mode
        if not batched_mode and 'mag' in descriptors and isinstance(descriptors['mag'], np.ndarray):
            mag = descriptors['mag']
            freqst = descriptors['freqst']
            freq_limit_index = np.argmin(np.abs(freqst - plot_settings['Frequency'][1]))
            fig.add_trace(go.Heatmap(
                z=mag[:freq_limit_index, :].T,
                x=freqst[:freq_limit_index],
                y=time_vector,
                colorscale=plot_settings['colormap'],
                zmin=plot_settings.get("Magnitude Spectrogram", [None, None])[0], # Apply zmin from settings
                zmax=plot_settings.get("Magnitude Spectrogram", [None, None])[1], # Apply zmax from settings
                colorbar=dict(
                    title=dict(
                        text='Time-Frequency<br>Magnitude<br>Spectrogram',  # Wrapped colorbar title
                        side='right',
                        font=dict(size=12)  # Move titlefont into title dictionary as font
                    ),
                    x=1.02 + (col - 1) * 0.05,  # Adjusted to place colorbar on the right
                    y=0.5,
                    len=0.4,
                    yanchor='middle',
                    xanchor='left',
                    tickfont=dict(size=10)
                )
            ), row=row, col=col)
            fig.update_xaxes(title_text='Frequency (Hz)', row=row, col=col, range=plot_settings['Frequency'])
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
        else:
            # If mag is not available or we're in batched mode, display a message
            fig.add_annotation(
                text="Time-Frequency Magnitude Spectrogram<br>not available in batched processing mode",
                x=0.5, y=0.5,
                xref=f"x{col}", yref=f"y{col}",
                showarrow=False,
                font=dict(size=12, color="red")
            )
            # Add an empty trace to ensure the subplot is created
            fig.add_trace(go.Scatter(x=[None], y=[None], mode='lines', visible=False), row=row, col=col)
            fig.update_xaxes(title_text='Frequency (Hz)', row=row, col=col, range=plot_settings['Frequency'])
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Magnitude * Voice Spectrogram":
        # Check if we're in batched mode
        batched_mode = plot_settings.get('batched_mode', False)

        # Check if mag_voice is available in descriptors and we're not in batched mode
        if not batched_mode and 'mag_voice' in descriptors and isinstance(descriptors['mag_voice'], np.ndarray):
            mag_voice = descriptors['mag_voice']
            freqst = descriptors['freqst']
            freq_limit_index = np.argmin(np.abs(freqst - plot_settings['Frequency'][1]))
            fig.add_trace(go.Heatmap(
                z=mag_voice[:freq_limit_index, :].T,
                x=freqst[:freq_limit_index],
                y=time_vector,
                colorscale=plot_settings['colormap_mag_voice'],
                zmin=plot_settings["Magnitude * Voice"][0],
                zmax=plot_settings["Magnitude * Voice"][1],
                colorbar=dict(
                    title=dict(
                        text='Magnitude<br>* Voice<br>Spectrogram',  # Wrapped colorbar title
                        side='right',
                        font=dict(size=12)  # Move titlefont into title dictionary as font
                    ),
                    x=1.02 + (col - 1) * 0.05,  # Adjusted to place colorbar on the right
                    y=0.5,
                    len=0.4,
                    yanchor='middle',
                    xanchor='left',
                    tickfont=dict(size=10)
                )
            ), row=row, col=col)
            fig.update_xaxes(title_text='Frequency (Hz)', row=row, col=col, range=plot_settings['Frequency'])
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
        else:
            # If mag_voice is not available or we're in batched mode, display a message
            fig.add_annotation(
                text="Magnitude * Voice Spectrogram<br>not available in batched processing mode",
                x=0.5, y=0.5,
                xref=f"x{col}", yref=f"y{col}",
                showarrow=False,
                font=dict(size=12, color="red")
            )
            # Add an empty trace to ensure the subplot is created
            fig.add_trace(go.Scatter(x=[None], y=[None], mode='lines', visible=False), row=row, col=col)
            fig.update_xaxes(title_text='Frequency (Hz)', row=row, col=col, range=plot_settings['Frequency'])
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Normalized dominant frequencies":
        peak_freq = descriptors['peak_freq']
        spec_centroid = descriptors['spec_centroid']
        fdom = descriptors['fdom']
        fig.add_trace(go.Scatter(x=peak_freq, y=time_vector, mode='lines', name='Peak Frequency'), row=row, col=col)
        fig.add_trace(go.Scatter(x=spec_centroid, y=time_vector, mode='lines', name='Spectral Centroid'), row=row, col=col)
        fig.add_trace(go.Scatter(x=fdom, y=time_vector, mode='lines', name='Dominant Frequency'), row=row, col=col)
        fig.update_xaxes(title_text='Frequency (Hz)', row=row, col=col, range=plot_settings['Frequency'])
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Magnitude * Voice Slope Attribute":
        mag_voice_slope = descriptors['mag_voice_slope']
        fig.add_trace(go.Scatter(x=mag_voice_slope, y=time_vector, mode='lines'), row=row, col=col)
        fig.update_xaxes(title_text='Slope', row=row, col=col, range=plot_settings.get('Mag*Voice Slope', [-1, 1]))
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Normalized Spectral Decrease":
        spec_decrease_normalized = descriptors['spec_decrease'] / spec_decrease_p95 if spec_decrease_p95 != 0 else descriptors['spec_decrease']
        fig.add_trace(go.Scatter(x=spec_decrease_normalized, y=time_vector, mode='lines'), row=row, col=col)
        fig.update_xaxes(title_text='Normalized Decrease', row=row, col=col, range=plot_settings.get('Spectral Decrease', [0, 1]))
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Normalized High Frequency Content":
        hfc_normalized = descriptors['hfc'] / hfc_p95 if hfc_p95 != 0 else descriptors['hfc']
        fig.add_trace(go.Scatter(x=hfc_normalized, y=time_vector, mode='lines'), row=row, col=col)
        fig.update_xaxes(title_text='Normalized Energy', row=row, col=col, range=plot_settings.get('HFC', [0, 1]))
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Spectral bandwidth":
        spec_bandwidth = descriptors['spec_bandwidth']
        fig.add_trace(go.Scatter(x=spec_bandwidth, y=time_vector, mode='lines', line=dict(color='green')), row=row, col=col)
        fig.update_xaxes(title_text='Bandwidth (Hz)', row=row, col=col, range=plot_settings.get('Spectral Bandwidth', [0, 50]))
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Spectral roll-off":
        spec_rolloff = descriptors['spec_rolloff']
        fig.add_trace(go.Scatter(x=spec_rolloff, y=time_vector, mode='lines', line=dict(color='orange')), row=row, col=col)
        fig.update_xaxes(title_text='Rolloff (Hz)', row=row, col=col, range=plot_settings.get('Spectral Rolloff', [0, 50]))
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "WOSS (Weighted Optimum Spectral Shape)":
        epsilon = plot_settings.get('epsilon', 1e-10)
        fdom_exponent = plot_settings.get('fdom_exponent', 2.0)
        hfc_normalized = descriptors['hfc'] / hfc_p95
        norm_fdom = descriptors['norm_fdom']
        mag_voice_slope = descriptors['mag_voice_slope']
        denominator = hfc_normalized * (norm_fdom**fdom_exponent + epsilon)
        with np.errstate(divide='ignore', invalid='ignore'):
            woss = np.where(denominator > epsilon, mag_voice_slope / denominator, 0.0)
            woss[~np.isfinite(woss)] = 0.0
            woss = np.clip(woss, -1e6, 1e6)
        fig.add_trace(go.Scatter(x=woss, y=time_vector, mode='lines', line=dict(color='purple')), row=row, col=col)
        fig.update_xaxes(title_text='WOSS Value', row=row, col=col, range=plot_settings.get('WOSS', [-3, 3]))
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])

    # Add marker if applicable
    if trace_data_item.get('marker_value') is not None:
        fig.add_hline(y=trace_data_item['marker_value'], line=dict(color='red', dash='dash'), row=row, col=col)

def parse_polyline_string(coord_string):
    """
    Parse a string of polyline coordinates into a list of vertex tuples.

    This function converts a semicolon-separated string of coordinate pairs
    into a list of (x,y) tuples for use in polyline-based trace selection.

    Args:
        coord_string: String in format 'x1,y1; x2,y2; ...' where each pair
                     represents a vertex of the polyline

    Returns:
        list: List of (x,y) tuples representing polyline vertices

    Raises:
        ValueError: If the string format is invalid or contains fewer than 2 vertices
    """
    vertices = []
    pairs = coord_string.strip().split(';')

    # Handle empty string case
    if not pairs or not pairs[0]:
        raise ValueError("Coordinate string is empty.")

    # Process each coordinate pair
    for pair in pairs:
        try:
            coords = pair.strip().split(',')
            if len(coords) != 2:
                raise ValueError(f"Invalid coordinate pair format: '{pair}'. Expected 'x,y'.")

            # Convert to floating point values
            x = float(coords[0].strip())
            y = float(coords[1].strip())
            vertices.append((x, y))
        except ValueError as e:
            raise ValueError(f"Could not parse coordinate pair '{pair}': {e}")

    # Ensure we have at least 2 vertices to define a polyline
    if len(vertices) < 2:
        raise ValueError("Polyline must have at least 2 vertices.")

    return vertices

def distance_point_to_segment(px, py, x1, y1, x2, y2):
    """
    Calculate the shortest distance from a point to a line segment.

    This function computes the minimum distance from point (px,py) to the line
    segment defined by endpoints (x1,y1) and (x2,y2). It handles special cases
    like zero-length segments and points that project outside the segment.

    Args:
        px, py: Coordinates of the point
        x1, y1: Coordinates of the first endpoint of the line segment
        x2, y2: Coordinates of the second endpoint of the line segment

    Returns:
        float: The minimum distance from the point to the line segment
    """
    # Calculate squared length of the segment
    seg_len_sq = (x2 - x1)**2 + (y2 - y1)**2

    # Handle degenerate case where segment is essentially a point
    if seg_len_sq < 1e-12:
        return math.sqrt((px - x1)**2 + (py - y1)**2)

    # Project point onto the line containing the segment
    # Parameter t represents position along the infinite line (0=start, 1=end)
    t = ((px - x1) * (x2 - x1) + (py - y1) * (y2 - y1)) / seg_len_sq

    # Clamp t to the segment [0, 1] to handle points that project outside the segment
    t = max(0, min(1, t))

    # Find the closest point on the segment
    closest_x = x1 + t * (x2 - x1)
    closest_y = y1 + t * (y2 - y1)

    # Calculate Euclidean distance from the original point to the closest point on the segment
    dist = math.sqrt((px - closest_x)**2 + (py - closest_y)**2)
    return dist

def find_traces_near_polyline(header_loader, polyline_vertices, max_distance):
    """
    Find trace indices within a specified distance of a polyline.

    This function calculates the minimum distance from each trace location to
    a polyline defined by a series of vertices. Traces within the specified
    maximum distance are selected. It uses the distance_point_to_segment helper
    function to efficiently calculate distances to line segments.

    Args:
        header_loader: SegyHeaderLoader object containing trace coordinates
        polyline_vertices: List of (x,y) tuples defining the polyline vertices
        max_distance: Maximum distance threshold for trace selection

    Returns:
        list: Indices of traces within the specified distance of the polyline
    """
    selected_indices = []
    trace_coords = np.column_stack((header_loader.x_coords, header_loader.y_coords))

    print(f"\nFinding traces within {max_distance} units of the defined polyline...")
    # Use tqdm for progress bar to show processing status
    for i in tqdm(range(len(trace_coords)), desc="Checking trace proximity"):
        trace_x, trace_y = trace_coords[i]
        min_dist_to_polyline = float('inf')

        # Iterate through each segment of the polyline
        for j in range(len(polyline_vertices) - 1):
            x1, y1 = polyline_vertices[j]      # Start point of segment
            x2, y2 = polyline_vertices[j+1]    # End point of segment

            # Calculate distance from trace to this segment
            dist = distance_point_to_segment(trace_x, trace_y, x1, y1, x2, y2)

            # Keep track of minimum distance to any segment
            min_dist_to_polyline = min(min_dist_to_polyline, dist)

        # Check if the minimum distance is within the tolerance
        if min_dist_to_polyline <= max_distance:
            selected_indices.append(header_loader.unique_indices[i])

    print(f"Found {len(selected_indices)} traces near the polyline.")
    return selected_indices

# GPU Memory Management
def get_suggested_batch_size():
    """
    Determine optimal batch size based on available GPU memory.

    This function attempts to query the available GPU memory using CuPy
    and calculates an appropriate batch size for processing. If GPU memory
    information is not available, it falls back to conservative defaults.

    Returns:
        tuple: (suggested_batch_size, free_memory_mb)
            - suggested_batch_size: Recommended batch size for processing
            - free_memory_mb: Available GPU memory in megabytes
    """
    try:
        # Try to import CuPy and get GPU memory information
        import cupy as cp
        free_bytes, _ = cp.cuda.runtime.memGetInfo()
        free_mb = free_bytes / (1024 ** 2)

        # Calculate batch size based on available memory
        # Scale with available memory but keep within reasonable bounds
        suggested_batch = min(max(10, int(free_mb / 100)), 50)  # Adjust based on available GPU memory
    except Exception as e:
        # Fall back to conservative defaults if GPU memory info is unavailable
        free_mb = 4096  # fallback guess
        suggested_batch = 20  # Conservative default
        print(f"Could not determine optimal batch size from GPU memory: {e}. Using default batch size of {suggested_batch}.")

    print(f"Suggesting batch size: {suggested_batch} (free GPU RAM: {free_mb:.1f} MB)")
    return suggested_batch, free_mb

# Add this class after the SegyHeaderLoader class, around line 90
class AttributeSelector:
    def __init__(self, root, exportable_attrs):
        self.root = root
        self.exportable_attrs = exportable_attrs
        self.selected_attrs = []

    def show_dialog(self):
        attrs_window = tk.Toplevel(self.root)
        attrs_window.title("Select Attributes to Export")
        attrs_window.geometry("400x450")

        tk.Label(attrs_window, text="Select spectral attributes to export to SEG-Y:").pack(pady=5)
        frame = tk.Frame(attrs_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        scrollbar = tk.Scrollbar(frame, orient="vertical")
        listbox = tk.Listbox(frame, selectmode=tk.MULTIPLE, yscrollcommand=scrollbar.set, width=40, height=15)
        scrollbar.config(command=listbox.yview)
        scrollbar.pack(side="right", fill="y")
        listbox.pack(side="left", fill="both", expand=True)

        for attr in self.exportable_attrs:
            listbox.insert(tk.END, attr)

        def on_ok():
            self.selected_attrs = [self.exportable_attrs[i] for i in listbox.curselection()]
            attrs_window.destroy()

        tk.Button(attrs_window, text="OK", command=on_ok).pack(pady=10)
        attrs_window.wait_window()

        return self.selected_attrs

# Function to select attributes for export
def select_export_attributes(root, desired_attrs, first_descriptor, sample_length):
    """
    Creates a dialog for selecting attributes to export to SEG-Y files.
    Filters only feasible attributes for export, matching logic from 10b_3D_WOSS_InitEq_GPU_ILXL_Merged.py.
    Returns a list of selected attribute names.
    """
    # Filter exportable attributes as in 10b
    exportable_attrs = [
        key for key in desired_attrs
        if key in first_descriptor and isinstance(first_descriptor[key], np.ndarray) and len(first_descriptor[key]) == sample_length
    ]
    # Optionally add WOSS if possible
    if (
        'hfc' in first_descriptor and
        'norm_fdom' in first_descriptor and
        'mag_voice_slope' in first_descriptor
    ):
        exportable_attrs.append('WOSS')

    # Create a dialog window
    dialog = tk.Toplevel(root)
    dialog.title("Select Attributes to Export")
    dialog.geometry("400x300")
    dialog.resizable(False, False)
    dialog.grab_set()  # Make the dialog modal

    tk.Label(dialog, text="Select spectral attributes to export to SEG-Y:", pady=10).pack()

    listbox = tk.Listbox(dialog, selectmode=tk.MULTIPLE, width=40, height=15)
    for attr in exportable_attrs:
        listbox.insert(tk.END, attr)
    listbox.pack(padx=10, pady=5, fill=tk.BOTH, expand=True)

    result = []
    def on_ok():
        nonlocal result
        selected_indices = listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("Warning", "No attributes selected. Please select at least one attribute.")
            return
        result = [listbox.get(i) for i in selected_indices]
        dialog.destroy()

    tk.Button(dialog, text="OK", command=on_ok, width=10).pack(pady=10)
    dialog.wait_window()
    return result

# MAIN SCRIPT RUN
if __name__ == "__main__":
    """
    Main execution flow of the WOSS seismic analysis application.

    This section implements the main workflow of the application:
    1. Loading SEG-Y data and well markers
    2. Setting analysis parameters
    3. Displaying basemap visualization
    4. Providing interactive trace selection options
    5. Computing spectral attributes
    6. Visualizing results
    7. Exporting calculated attributes
    """
    # Initialize Tkinter root window but keep it hidden
    root = tk.Tk()
    root.withdraw()  # Hide the main window but keep it active

    print("\nSTEP 1: Loading input files...")
    # Select SEG-Y file
    segy_path = filedialog.askopenfilename(
        title="Select SEG-Y file",
        filetypes=[("SEG-Y files", "*.sgy *.segy"), ("All files", "*.*")]
    )
    if not segy_path:
        print("No SEG-Y file selected. Exiting.")
        exit()
    print(f"Selected SEG-Y file: {segy_path}")

    header_loader = SegyHeaderLoader(segy_path)
    dt = get_sampling_interval(segy_path)
    print(f"Sampling interval: {dt} seconds")

    print("\nSTEP 2: Setting spectral analysis parameters and computing global normalization...")
    plot_settings = get_plot_settings(dt, header_loader, segy_path)

    well_df = None
    well_df_basemap = None
    well_df_spectral = None
    use_well_markers = messagebox.askyesno("Well Markers", "Do you want to use well markers?")
    if use_well_markers:
        excel_path = filedialog.askopenfilename(
            title="Select Excel file with well markers",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if excel_path:
            try:
                well_df = load_excel_data(excel_path)
                print(f"Loaded well markers from: {excel_path}")

                # Keep the first surface selection for basemap plot
                selected_surfaces_basemap = select_surfaces(well_df, title="Select Surfaces for Basemap Plot")
                well_df_basemap = well_df[well_df["Surface"].isin(selected_surfaces_basemap)]

                # Remove the second surface selection and use full well_df for spectral analysis
                # This works because the well marker pair selection already has filtering capability
                well_df_spectral = well_df  # Use the complete dataset instead of filtering

                print(f"Selected {len(selected_surfaces_basemap)} surfaces for basemap")

            except Exception as e:
                print(f"Error loading well markers: {e}")
                well_df = None
                well_df_basemap = None
                well_df_spectral = None
        else:
            well_df = None
            well_df_basemap = None
            well_df_spectral = None
    else:
        well_df = None
        well_df_basemap = None
        well_df_spectral = None

    # Show initial basemap - now using the standard version only
    plot_basemap_with_wells(header_loader, well_df_basemap, segy_path)

    # Remove the interactive basemap prompt and display code here
    # The interactive basemap will only be shown during manual selection

    # Store previously used indices for convenience
    stored_indices = []

    # Initialize batch descriptor and batch_size
    dlogst_spec_descriptor_batch = None
    batch_size = None

    # MAIN LOOP FOR TRACE SELECTION AND ANALYSIS
    while True:
        print("\nSTEP 3: Choose selection mode...")
        selection_mode = simpledialog.askinteger(
            "Selection Mode",
            "Choose selection mode:\n"
            "1. By well markers\n"
            "2. Single inline (all crosslines)\n"
            "3. Single crossline (all inlines)\n"
            "4. By inline/crossline section (3D section)\n"
            "5. By Polyline (define line on map)",  # Updated description
            minvalue=1,
            maxvalue=5
        )

        if selection_mode is None:
            print("No selection mode chosen. Exiting.")
            break

        # After user selects mode, decide batch mode
        if selection_mode in [2, 3, 4, 5]:
            try:
                from dlogst_spec_descriptor_gpu_init import dlogst_spec_descriptor_gpu_2d_chunked as dlogst_spec_descriptor_batch
            except ImportError:
                print("Batch GPU descriptor module (f32_pinmem) not found, falling back to single trace mode.")
                dlogst_spec_descriptor_batch = None
                batch_size = None
            else:
                suggested_batch, free_mb = get_suggested_batch_size()
                batch_size = simpledialog.askinteger(
                    "Batch Size for GPU",
                    f"Estimated free GPU memory: {free_mb:.1f} MB\n"
                    f"Suggested batch size: {suggested_batch}\n\n"
                    f"Enter batch size (max traces per chunk):",
                    initialvalue=suggested_batch,
                    minvalue=10,
                    maxvalue=4000,
                )
                if batch_size is None:
                    batch_size = suggested_batch
        else:
            dlogst_spec_descriptor_batch = None
            batch_size = None

        trace_data = []
        plot_marker = False
        chosen_indices = [] # Initialize chosen_indices

        # Updated polyline selection mode
        if selection_mode == 5:  # Polyline selection
            print("\nPolyline Selection Mode:")

            # First display the interactive basemap for reference
            plot_interactive_basemap(header_loader, well_df_basemap, segy_path)

            # Open our improved polyline selection UI
            polyline_vertices, chosen_indices = create_polyline_selection_ui()

            if not chosen_indices:
                print("Polyline selection cancelled or no traces selected.")
                continue

            # Load trace data for the selected indices
            trace_data = []
            for trace_idx in tqdm(chosen_indices, desc="Fetching traces"):
                trace = load_trace_sample(segy_path, trace_idx)
                trace_data.append({'trace_sample': trace, 'trace_idx': trace_idx})

            # Select outputs
            selected_outputs = select_outputs(is_2d_3d_mode=True)
            print("\nCalculating spectral descriptors...")

            display_keys_to_exclude = [
                'Frequency', 'Dominant Frequency', 'Time (Y-axis)',
                'hfc_p95', 'spec_decrease_p95', 'hfc_percentile', 'spec_decrease_percentile',
                'Input Signal', 'Magnitude * Voice', 'Spectral Slope',
                'Spectral Bandwidth', 'Spectral Rolloff', 'Mag*Voice Slope',
                'WOSS', 'Spectral Decrease', 'HFC', 'epsilon', 'sample_percent',
                'max_traces', 'fdom_exponent', 'use_band_limited',
                'colormap', 'colormap_mag_voice', 'colormap_dom_freq',
                'colormap_spec_decrease', 'colormap_hfc', 'colormap_bandwidth',
                'colormap_rolloff', 'colormap_woss', 'section_colormap',
                'colormap_mag_voice_slope'
            ]
            descriptor_settings = {k: v for k, v in plot_settings.items()
                                 if k not in display_keys_to_exclude}

            if dlogst_spec_descriptor_batch is not None:
                # Zero-pad traces to uniform length
                trace_lengths = [len(t['trace_sample']) for t in trace_data]
                if min(trace_lengths) != max(trace_lengths):
                    print("Traces have different lengths. Applying zero padding for batch processing...")
                    max_len = max(trace_lengths)
                    for t in trace_data:
                        if len(t['trace_sample']) < max_len:
                            t['trace_sample'] = np.pad(t['trace_sample'], (0, max_len - len(t['trace_sample'])), mode='constant')

                arr = np.stack([t['trace_sample'] for t in trace_data])
                # Filter descriptor_settings to only accepted keywords for dlogst_spec_descriptor_batch
                valid_keys = ['fmax', 'shape', 'kmax', 'int_val', 'b1', 'b2', 'p_bandwidth', 'roll_percent', 'batch_size', 'use_band_limited']
                filtered_descriptor_settings = {k: v for k, v in descriptor_settings.items() if k in valid_keys}
                descriptors_all = dlogst_spec_descriptor_batch(
                    arr, dt, fmax=arr.shape[1] // 2,
                    use_band_limited=plot_settings['use_band_limited'],
                    **filtered_descriptor_settings,
                    batch_size=batch_size
                )
                descriptors_list = []
                for i in tqdm(range(arr.shape[0]), desc="Calculating spectral descriptors"):  # progress bar here
                    di = {}
                    for key in descriptors_all:
                        val = descriptors_all[key]
                        if isinstance(val, np.ndarray) and val.shape[0] == arr.shape[0]:
                            di[key] = val[i]
                        else:
                            di[key] = val
                    descriptors_list.append(di)
            else:
                descriptors_list = [
                    dlogst_spec_descriptor(t['trace_sample'], dt, fmax=len(t['trace_sample'])//2, use_band_limited=plot_settings['use_band_limited'], **descriptor_settings)
                    for t in tqdm(trace_data, desc="Analyzing traces")
                ]

            # Plot the seismic section
            plot_seismic_section(trace_data, descriptors_list, dt, plot_settings, selected_outputs, header_loader, selection_mode)

        elif selection_mode == 1:
            # Existing code for well marker selection
            if well_df_spectral is not None and not well_df_spectral.empty:
                plot_marker = "TWT_sec" in well_df_spectral.columns and messagebox.askyesno(
                    "TWT Marker Option", "Plot TWT marker from Excel file?"
                )
                selected_markers = select_well_marker_pairs(well_df_spectral)
                if not selected_markers:
                    print("\nNo well markers selected for this set.")
                    continue

                print(f"\nSelected {len(selected_markers)} well-marker pairs for analysis.")
                for marker in tqdm(selected_markers, desc="Loading traces"):
                    trace_idx = get_nearest_trace_index(header_loader, marker["X"], marker["Y"])
                    trace_sample = load_trace_sample(segy_path, trace_idx)
                    key = f"{marker['Well']}_{marker['Surface']}"
                    trace_data.append({
                        'trace_sample': trace_sample,
                        'trace_idx': trace_idx,
                        'well_marker_name': key,
                        'marker_value': marker["TWT_sec"] if plot_marker and "TWT_sec" in marker else None
                    })

                # Let user select which outputs to display
                selected_outputs = select_outputs()

                # NEW: Add plot mode selection
                if len(trace_data) > 1:
                    plot_mode = simpledialog.askinteger(
                        "Plot Mode",
                        "Choose plot mode:\n"
                        "1. Plot each trace with all selected outputs (original)\n"
                        "2. Plot each output with all selected traces (comparison)",
                        minvalue=1,
                        maxvalue=2
                    )
                    if plot_mode is None:
                        plot_mode = 1  # Default to mode 1 if canceled
                else:
                    plot_mode = 1  # Default to mode 1 for single trace

                # Compute descriptors for all traces
                print("\nComputing spectral descriptors...")
                all_descriptors = []
                # Define the known valid calculation parameters for the descriptor functions
                valid_calc_params = [
                    'use_band_limited', 'shape', 'kmax', 'int_val',
                    'b1', 'b2', 'p_bandwidth', 'roll_percent'
                    # 'fdom_exponent' is used later for WOSS calculation, not directly by descriptor function
                    # 'epsilon' is used later for WOSS calculation, not directly by descriptor function
                ]
                # Create descriptor_settings containing only the valid calculation parameters
                descriptor_settings = {k: v for k, v in plot_settings.items() if k in valid_calc_params}
                print(f"Using descriptor settings: {descriptor_settings}") # Debug print

                # Always use batched version for well marker analysis
                from dlogst_spec_descriptor_gpu_init import dlogst_spec_descriptor_gpu as dlogst_spec_descriptor
                from dlogst_spec_descriptor_gpu_init import dlogst_spec_descriptor_gpu_2d_chunked_mag as dlogst_spec_descriptor_batch

                # Check for consistent trace lengths and apply zero padding if necessary
                trace_lengths = [len(t['trace_sample']) for t in trace_data]
                if min(trace_lengths) != max(trace_lengths):
                    print("Traces have different lengths. Applying zero padding for batch processing...")
                    max_len = max(trace_lengths)
                    for t in trace_data:
                        if len(t['trace_sample']) < max_len:
                            t['trace_sample'] = np.pad(t['trace_sample'], (0, max_len - len(t['trace_sample'])), mode='constant')

                # Stack traces into a batch
                trace_batch = np.stack([t['trace_sample'] for t in trace_data])

                # Get suggested batch size using the helper function
                suggested_batch, _ = get_suggested_batch_size()

                print(f"Using batched processing with batch size {suggested_batch} for well marker analysis")

                try:
                    # Process the batch
                    batch_results = dlogst_spec_descriptor_batch(
                        trace_batch, dt,
                        fmax=trace_batch.shape[1]//2,
                        # Pass the filtered descriptor_settings
                        **descriptor_settings,
                        batch_size=suggested_batch
                    )

                    # Convert batch results to individual descriptors
                    all_descriptors = []
                    for i in range(trace_batch.shape[0]):
                        di = {}
                        for key in batch_results:
                            val = batch_results[key]
                            if isinstance(val, np.ndarray) and val.shape[0] == trace_batch.shape[0]:
                                di[key] = val[i]
                            else:
                                di[key] = val
                        all_descriptors.append(di)

                except Exception as e:
                    print(f"Batched processing failed: {e}. Falling back to non-batched processing.")
                    # Fall back to non-batched processing as a last resort
                    all_descriptors = []
                    for trace in tqdm(trace_data, desc="Processing traces"):
                        nyquist = (1 / dt) / 2
                        fmax_calc = min(len(trace['trace_sample']) // 2, int(nyquist / (1/(dt*len(trace['trace_sample'])))))
                        descriptor = dlogst_spec_descriptor(
                            trace['trace_sample'], dt, fmax=fmax_calc,
                            # Pass the filtered descriptor_settings
                            **descriptor_settings
                        )
                        all_descriptors.append(descriptor)

                # Compute global normalization values
                all_hfc = np.concatenate([d['hfc'] for d in all_descriptors])
                all_spec_decrease = np.concatenate([d['spec_decrease'] for d in all_descriptors])
                hfc_p95 = np.percentile(all_hfc, 95) if all_hfc.size > 0 else 1.0
                spec_decrease_p95 = np.percentile(all_spec_decrease, 95) if all_spec_decrease.size > 0 else 1.0

                # Plotting based on selected mode
                if plot_mode == 1:
                    # Mode 1: Plot each trace with all selected outputs (original behavior)
                    for i, trace in enumerate(trace_data):
                        descriptors = all_descriptors[i]
                        hfc_normalized = descriptors['hfc'] / hfc_p95
                        spec_decrease_normalized = descriptors['spec_decrease'] / spec_decrease_p95
                        analyze_and_plot_trace(
                            trace['trace_sample'], trace['trace_idx'], dt, trace['well_marker_name'],
                            plot_settings, plot_marker=plot_marker, marker_value=trace['marker_value'],
                            hfc_normalized=hfc_normalized, spec_decrease_normalized=spec_decrease_normalized,
                            descriptors=descriptors, selected_outputs=selected_outputs
                        )

                        # Ask if user wants to continue to next trace if multiple selected
                        if i < len(trace_data) - 1:
                            if not messagebox.askyesno("Continue", f"Continue to next trace? ({i+1}/{len(trace_data)})"):
                                break

                elif plot_mode == 2:
                    # Mode 2: Plot each output with all selected traces (comparison)
                    num_samples = len(trace_data[0]['trace_sample'])
                    time_vector = np.arange(num_samples) * dt

                    for output in selected_outputs:
                        # Create a subplot for each trace in the same figure
                        fig = make_subplots(
                            rows=1, cols=len(trace_data),
                            subplot_titles=[trace['well_marker_name'] for trace in trace_data],
                            shared_yaxes=True  # Share y-axes for better comparison
                        )

                        # Add each trace as a subplot
                        for i, (trace, descriptors) in enumerate(zip(trace_data, all_descriptors)):
                            add_output_to_subplot(
                                fig, output, trace, descriptors, time_vector,
                                row=1, col=i+1, plot_settings=plot_settings,
                                hfc_p95=hfc_p95, spec_decrease_p95=spec_decrease_p95
                            )

                        # Update figure layout
                        subplot_width = 300  # Base width per subplot
                        if output in ["Magnitude Spectrogram", "Magnitude * Voice"]:
                            subplot_width = 400  # Wider for heatmaps

                        fig.update_layout(
                            title=f"{output} for Selected Well Markers",
                            height=850,  # Set to 850 for option 1, suboption 1
                            width=max(800, subplot_width * len(trace_data)),
                            showlegend=output == "Normalized dominant frequencies",  # Show legend only for multi-line plots,
                            margin=dict(r=100)  # Add right margin for colorbars
                        )
                        fig.show()

            else:
                messagebox.showinfo("Info", "No well markers available for selection.")
                continue

        elif selection_mode == 2:  # Single inline (all crosslines)
            inline_choice = simpledialog.askinteger(
                "Inline Selection",
                "Specify an inline number:",
                minvalue=np.min(header_loader.inlines),
                maxvalue=np.max(header_loader.inlines)
            )
            if inline_choice is None:
                print("Inline selection cancelled.")
                continue
            # Filter traces by inline
            inline_mask = header_loader.inlines == inline_choice
            chosen_indices = header_loader.unique_indices[inline_mask]
            if len(chosen_indices) == 0:
                print(f"No traces available for inline {inline_choice}.")
                continue
            trace_data = []
            for trace_idx in tqdm(chosen_indices, desc="Fetching traces for inline"):  # add tqdm progress bar here
                trace = load_trace_sample(segy_path, trace_idx)
                trace_data.append({'trace_sample': trace, 'trace_idx': trace_idx})
            # Select outputs, restricting to 2D/3D-compatible options
            selected_outputs = select_outputs(is_2d_3d_mode=True)
            print("\nCalculating spectral descriptors...")

            display_keys_to_exclude = [
                'Frequency', 'Dominant Frequency', 'Time (Y-axis)',
                'hfc_p95', 'spec_decrease_p95', 'hfc_percentile', 'spec_decrease_percentile',  # Added percentile keys
                'Input Signal', 'Magnitude * Voice', 'Spectral Slope',
                'Spectral Bandwidth', 'Spectral Rolloff', 'Mag*Voice Slope',
                'WOSS', 'Spectral Decrease', 'HFC', 'epsilon', 'sample_percent',
                'max_traces', 'fdom_exponent', 'use_band_limited',
                'colormap', 'colormap_mag_voice', 'colormap_dom_freq',  # Add colormap settings
                'colormap_spec_decrease', 'colormap_hfc', 'colormap_bandwidth',
                'colormap_rolloff', 'colormap_woss', 'section_colormap',
                'colormap_mag_voice_slope',  # Added to exclude this key
                'Magnitude Spectrogram'  # Added to exclude this key
            ]
            descriptor_settings = {k: v for k, v in plot_settings.items()
                                 if k not in display_keys_to_exclude}

            if dlogst_spec_descriptor_batch is not None:
                # --- Patch: Zero-pad traces to uniform length ---
                max_len = max(len(t['trace_sample']) for t in trace_data)
                for t in trace_data:
                    trace = t['trace_sample']
                    if len(trace) < max_len:
                        t['trace_sample'] = np.pad(trace, (0, max_len - len(trace)), mode='constant')
                # -------------------------------------------------
                arr = np.stack([t['trace_sample'] for t in trace_data])
                descriptors_all = dlogst_spec_descriptor_batch(
                    arr, dt, fmax=arr.shape[1] // 2,
                    use_band_limited=plot_settings['use_band_limited'],
                    **{k: v for k, v in descriptor_settings.items() if k != 'use_band_limited'},
                    batch_size=batch_size
                )
                descriptors_list = []
                for i in tqdm(range(arr.shape[0]), desc="Calculating spectral descriptors"):  # progress bar here
                    di = {}
                    for key in descriptors_all:
                        val = descriptors_all[key]
                        if isinstance(val, np.ndarray) and val.shape[0] == arr.shape[0]:
                            di[key] = val[i]
                        else:
                            di[key] = val
                    descriptors_list.append(di)
            else:
                descriptors_list = [
                    dlogst_spec_descriptor(t['trace_sample'], dt, fmax=len(t['trace_sample'])//2, use_band_limited=plot_settings['use_band_limited'], **descriptor_settings)
                    for t in tqdm(trace_data, desc="Analyzing traces")
                ]

            # Plot the seismic section
            plot_seismic_section(trace_data, descriptors_list, dt, plot_settings, selected_outputs, header_loader, selection_mode)

        elif selection_mode == 3:  # Single crossline (all inlines)
            crossline_choice = simpledialog.askinteger(
                "Crossline Selection",
                "Specify a crossline number:",
                minvalue=np.min(header_loader.crosslines),
                maxvalue=np.max(header_loader.crosslines)
            )
            if crossline_choice is None:
                print("Crossline selection cancelled.")
                continue
            # Filter traces by crossline
            crossline_mask = header_loader.crosslines == crossline_choice
            chosen_indices = header_loader.unique_indices[crossline_mask]
            if len(chosen_indices) == 0:
                print(f"No traces available for crossline {crossline_choice}.")
                continue
            trace_data = []
            for trace_idx in tqdm(chosen_indices, desc="Fetching traces"):
                trace = load_trace_sample(segy_path, trace_idx)
                trace_data.append({'trace_sample': trace, 'trace_idx': trace_idx})
            # Select outputs for 2D/3D mode
            selected_outputs = select_outputs(is_2d_3d_mode=True)
            print("\nCalculating spectral descriptors...")

            display_keys_to_exclude = [
                'Frequency', 'Dominant Frequency', 'Time (Y-axis)',
                'hfc_p95', 'spec_decrease_p95', 'hfc_percentile', 'spec_decrease_percentile',  # Added percentile keys
                'Input Signal', 'Magnitude * Voice', 'Spectral Slope',
                'Spectral Bandwidth', 'Spectral Rolloff', 'Mag*Voice Slope',
                'WOSS', 'Spectral Decrease', 'HFC', 'epsilon', 'sample_percent',
                'max_traces', 'fdom_exponent', 'use_band_limited',
                'colormap', 'colormap_mag_voice', 'colormap_dom_freq',  # Add colormap settings
                'colormap_spec_decrease', 'colormap_hfc', 'colormap_bandwidth',
                'colormap_rolloff', 'colormap_woss', 'section_colormap',
                'colormap_mag_voice_slope'  # Added to exclude this key
            ]
            descriptor_settings = {k: v for k, v in plot_settings.items()
                                 if k not in display_keys_to_exclude}

            if dlogst_spec_descriptor_batch is not None:
                # Zero-pad traces to uniform length
                max_len = max(len(t['trace_sample']) for t in trace_data)
                for t in trace_data:
                    trace = t['trace_sample']
                    if len(trace) < max_len:
                        t['trace_sample'] = np.pad(trace, (0, max_len - len(trace)), mode='constant')

                arr = np.stack([t['trace_sample'] for t in trace_data])
                descriptors_all = dlogst_spec_descriptor_batch(
                    arr, dt, fmax=arr.shape[1] // 2,
                    use_band_limited=plot_settings['use_band_limited'],
                    **{k: v for k, v in descriptor_settings.items() if k not in ['use_band_limited', 'Magnitude Spectrogram']},
                    batch_size=batch_size
                )
                descriptors_list = []
                for i in tqdm(range(arr.shape[0]), desc="Calculating spectral descriptors"):  # progress bar here
                    di = {}
                    for key in descriptors_all:
                        val = descriptors_all[key]
                        if isinstance(val, np.ndarray) and val.shape[0] == arr.shape[0]:
                            di[key] = val[i]
                        else:
                            di[key] = val
                    descriptors_list.append(di)
            else:
                descriptors_list = [
                    dlogst_spec_descriptor(t['trace_sample'], dt, fmax=len(t['trace_sample'])//2, use_band_limited=plot_settings['use_band_limited'], **descriptor_settings)
                    for t in tqdm(trace_data, desc="Analyzing traces")
                ]

            # Plot the seismic section
            plot_seismic_section(trace_data, descriptors_list, dt, plot_settings, selected_outputs, header_loader, selection_mode)

        elif selection_mode == 4:  # Inline/crossline section (3D section)
            min_inline = np.min(header_loader.inlines)
            max_inline = np.max(header_loader.inlines)
            min_crossline = np.min(header_loader.crosslines)
            max_crossline = np.max(header_loader.crosslines)

            # Step 1: Define AOI - Get inline/crossline ranges
            ranges = get_range_input("Input Range", (min_inline, max_inline, min_crossline, max_crossline))
            if ranges is None:
                print("Range selection cancelled.")
                continue

            min_inline, max_inline, min_crossline, max_crossline = ranges
            print(f"\nProcessing Area of Interest (AOI): Inlines {min_inline}-{max_inline}, Crosslines {min_crossline}-{max_crossline}")

            # Step 2: Prompt user for sub-option selection
            sub_option = simpledialog.askinteger(
                "AOI Processing Option",
                "Choose an option for AOI processing:\n"
                "1. Analyze and plot AOI samples\n"
                "2. Export directly without plotting",
                minvalue=1,
                maxvalue=2
            )
            if sub_option is None:
                print("AOI processing cancelled.")
                continue

            try:
                # Step 3: Select AOI Traces - Identify all indices within these ranges
                aoi_mask = ((header_loader.inlines >= min_inline) &
                            (header_loader.inlines <= max_inline) &
                            (header_loader.crosslines >= min_crossline) &
                            (header_loader.crosslines <= max_crossline))
                aoi_indices = header_loader.unique_indices[aoi_mask]

                if len(aoi_indices) == 0:
                    print("No traces found in the specified area.")
                    continue

                print(f"Found {len(aoi_indices)} traces in the defined AOI.")

                # Define descriptor settings outside the sub-option blocks
                display_keys_to_exclude = [
                    'Frequency', 'Dominant Frequency', 'Time (Y-axis)',
                    'hfc_p95', 'spec_decrease_p95', 'hfc_percentile', 'spec_decrease_percentile',
                    'Input Signal', 'Magnitude * Voice', 'Spectral Slope',
                    'Spectral Bandwidth', 'Spectral Rolloff', 'Mag*Voice Slope',
                    'WOSS', 'Spectral Decrease', 'HFC', 'epsilon', 'sample_percent',
                    'max_traces', 'fdom_exponent', 'use_band_limited',
                    'colormap', 'colormap_mag_voice', 'colormap_dom_freq',
                    'colormap_spec_decrease', 'colormap_hfc', 'colormap_bandwidth',
                    'colormap_rolloff', 'colormap_woss', 'section_colormap',
                    'colormap_mag_voice_slope',
                    'Magnitude Spectrogram'  # <-- Add this line to exclude this key
                ]
                descriptor_settings = {k: v for k, v in plot_settings.items() if k not in display_keys_to_exclude}

                # Sub-option 1: Analyze and Plot AOI Samples
                if sub_option == 1:
                    # Warn if AOI is large
                    if len(aoi_indices) > 1000:
                        messagebox.showwarning(
                            "Memory Warning",
                            "The selected AOI is large and may consume significant memory for plotting. "
                            "Consider exporting directly or selecting a smaller AOI."
                        )

                    # Add prompt for processing direction
                    processing_choice = simpledialog.askinteger(
                        "Processing Direction",
                        "Choose processing direction:\n1. By Inline\n2. By Crossline",
                        minvalue=1,
                        maxvalue=2
                    )
                    if processing_choice is None:
                        print("Processing direction selection cancelled.")
                        continue
                    processing_type = 'inline' if processing_choice == 1 else 'crossline'

                    # Get unique groups based on processing direction
                    if processing_type == 'inline':
                        unique_groups = np.unique(header_loader.inlines[aoi_mask])
                    else:
                        unique_groups = np.unique(header_loader.crosslines[aoi_mask])

                    # Select outputs for plotting first - we'll need this for all groups
                    selected_outputs = select_outputs(is_2d_3d_mode=True)
                    if not selected_outputs:
                        print("No outputs selected. Skipping analysis.")
                        continue

                    # Get display preference (fixed inline or crossline)
                    section_info = get_3d_section_selection(min_inline, max_inline, min_crossline, max_crossline)
                    if section_info is None:
                        print("Section type selection cancelled.")
                        continue

                    section_type, fixed_value = section_info

                    # Create a mask for the cross-section to display
                    if section_type == 1:  # Fixed inline
                        display_mask = (header_loader.inlines == fixed_value) & \
                                       (header_loader.crosslines >= min_crossline) & \
                                       (header_loader.crosslines <= max_crossline)
                    else:  # Fixed crossline
                        display_mask = (header_loader.crosslines == fixed_value) & \
                                       (header_loader.inlines >= min_inline) & \
                                       (header_loader.inlines <= max_inline)

                    display_indices = header_loader.unique_indices[display_mask]

                    if len(display_indices) == 0:
                        print(f"No traces found on {'inline' if section_type == 1 else 'crossline'} {fixed_value} within the AOI.")
                        continue

                    print(f"Found {len(display_indices)} traces for display along {'inline' if section_type == 1 else 'crossline'} {fixed_value}.")

                    # Create dictionaries to store processed data directly indexed by trace_idx
                    display_trace_data = {}
                    display_descriptors = {}

                    # Process AOI traces only for the selected cross-section
                    print(f"\nProcessing traces for {'inline' if section_type == 1 else 'crossline'} {fixed_value}...")

                    # Process only the cross-section we need to display
                    cross_section_indices = display_indices  # These are the trace indices we need

                    # Load all traces for the cross-section at once
                    cross_section_data = []
                    for trace_idx in tqdm(cross_section_indices, desc=f"Fetching traces for {'inline' if section_type == 1 else 'crossline'} {fixed_value}"):
                        trace = load_trace_sample(segy_path, trace_idx)
                        cross_section_data.append({'trace_sample': trace, 'trace_idx': trace_idx})

                    # Compute spectral descriptors for all traces in the cross-section
                    if dlogst_spec_descriptor_batch is not None and cross_section_data:
                        try:
                            # Check for consistent trace lengths and apply zero padding if necessary
                            trace_lengths = [len(t['trace_sample']) for t in cross_section_data]
                            if min(trace_lengths) != max(trace_lengths):
                                print("Traces have different lengths. Applying zero padding for batch processing...")
                                max_len = max(trace_lengths)
                                for t in cross_section_data:
                                    if len(t['trace_sample']) < max_len:
                                        t['trace_sample'] = np.pad(
                                            t['trace_sample'],
                                            (0, max_len - len(t['trace_sample'])),
                                            mode='constant'
                                        )

                            # Stack traces into a batch
                            arr = np.stack([t['trace_sample'] for t in cross_section_data])

                            # Remove problematic parameters
                            filtered_descriptor_settings = {
                                k: v for k, v in descriptor_settings.items()
                                if k != 'use_band_limited' and k != 'hfc_p5'
                            }

                            # Process the batch
                            descriptors_all = dlogst_spec_descriptor_batch(
                                arr, dt, fmax=arr.shape[1] // 2,
                                use_band_limited=plot_settings['use_band_limited'],
                                **filtered_descriptor_settings,
                                batch_size=batch_size
                            )

                            # Extract individual descriptors
                            cross_section_descriptors = []
                            for i in range(arr.shape[0]):
                                di = {}
                                for key in descriptors_all:
                                    val = descriptors_all[key]
                                    if isinstance(val, np.ndarray) and val.shape[0] == arr.shape[0]:
                                        di[key] = val[i]
                                    else:
                                        di[key] = val
                                cross_section_descriptors.append(di)

                        except Exception as e:
                            print(f"Error in batch processing: {e}")
                            # Fall back to non-batch processing
                            cross_section_descriptors = [
                                dlogst_spec_descriptor(
                                    t['trace_sample'], dt, fmax=len(t['trace_sample'])//2,
                                    use_band_limited=plot_settings['use_band_limited'],
                                    **descriptor_settings
                                )
                                for t in tqdm(cross_section_data, desc="Computing descriptors (fallback method)")
                            ]
                    else:
                        # Use non-batch processing
                        cross_section_descriptors = [
                            dlogst_spec_descriptor(
                                t['trace_sample'], dt, fmax=len(t['trace_sample'])//2,
                                use_band_limited=plot_settings['use_band_limited'],
                                **descriptor_settings
                            )
                            for t in tqdm(cross_section_data, desc="Computing descriptors")
                        ]

                    # Now organize the traces and descriptors in the right order
                    ordered_trace_data = []
                    ordered_descriptors = []

                    # For crosslines, we typically want to sort by inline number
                    # For inlines, we typically want to sort by crossline number
                    if section_type == 1:  # Fixed inline - sort by crossline
                        indices_with_xl = []
                        for i, td in enumerate(cross_section_data):
                            idx = td['trace_idx']
                            pos = np.where(header_loader.unique_indices == idx)[0][0]
                            xl = header_loader.crosslines[pos]
                            indices_with_xl.append((i, xl))

                        # Sort by crossline
                        indices_with_xl.sort(key=lambda x: x[1])

                        # Add in sorted order
                        for i, _ in indices_with_xl:
                            ordered_trace_data.append(cross_section_data[i])
                            ordered_descriptors.append(cross_section_descriptors[i])

                    else:  # Fixed crossline - sort by inline
                        indices_with_il = []
                        for i, td in enumerate(cross_section_data):
                            idx = td['trace_idx']
                            pos = np.where(header_loader.unique_indices == idx)[0][0]
                            il = header_loader.inlines[pos]
                            indices_with_il.append((i, il))

                        # Sort by inline
                        indices_with_il.sort(key=lambda x: x[1])

                        # Add in sorted order
                        for i, _ in indices_with_il:
                            ordered_trace_data.append(cross_section_data[i])
                            ordered_descriptors.append(cross_section_descriptors[i])

                    print(f"Processing complete. Rendering visualization...")

                    # Plot the selected section
                    plot_seismic_section(
                        ordered_trace_data,
                        ordered_descriptors,
                        dt,
                        plot_settings,
                        selected_outputs,
                        header_loader,
                        selection_mode=4,
                        fixed_inline=fixed_value if section_type == 1 else None,
                        fixed_crossline=fixed_value if section_type == 2 else None
                    )

                # Sub-option 2: Export Directly Without Plotting
                elif sub_option == 2:
                    # Get export directory
                    export_dir = filedialog.askdirectory(title="Select directory to save exported SEG-Y files")
                    if not export_dir:
                        print("Export cancelled.")
                        continue

                    # Select attributes to export
                    first_trace = load_trace_sample(segy_path, aoi_indices[0])
                    sample_length = len(first_trace)
                    first_descriptor = dlogst_spec_descriptor(first_trace, dt, fmax=sample_length//2, use_band_limited=plot_settings['use_band_limited'], **descriptor_settings)

                    # Create a mapping between internal attribute names and user-friendly display names
                    attr_display_names = {
                        "data": "Original Seismic Amplitude",
                        "mag_voice_slope": "Magnitude*Voice Slope",
                        "spec_decrease": "Spectral Decrease",
                        "hfc": "High Frequency Content (HFC)",
                        "spec_bandwidth": "Spectral Bandwidth",
                        "spec_rolloff": "Spectral Rolloff",
                        "WOSS": "WOSS (Weighted-Optimum Spectral Shape)",
                        "norm_fdom": "Normalized Dominant Frequency"
                    }

                    # Check which attributes are available and valid for export
                    exportable_attrs = []
                    exportable_display_names = []

                    # Check basic attributes
                    for attr, display_name in attr_display_names.items():
                        if attr == "WOSS":
                            # WOSS is a special case - we'll add it later if prerequisites are available
                            continue

                        if (attr in first_descriptor and
                            isinstance(first_descriptor[attr], np.ndarray) and
                            len(first_descriptor[attr]) == sample_length):
                            exportable_attrs.append(attr)
                            exportable_display_names.append(display_name)

                    # Check if WOSS can be calculated
                    if ('hfc' in first_descriptor and
                        'norm_fdom' in first_descriptor and
                        'mag_voice_slope' in first_descriptor and
                        all(isinstance(first_descriptor[k], np.ndarray) and
                            len(first_descriptor[k]) == sample_length
                            for k in ['hfc', 'norm_fdom', 'mag_voice_slope'])):
                        exportable_attrs.append('WOSS')
                        exportable_display_names.append(attr_display_names['WOSS'])

                    # If no exportable attributes found
                    if not exportable_attrs:
                        messagebox.showinfo("Export Info", "No valid attributes available for export.")
                        continue

                    # Create an AttributeSelector with display names but return actual attribute keys
                    selector = AttributeSelector(root, exportable_display_names)
                    selected_display_attrs = selector.show_dialog()

                    # Map selected display names back to attribute keys
                    selected_attrs = []
                    for display_name in selected_display_attrs:
                        idx = exportable_display_names.index(display_name)
                        selected_attrs.append(exportable_attrs[idx])

                    if not selected_attrs:
                        print("No attributes selected for export.")
                        continue

                    # Prompt user to choose grouping: by inline or by crossline
                    grouping_choice = simpledialog.askinteger(
                        "Export Grouping",
                        "Choose how to group the export:\n"
                        "1. By Inline\n"
                        "2. By Crossline",
                        minvalue=1,
                        maxvalue=2
                    )
                    if grouping_choice is None:
                        print("Grouping selection cancelled.")
                        continue

                    grouping_type = 'inline' if grouping_choice == 1 else 'crossline'
                    print(f"\nExporting by {grouping_type}...")

                    # Prompt for batch step size
                    batch_step = simpledialog.askinteger(
                        f"Batch Size ({grouping_type.capitalize()})",
                        f"Enter the number of {grouping_type}s to process per batch:",
                        initialvalue=10,
                        minvalue=1
                    )
                    if batch_step is None:
                        print("Batch size selection cancelled.")
                        continue

                    # Get unique inlines or crosslines in the AOI
                    if grouping_type == 'inline':
                        unique_groups = np.unique(header_loader.inlines[aoi_mask])
                    else:
                        unique_groups = np.unique(header_loader.crosslines[aoi_mask])

                    print(f"Found {len(unique_groups)} unique {grouping_type}s in the AOI. Processing in batches of {batch_step}.")

                    all_batch_files = {attr: [] for attr in selected_attrs} # Store batch filenames per attribute

                    # Process groups in batches
                    for i in range(0, len(unique_groups), batch_step):
                        current_batch_groups = unique_groups[i:i+batch_step]
                        batch_start = current_batch_groups[0]
                        batch_end = current_batch_groups[-1]
                        print(f"\nProcessing batch: {grouping_type.capitalize()}s {batch_start} to {batch_end}")

                        batch_indices = []
                        batch_trace_data = []

                        # Collect indices and load traces for the current batch
                        for group_value in current_batch_groups:
                            if grouping_type == 'inline':
                                group_mask = (header_loader.inlines == group_value) & aoi_mask
                            else:
                                group_mask = (header_loader.crosslines == group_value) & aoi_mask
                            group_indices = header_loader.unique_indices[group_mask]
                            batch_indices.extend(group_indices)

                        if not batch_indices:
                            print(f"No traces found for batch {batch_start}-{batch_end}.")
                            continue

                        # Load traces for the entire batch
                        for trace_idx in tqdm(batch_indices, desc=f"Fetching traces for batch {batch_start}-{batch_end}"):
                            trace = load_trace_sample(segy_path, trace_idx)
                            batch_trace_data.append({'trace_sample': trace, 'trace_idx': trace_idx})

                        if not batch_trace_data:
                            print(f"No trace data loaded for batch {batch_start}-{batch_end}.")
                            continue

                        # Compute descriptors for the batch
                        batch_descriptors = []
                        if dlogst_spec_descriptor_batch is not None:
                            # Zero-pad traces to uniform length
                            trace_lengths = [len(t['trace_sample']) for t in batch_trace_data]
                            if not trace_lengths: continue # Skip if empty
                            max_len = max(trace_lengths) if trace_lengths else 0
                            if min(trace_lengths) != max_len:
                                print("Traces have different lengths. Applying zero padding for batch processing...")
                                for t in batch_trace_data:
                                    if len(t['trace_sample']) < max_len:
                                        t['trace_sample'] = np.pad(t['trace_sample'], (0, max_len - len(t['trace_sample'])), mode='constant')

                            if batch_trace_data: # Ensure there's data to stack
                                arr = np.stack([t['trace_sample'] for t in batch_trace_data])
                                descriptors_all = dlogst_spec_descriptor_batch(
                                    arr, dt, fmax=arr.shape[1] // 2,
                                    use_band_limited=plot_settings['use_band_limited'],
                                    **{k: v for k, v in descriptor_settings.items() if k != 'use_band_limited'},
                                    batch_size=batch_size
                                )
                                for j in range(arr.shape[0]):
                                    di = {}
                                    for key in descriptors_all:
                                        val = descriptors_all[key]
                                        if isinstance(val, np.ndarray) and val.shape[0] == arr.shape[0]:
                                            di[key] = val[j]
                                        else:
                                            di[key] = val
                                    batch_descriptors.append(di)
                        else: # Fallback to single trace processing if batch function not available
                             batch_descriptors = [
                                dlogst_spec_descriptor(t['trace_sample'], dt, fmax=len(t['trace_sample'])//2, use_band_limited=plot_settings['use_band_limited'], **descriptor_settings)
                                for t in tqdm(batch_trace_data, desc=f"Analyzing traces for batch {batch_start}-{batch_end}")
                            ]

                        if len(batch_descriptors) != len(batch_trace_data):
                             print(f"Warning: Mismatch between trace data ({len(batch_trace_data)}) and descriptors ({len(batch_descriptors)}) for batch {batch_start}-{batch_end}. Skipping batch.")
                             continue

                        # Export each selected attribute for this batch into a single batch file per attribute
                        for attr in selected_attrs:
                            batch_output_file = os.path.join(export_dir, f"{attr}_{grouping_type}_{batch_start}-{batch_end}_batch.segy")
                            all_batch_files[attr].append(batch_output_file) # Store filename for merging

                            with segyio.open(segy_path, 'r', ignore_geometry=True) as src:
                                spec = segyio.tools.metadata(src)
                                # Ensure sample length matches the (potentially padded) trace data
                                spec.samples = np.arange(len(batch_trace_data[0]['trace_sample']))
                                spec.tracecount = len(batch_trace_data)
                                with segyio.create(batch_output_file, spec) as dst:
                                    dst.bin = src.bin
                                    try: # Copy text header if available
                                        dst.text[0] = src.text[0]
                                    except:
                                        pass
                                    for k, (trace_item, descriptor) in enumerate(zip(batch_trace_data, batch_descriptors)):
                                        dst.header[k] = src.header[trace_item['trace_idx']]
                                        if attr == 'WOSS':
                                            epsilon = plot_settings.get('epsilon', 1e-10)
                                            fdom_exponent = plot_settings.get('fdom_exponent', 2.0)
                                            # Ensure required keys exist before calculation
                                            if all(key in descriptor for key in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                                                hfc_norm = descriptor['hfc'] / plot_settings['hfc_p95'] if plot_settings['hfc_p95'] != 0 else descriptor['hfc']
                                                norm_fdom = descriptor['norm_fdom']
                                                mag_voice_slope = descriptor['mag_voice_slope']
                                                denominator = hfc_norm * (norm_fdom**fdom_exponent + epsilon)
                                                with np.errstate(divide='ignore', invalid='ignore'):
                                                    woss = np.where(denominator > epsilon, mag_voice_slope / denominator, 0.0)
                                                    woss[~np.isfinite(woss)] = 0.0
                                                    woss = np.clip(woss, -1e6, 1e6)
                                                dst.trace[k] = woss
                                            else:
                                                print(f"Warning: Missing keys for WOSS calculation in trace {trace_item['trace_idx']}. Setting WOSS to 0.")
                                                dst.trace[k] = np.zeros_like(trace_item['trace_sample'], dtype=np.float32)
                                        elif attr in descriptor:
                                            # Ensure the attribute data has the correct length
                                            attr_data = descriptor[attr]
                                            if len(attr_data) == len(dst.samples):
                                                 dst.trace[k] = attr_data
                                            else:
                                                 print(f"Warning: Length mismatch for attribute '{attr}' in trace {trace_item['trace_idx']}. Expected {len(dst.samples)}, got {len(attr_data)}. Setting trace to zeros.")
                                                 dst.trace[k] = np.zeros(len(dst.samples), dtype=np.float32)
                                        else:
                                            print(f"Warning: Attribute '{attr}' not found in descriptor for trace {trace_item['trace_idx']}. Setting trace to zeros.")
                                            dst.trace[k] = np.zeros(len(dst.samples), dtype=np.float32)


                        # Clear memory for the batch
                        batch_trace_data.clear()
                        batch_descriptors.clear()

                    # Merge batch files for each attribute after all batches are processed
                    print("\nMerging batch files for each attribute...")
                    for attr in selected_attrs:
                        batch_files_for_attr = all_batch_files[attr]

                        # Check if files exist
                        batch_files_for_attr = [f for f in batch_files_for_attr if os.path.exists(f)]

                        if batch_files_for_attr:
                            # Create merged output file name
                            output_file = os.path.join(export_dir, f"{attr}_AOI_{min_inline}-{max_inline}_{min_crossline}-{max_crossline}_merged.segy")

                            # Merge files
                            if merge_segy_batch_files(batch_files_for_attr, output_file):
                                print(f"Successfully merged {len(batch_files_for_attr)} batch files for {attr} into {output_file}")

                                # Automatically delete individual batch files
                                for batch_file in batch_files_for_attr:
                                    try:
                                        os.remove(batch_file)
                                        print(f"Deleted {batch_file}")
                                    except Exception as e:
                                        print(f"Error deleting {batch_file}: {e}")
                            else:
                                print(f"Error merging batch files for attribute {attr}")
                        else:
                            print(f"No batch files found to merge for attribute {attr}")

                    print(f"Export and merge completed in {export_dir}")

            except Exception as e:
                # Use custom exception hook format
                exc_type, exc_value, exc_traceback = sys.exc_info()
                custom_excepthook(exc_type, exc_value, exc_traceback)
                messagebox.showerror("Error", f"An error occurred during AOI export: {str(e)}")
                print(f"An error occurred: {str(e)}")
                continue

