# WOSS Seismic Analysis Tool: Options 2 & 3 Workflow (Single Inline/Crossline)

This document details the Python scripts and code flow for the WOSS Seismic Analysis Tool when using Option 2 ("Single inline (all crosslines)") or Option 3 ("Single crossline (all inlines)") for selecting data. It focuses on the transition from Step 3 (Select Mode/Traces) to Step 4 (View Results) and the spectral descriptor calculation process.

## Key Python Files and Their Roles

The primary Python files involved in this workflow are largely the same as for Option 1, with some differences in how functions are utilized:

1.  **`app_ref.py`**:
    *   **Role**: The main Streamlit application file. It defines the UI, manages `st.session_state`, controls the step-by-step flow, and calls other modules.
    *   **Key Functions**: Handles UI for data loading, mode selection (including inline/crossline number input), parameter configuration, initiating calculations for sections, and displaying results (typically as 2D sections).

2.  **`data_utils.py`**:
    *   **Role**: Contains utility functions for data loading and preparation.
    *   **Key Functions**:
        *   `SegyHeaderLoader`: Crucial for accessing inline/crossline numbers and trace indices.
        *   `load_trace_sample`: Loads seismic data for individual trace indices.
        *   `get_sampling_interval`, `get_trace_count`: Provide basic SEGY metadata.

3.  **`dlogst_spec_descriptor_gpu.py`**:
    *   **Role**: Contains core functions for calculating spectral descriptors using GPU.
    *   **Key Functions**:
        *   `dlogst_spec_descriptor_gpu`: Can be used for single trace processing if chunking is not applied.
        *   `dlogst_spec_descriptor_gpu_2d_chunked`: Processes a 2D array of traces (a section) in chunks on the GPU. This is key for efficient processing of entire inlines or crosslines.
        *   `dlogst_spec_descriptor_gpu_2d_chunked_mag`: Similar to above but might focus on magnitude-related outputs.

4.  **`processing.py`**:
    *   **Role**: Contains functions for data processing tasks.
    *   **Key Functions**:
        *   `calculate_stats_and_defaults`: Used in Step 2 for parameter suggestions.
        *   `calculate_woss`: Calculates the WOSS attribute.

5.  **`visualization.py`**:
    *   **Role**: Handles the creation of plots.
    *   **Key Functions**:
        *   `plot_multi_trace_section`: Generates 2D plots for seismic sections (inlines/crosslines) displaying selected spectral attributes.
        *   `plot_spectral_descriptors`: While primarily for Option 1, components might be reused or adapted if detailed single-trace views within a section were desired.

## Workflow: Step 3 (Select Mode) to Step 4 (View Results) - Options 2 & 3

The workflow for analyzing a single inline or crossline is managed within `app_ref.py`.

### 1. Step 3: Select Analysis Mode (`app_ref.py`)

*   The application is at `st.session_state.current_step == "select_mode"`.
*   The user selects either `"Single inline (all crosslines)"` or `"Single crossline (all inlines)"` from the "Select Mode" dropdown.
    ```python
    # In app_ref.py, within the "select_mode" step:
    st.session_state.selection_mode = st.sidebar.selectbox(
        "Select Mode",
        options=[
            # ... other options ...
            "Single inline (all crosslines)",
            "Single crossline (all inlines)",
            # ... other options ...
        ],
        # ...
    )
    ```
*   **Input for Inline/Crossline Number**:
    *   If "Single inline" is chosen, a number input appears for `st.session_state.selected_inline`.
    *   If "Single crossline" is chosen, a number input appears for `st.session_state.selected_crossline`.
    *   Min/max values for these inputs are derived from `st.session_state.header_loader.inlines` or `st.session_state.header_loader.crosslines`.
*   **Batch Size (GPU)**: If `GPU_AVAILABLE` is true, a number input for `st.session_state.batch_size` (for GPU processing chunks) is shown.
*   The user clicks the "Proceed" button:
    ```python
    # In app_ref.py, at the end of the "select_mode" step:
    if st.button("Proceed", key="proceed_button"):
        st.session_state.current_step = "select_traces"
        st.rerun()
    ```

### 2. Step 3.5: Select Traces (and Prepare for Section Analysis) (`app_ref.py`)

The application is at `st.session_state.current_step == "select_traces"`.

*   **Identifying Traces for the Section**:
    *   **Single Inline Mode**:
        ```python
        # In app_ref.py, within "select_traces" for "Single inline":
        inline_choice = st.session_state.selected_inline
        inline_mask = st.session_state.header_loader.inlines == inline_choice
        chosen_indices = st.session_state.header_loader.unique_indices[inline_mask]
        st.session_state.selected_indices = chosen_indices.tolist()
        ```
    *   **Single Crossline Mode**:
        ```python
        # In app_ref.py, within "select_traces" for "Single crossline":
        crossline_choice = st.session_state.selected_crossline
        crossline_mask = st.session_state.header_loader.crosslines == crossline_choice
        chosen_indices = st.session_state.header_loader.unique_indices[crossline_mask]
        st.session_state.selected_indices = chosen_indices.tolist()
        ```
    *   `st.session_state.selected_indices` now holds the trace indices forming the chosen inline or crossline.

*   **Loading Trace Data for the Section**:
    *   A button "Load Traces for Processing" appears.
    *   When clicked, the application iterates through `st.session_state.selected_indices`.
    *   For each `trace_idx` in the section:
        1.  `load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)` (from `data_utils.py`) loads the seismic amplitude data.
        2.  This data is collected into `st.session_state.loaded_trace_data`. This list will contain multiple 1D trace arrays.
    *   A flag like `st.session_state.traces_loaded_inline` or `st.session_state.traces_loaded_crossline` is set to `True`.

    ```python
    # In app_ref.py, within "select_traces" for "Single inline" (similar for crossline):
    if not st.session_state.traces_loaded_inline:
        if st.button("Load Traces for Processing", key="load_traces_inline"):
            with st.spinner(f"Loading {len(chosen_indices)} traces for inline {inline_choice}..."):
                loaded_data_temp = []
                try:
                    for idx in st.session_state.selected_indices:
                        trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, idx)
                        # Store trace_sample, idx, and potentially header info if needed later
                        loaded_data_temp.append({'trace_sample': trace_sample, 'trace_idx': idx})
                    st.session_state.loaded_trace_data = loaded_data_temp
                    st.session_state.traces_loaded_inline = True
                    logging.info(f"Successfully loaded {len(loaded_data_temp)} traces for inline.")
                    st.rerun()
                # ... error handling ...
    ```

*   **Select Outputs for Display**:
    *   A multiselect widget allows the user to choose which spectral descriptors to calculate and display for the section (e.g., "WOSS", "Spectral Slope"). The options are from `AVAILABLE_OUTPUTS_SECTION`.
    *   The choices are stored in `st.session_state.selected_outputs`.

### 3. Spectral Descriptor Calculation for the Section (`app_ref.py` within "select_traces")

*   The "Calculate Descriptors" button becomes active once traces are loaded and outputs are selected.
*   When clicked:
    1.  Spectral parameters are retrieved from `st.session_state.plot_settings`.
    2.  The `st.session_state.loaded_trace_data` (a list of 1D trace arrays) is typically stacked into a 2D numpy array representing the entire seismic section.
        ```python
        # Conceptual stacking (actual implementation might vary slightly)
        # Ensure all traces have the same length before stacking
        # trace_samples_list = [item['trace_sample'] for item in st.session_state.loaded_trace_data]
        # section_data_2d = np.stack(trace_samples_list, axis=-1) # Shape: (num_samples, num_traces)
        ```
    3.  If `GPU_AVAILABLE` and `st.session_state.batch_size` is set, the `dlogst_spec_descriptor_gpu_2d_chunked` function (from `dlogst_spec_descriptor_gpu.py`) is called.
        *   **Inputs**: The 2D `section_data_2d`, `st.session_state.dt`, `st.session_state.batch_size`, and spectral parameters.
        *   **Process**: This function processes the 2D section in batches (chunks of traces) on the GPU. For each selected output descriptor, it computes the attribute across all traces in the section.
        *   **Output**: A dictionary where keys are the *internal names* of the selected output descriptors (e.g., `'WOSS'`, `'spec_slope'`) and values are 2D numpy arrays (shape: `num_samples` x `num_traces_in_section`) representing the calculated attribute section.
    4.  If GPU/batch processing is not used, it might fall back to iterating through traces and calling `dlogst_spec_descriptor_gpu` for each, then assembling the 2D attribute sections.
    5.  The resulting dictionary of 2D attribute sections is stored in `st.session_state.calculated_descriptors`.
    6.  `st.session_state.analysis_complete` is set to `True`.

    ```python
    # In app_ref.py, within "select_traces" for "Single inline" (similar for crossline):
    if st.button("Calculate Descriptors", key="calculate_descriptors_inline", ...):
        with st.spinner("Calculating spectral descriptors for the section..."):
            try:
                if not st.session_state.loaded_trace_data:
                    st.error("No trace data loaded.")
                    # ... return or handle ...

                # Prepare 2D array from loaded_trace_data
                # Assuming all traces are of the same length
                trace_samples_list = [item['trace_sample'] for item in st.session_state.loaded_trace_data]
                # Pad traces if they have different lengths (important for robust stacking)
                max_len = max(len(t) for t in trace_samples_list)
                padded_traces = [np.pad(t, (0, max_len - len(t)), 'constant') for t in trace_samples_list]
                section_data_2d = np.stack(padded_traces, axis=-1) # Shape: (max_len, num_traces)

                # Get spectral parameters
                spectral_params = { ... } # Assembled from st.session_state.plot_settings

                # Get internal names for selected outputs
                # ATTR_NAME_MAP maps display names to internal names
                internal_outputs_to_calculate = [ATTR_NAME_MAP.get(out, out.lower().replace(" ", "_")) for out in st.session_state.selected_outputs]
                
                # WOSS calculation requires specific components.
                # If WOSS is selected, ensure its components are also calculated if not already selected.
                required_for_woss = {"hfc", "norm_fdom", "mag_voice_slope"}
                if "WOSS" in internal_outputs_to_calculate:
                    for comp in required_for_woss:
                        if comp not in internal_outputs_to_calculate:
                            internal_outputs_to_calculate.append(comp)
                
                # Filter out "Input Signal" as it's already section_data_2d
                outputs_for_gpu_calc = [out for out in internal_outputs_to_calculate if out != "data" and out != "Input Signal"]


                if GPU_AVAILABLE and st.session_state.batch_size:
                    # dlogst_spec_descriptor_gpu_2d_chunked expects a list of attributes to compute
                    # and returns a dictionary of 2D arrays.
                    calculated_attribute_sections = dlogst_spec_descriptor_gpu_2d_chunked(
                        section_data_2d,
                        st.session_state.dt,
                        st.session_state.batch_size,
                        attributes_to_calculate=outputs_for_gpu_calc, # Pass internal names
                        **spectral_params
                    )
                else:
                    # Fallback: Process trace by trace (slower)
                    st.warning("GPU/batch processing not enabled. Processing trace by trace.")
                    calculated_attribute_sections = {}
                    for attr_name in outputs_for_gpu_calc:
                        attr_section_list = []
                        for i in range(section_data_2d.shape[1]): # Iterate through traces
                            single_trace_data = section_data_2d[:, i]
                            # Call single trace GPU version or CPU version
                            # This part needs a CPU equivalent or single trace GPU call
                            # For simplicity, assuming dlogst_spec_descriptor_gpu can return specific attributes
                            desc_dict_single_trace = dlogst_spec_descriptor_gpu(single_trace_data, st.session_state.dt, **spectral_params)
                            if attr_name in desc_dict_single_trace:
                                attr_section_list.append(desc_dict_single_trace[attr_name])
                            else:
                                attr_section_list.append(np.full_like(single_trace_data, np.nan)) # Placeholder
                        calculated_attribute_sections[attr_name] = np.stack(attr_section_list, axis=-1)
                
                # Add original data if selected
                if "Input Signal" in st.session_state.selected_outputs or "data" in internal_outputs_to_calculate:
                     calculated_attribute_sections["data"] = section_data_2d


                # Explicitly calculate WOSS if selected, using components from calculated_attribute_sections
                if "WOSS" in internal_outputs_to_calculate and all(comp in calculated_attribute_sections for comp in required_for_woss):
                    # calculate_woss needs a dictionary of components for each trace.
                    # Here, we have 2D sections. We need to adapt or apply calculate_woss per trace.
                    # For simplicity, let's assume calculate_woss can be vectorized or applied trace-wise.
                    # This part might need careful implementation based on calculate_woss's exact signature.
                    # A placeholder for WOSS calculation on sections:
                    num_samples, num_traces = section_data_2d.shape
                    woss_section = np.zeros((num_samples, num_traces))
                    for i in range(num_traces):
                        trace_components = {
                            'hfc': calculated_attribute_sections['hfc'][:, i],
                            'norm_fdom': calculated_attribute_sections['norm_fdom'][:, i],
                            'mag_voice_slope': calculated_attribute_sections['mag_voice_slope'][:, i],
                            # Add other necessary components if any
                        }
                        woss_section[:, i] = calculate_woss(trace_components, st.session_state.plot_settings)
                    calculated_attribute_sections["WOSS"] = woss_section


                st.session_state.calculated_descriptors = calculated_attribute_sections # Store the dict of 2D arrays
                st.session_state.analysis_complete = True
                st.success("Section descriptor calculation complete.")
                st.rerun() # To update UI and show View Results button
            # ... error handling ...
    ```

### 4. Transition to Step 4: View Results (`app_ref.py`)

*   Once `st.session_state.analysis_complete` is `True`, the "View Results" button (or similar navigation) leads to the results display.
*   The application state changes: `st.session_state.current_step = "view_results"`.
*   **Displaying Section Plots**:
    *   The user can select which calculated attribute sections (from `st.session_state.selected_outputs`) to display.
    *   The `plot_multi_trace_section` function (from `visualization.py`) is called for each selected attribute.
    *   **Inputs**:
        *   The 2D attribute array (e.g., `st.session_state.calculated_descriptors['WOSS']`).
        *   `st.session_state.dt` (sampling interval).
        *   Plot settings from `st.session_state.plot_settings` (e.g., colormaps, amplitude limits for the attribute, time limits).
        *   Trace header information (e.g., crossline numbers for an inline plot, or inline numbers for a crossline plot) to label the X-axis of the section. This info can be retrieved from `st.session_state.header_loader` based on `st.session_state.selected_indices`.
    *   **Output**: A Plotly figure object showing the 2D attribute section, rendered using `st.plotly_chart()`.

## Data Flow Summary (Options 2 & 3)

1.  **Initial Data (Step 1)**: Same as Option 1.
    *   SEG-Y -> `st.session_state.header_loader`, `st.session_state.dt`, `st.session_state.segy_temp_file_path`.

2.  **Parameter Configuration (Step 2)**: Same as Option 1.
    *   User inputs -> `st.session_state.plot_settings`.
    *   Statistics -> `st.session_state.stats_defaults`.

3.  **Mode and Trace Selection (Step 3 & 3.5)**:
    *   User selects "Single inline" or "Single crossline" and specifies number -> `st.session_state.selection_mode`, `st.session_state.selected_inline`/`selected_crossline`.
    *   `header_loader` + selected inline/crossline -> `st.session_state.selected_indices` (trace indices for the section).
    *   `selected_indices` + `segy_temp_file_path` -> `load_trace_sample` (in a loop) -> `st.session_state.loaded_trace_data` (list of 1D trace arrays).
    *   User selects outputs -> `st.session_state.selected_outputs`.

4.  **Descriptor Calculation for Section (Step 3.5)**:
    *   `st.session_state.loaded_trace_data` (stacked into 2D array) + `st.session_state.dt` + `st.session_state.plot_settings` (spectral params) + `st.session_state.batch_size` + `st.session_state.selected_outputs` (internal names) -> `dlogst_spec_descriptor_gpu_2d_chunked` (or equivalent).
    *   Result -> `st.session_state.calculated_descriptors` (dictionary of 2D attribute arrays, e.g., `{'WOSS': woss_section_2d, 'spec_slope': slope_section_2d}`).

5.  **Viewing Results (Step 4)**:
    *   `st.session_state.calculated_descriptors` (specific 2D attribute array) + `st.session_state.selected_outputs` + `st.session_state.plot_settings` (display limits, colormaps) + `st.session_state.dt` + relevant header info -> `plot_multi_trace_section` -> Plotly Figure (2D section plot) displayed in UI.

This workflow allows for efficient processing and visualization of entire seismic lines or crosslines by leveraging chunked GPU calculations and specialized 2D plotting functions.
