# -*- coding: utf-8 -*-
"""
GPU utility functions for the WOSS Seismic Analysis Tool.

This module provides utility functions for checking GPU availability
and handling GPU-related operations.
"""

import logging

def check_gpu_availability():
    """
    Check if GPU is available and working.
    
    This function attempts to import cupy and perform a simple operation
    to verify that the GPU is available and working correctly.
    
    Returns:
        bool: True if GPU is available and working, False otherwise
    """
    try:
        import cupy as cp
        # Try to perform a simple operation to verify GPU is working
        a = cp.array([1, 2, 3])
        b = cp.sum(a)
        logging.info("GPU is available and working correctly.")
        return True
    except ImportError as e:
        logging.warning(f"GPU not available - CuPy import failed: {e}")
        return False
    except Exception as e:
        logging.warning(f"GPU available but not working correctly: {e}")
        return False

def get_gpu_info():
    """
    Get information about the available GPU(s).
    
    Returns:
        dict: Dictionary containing GPU information, or None if GPU is not available
    """
    try:
        import cupy as cp
        
        # Check if CUDA is available
        if not cp.cuda.is_available():
            logging.warning("CUDA is not available.")
            return None
        
        # Get device count
        device_count = cp.cuda.runtime.getDeviceCount()
        
        # Get information for each device
        devices = []
        for i in range(device_count):
            cp.cuda.runtime.setDevice(i)
            props = cp.cuda.runtime.getDeviceProperties(i)
            
            # Get memory info
            free, total = cp.cuda.runtime.memGetInfo()
            free_mb = free / (1024 ** 2)
            total_mb = total / (1024 ** 2)
            
            devices.append({
                'device_id': i,
                'name': props['name'].decode('utf-8'),
                'compute_capability': f"{props['major']}.{props['minor']}",
                'total_memory_mb': total_mb,
                'free_memory_mb': free_mb
            })
        
        return {
            'device_count': device_count,
            'devices': devices
        }
    except Exception as e:
        logging.warning(f"Failed to get GPU information: {e}")
        return None

def log_gpu_info():
    """
    Log information about the available GPU(s).
    """
    gpu_info = get_gpu_info()
    if gpu_info:
        logging.info(f"Found {gpu_info['device_count']} GPU device(s):")
        for i, device in enumerate(gpu_info['devices']):
            logging.info(f"  Device {i}: {device['name']} (Compute {device['compute_capability']})")
            logging.info(f"    Memory: {device['free_memory_mb']:.2f}MB free / {device['total_memory_mb']:.2f}MB total")
    else:
        logging.warning("No GPU information available.")
