"""
Pre-computation utilities for the WOSS Seismic Analysis Tool.

This module contains functions for pre-processing seismic data before analysis.
"""

import numpy as np
from scipy import signal
import logging

def apply_smoothing(data, window_size=5):
    """
    Apply moving average smoothing to a signal.

    Args:
        data (numpy.ndarray): Input signal
        window_size (int): Size of the smoothing window (must be odd)

    Returns:
        numpy.ndarray: Smoothed signal
    """
    if data is None:
        return None

    # Ensure window size is odd
    if window_size % 2 == 0:
        window_size += 1

    # Apply moving average
    return np.convolve(data, np.ones(window_size)/window_size, mode='same')

def apply_normalization(data, method="Max Amplitude"):
    """
    Normalize a signal using the specified method.

    Args:
        data (numpy.ndarray): Input signal
        method (str): Normalization method ("Max Amplitude", "RMS", or "L2 Norm")

    Returns:
        numpy.ndarray: Normalized signal
    """
    if data is None:
        return None

    # Handle zero or constant signals
    if np.all(data == 0) or np.max(data) - np.min(data) < 1e-10:
        return np.zeros_like(data)

    if method == "Max Amplitude":
        # Normalize by maximum absolute amplitude
        return data / np.max(np.abs(data))
    elif method == "RMS":
        # Normalize by root mean square
        rms = np.sqrt(np.mean(np.square(data)))
        if rms > 0:
            return data / rms
        return data
    elif method == "L2 Norm":
        # Normalize by L2 norm (Euclidean norm)
        l2_norm = np.linalg.norm(data)
        if l2_norm > 0:
            return data / l2_norm
        return data
    else:
        logging.warning(f"Unknown normalization method: {method}. Using Max Amplitude.")
        return data / np.max(np.abs(data))

def apply_frequency_filter(data, dt, filter_type="Bandpass", filter_params=None):
    """
    Apply frequency domain filtering to a signal.

    Args:
        data (numpy.ndarray): Input signal
        dt (float): Sampling interval in seconds
        filter_type (str): Type of filter ("Bandpass", "Lowpass", or "Highpass")
        filter_params (dict): Filter parameters (low_cut and/or high_cut in Hz)

    Returns:
        numpy.ndarray: Filtered signal
    """
    if data is None or dt is None or dt <= 0:
        return data

    if filter_params is None:
        filter_params = {}

    # Calculate Nyquist frequency
    fs = 1.0 / dt  # Sampling frequency
    nyquist = fs / 2.0

    # Design filter based on type
    if filter_type == "Bandpass":
        low_cut = filter_params.get("low_cut", 5.0)
        high_cut = filter_params.get("high_cut", 80.0)

        # Ensure frequencies are within valid range
        low_cut = max(0.1, min(low_cut, nyquist - 1))
        high_cut = max(low_cut + 1, min(high_cut, nyquist - 0.1))

        # Normalize frequencies to Nyquist
        low = low_cut / nyquist
        high = high_cut / nyquist

        # Create bandpass filter
        b, a = signal.butter(4, [low, high], btype='band')

    elif filter_type == "Lowpass":
        high_cut = filter_params.get("high_cut", 60.0)

        # Ensure frequency is within valid range
        high_cut = max(0.1, min(high_cut, nyquist - 0.1))

        # Normalize frequency to Nyquist
        high = high_cut / nyquist

        # Create lowpass filter
        b, a = signal.butter(4, high, btype='low')

    elif filter_type == "Highpass":
        low_cut = filter_params.get("low_cut", 5.0)

        # Ensure frequency is within valid range
        low_cut = max(0.1, min(low_cut, nyquist - 0.1))

        # Normalize frequency to Nyquist
        low = low_cut / nyquist

        # Create highpass filter
        b, a = signal.butter(4, low, btype='high')

    else:
        logging.warning(f"Unknown filter type: {filter_type}. No filtering applied.")
        return data

    # Apply filter
    try:
        filtered_data = signal.filtfilt(b, a, data)
        return filtered_data
    except Exception as e:
        logging.error(f"Error applying filter: {e}")
        return data

def run_precomputation(seismic_data_list, params):
    """
    Run pre-computation on a list of seismic traces.

    Args:
        seismic_data_list (list): List of seismic traces (numpy arrays)
        params (dict): Pre-computation parameters

    Returns:
        list: List of processed seismic traces or dictionaries with processed traces and descriptors
    """
    if not seismic_data_list:
        return []

    # Get parameters
    apply_smoothing_flag = params.get("apply_smoothing", False)
    smoothing_window = params.get("smoothing_window", 5)
    apply_normalization_flag = params.get("apply_normalization", False)
    normalization_method = params.get("normalization_method", "Max Amplitude")
    apply_filter_flag = params.get("apply_filter", False)
    filter_type = params.get("filter_type", "Bandpass")
    filter_params = params.get("filter_params", {})

    # Get sampling interval from session state (if available)
    import streamlit as st
    import logging
    dt = st.session_state.get("dt", 0.004)  # Default to 4ms if not available

    # Log the parameters being used
    logging.info(f"Running precomputation with parameters: {params}")

    # Process each trace
    processed_traces = []
    for trace in seismic_data_list:
        # Make a copy of the trace
        processed_trace = trace.copy()

        # Apply smoothing if enabled
        if apply_smoothing_flag:
            processed_trace = apply_smoothing(processed_trace, smoothing_window)

        # Apply frequency filtering if enabled
        if apply_filter_flag:
            processed_trace = apply_frequency_filter(processed_trace, dt, filter_type, filter_params)

        # Apply normalization if enabled
        if apply_normalization_flag:
            processed_trace = apply_normalization(processed_trace, normalization_method)

        processed_traces.append(processed_trace)

    # Log the number of traces processed
    logging.info(f"Processed {len(processed_traces)} traces")

    # Try to calculate spectral descriptors if GPU is available
    try:
        # Import the GPU function if available
        from utils.dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu

        # Check if GPU is available in session state
        gpu_available = st.session_state.get('GPU_AVAILABLE', False)

        if gpu_available:
            logging.info("GPU available, calculating spectral descriptors")

            # Get spectral parameters from plot_settings
            plot_settings = st.session_state.get('plot_settings', {})

            # Extract parameters for descriptor calculation
            spectral_params = {
                'use_band_limited': plot_settings.get('use_band_limited', False),
                'shape': plot_settings.get('shape', 0.35),
                'kmax': plot_settings.get('kmax', 120.0),
                'int_val': plot_settings.get('int_val', 35.0),
                'b1': plot_settings.get('b1', 5.0),
                'b2': plot_settings.get('b2', 40.0),
                'p_bandwidth': plot_settings.get('p_bandwidth', 2.0),
                'roll_percent': plot_settings.get('roll_percent', 0.80),
                'epsilon': plot_settings.get('epsilon', 1e-4),
                'hfc_percentile': plot_settings.get('hfc_percentile', 95.0)
            }

            # Calculate descriptors for each trace
            descriptors = []
            for trace in processed_traces:
                try:
                    descriptor = dlogst_spec_descriptor_gpu(trace, dt, **spectral_params)
                    descriptors.append(descriptor)
                except Exception as e:
                    logging.error(f"Error calculating descriptor: {e}")
                    # Return the processed trace without descriptors
                    descriptors.append({'data': trace})

            # Calculate HFC percentile for WOSS calculation
            hfc_values = []
            for desc in descriptors:
                if 'hfc' in desc:
                    hfc_values.extend(desc['hfc'])

            if hfc_values:
                import numpy as np
                # Get the user-configured HFC percentile (default to 95 if not specified)
                hfc_percentile = plot_settings.get('hfc_percentile', 95.0)
                hfc_p95 = np.percentile(hfc_values, hfc_percentile)
                # Store in plot_settings for use in analyze_data_page.py
                st.session_state.plot_settings['hfc_p95'] = float(hfc_p95)
                logging.info(f"Calculated HFC p{hfc_percentile}: {hfc_p95}")

                # Log additional information for debugging
                logging.info(f"HFC p{hfc_percentile} value stored in session state: {st.session_state.plot_settings.get('hfc_p95', 'Not set')}")

            # Return the descriptors (which include the processed traces)
            return descriptors
    except Exception as e:
        logging.warning(f"Could not calculate spectral descriptors: {e}")
        # Fall back to returning just the processed traces

    return processed_traces
