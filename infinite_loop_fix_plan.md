# Plan to Fix Infinite Refresh Loop on "View Results"

## 1. Problem Statement

The application (`app.py`) experiences an infinite refresh loop when the user selects "View Results" after completing Step 4, Option 1 (well marker-based analysis). Instead of displaying the spectral descriptor plots, the page continuously reloads.

## 2. Analysis of Current Flow & Problem Identification

### 2.1. Transition to "View Results"
- In `pages/analyze_data_page.py`, after the analysis for well markers (Option 1) is complete, clicking the "View Results" button sets `st.session_state.current_step = "view_results"` and triggers an `st.rerun()`.

### 2.2. Routing in `app.py`
- When `st.session_state.current_step` is "view_results", the main routing logic in `app.py` (lines 165-174) calls `render_export_results()` from `pages/export_results_page.py`.
  ```python
  # app.py
  165 | elif st.session_state.current_step in ["view_results", "export_process", "download_export", "export_results"]: 
  166 |     # ...
  174 |     render_export_results()
  ```
- Inside `pages/export_results_page.py`, the `render()` function (lines 64-89) checks `st.session_state.current_step`. If it's "view_results", it calls `render_view_results()` (defined within the same file, lines 597-1011). This function is responsible for displaying the plots.

### 2.3. Root Cause of the Infinite Loop
The infinite loop originates from the sidebar navigation logic in `app.py` (lines 127-136):
1.  When `st.session_state.current_step` is "view_results":
    *   The `current_display_step` (used to set the sidebar radio default) is correctly determined as "5. Export Results" (as per lines 114-115 in `app.py`).
    *   The `selected_display_step` from the sidebar radio button will therefore be "5. Export Results".
2.  `new_step = APP_STEPS[selected_display_step]` becomes `APP_STEPS["5. Export Results"]`, which is `"export_results"` (line 127).
3.  The condition `st.session_state.current_step != new_step` (line 128) evaluates to `"view_results" != "export_results"`, which is **true**.
4.  Because this condition is true, the code enters the `if` block. The `elif new_step == "export_results":` condition (line 132) is met.
5.  `st.session_state.current_step` is set to `"view_results"` (line 133). This doesn't change the state if it was already "view_results".
6.  Crucially, `st.rerun()` is called (line 136).

This cycle repeats indefinitely because the condition at line 128 remains true, leading to a constant rerun.

## 3. Comparison with Reference Implementation (`backup/app_ref.py`)

The reference implementation (`backup/app_ref.py`) handles the "view_results" state directly in its main `if/elif` structure (lines 2687 onwards). It does not have the same sidebar-driven sub-state update logic that is causing the issue in the current modularized `app.py`.

## 4. Proposed Fix

The fix involves modifying the sidebar navigation logic in `app.py` (lines 127-136) to correctly handle cases where the `current_step` is a sub-state (like "view_results") of a main navigation item (like "export_results").

**Proposed Code Change for `app.py` (around lines 127-136):**
```python
# Update current_step if navigation changes (MODIFIED LOGIC)
new_step_from_sidebar_key = APP_STEPS[selected_display_step]

# Determine the actual target page step based on the sidebar key
if new_step_from_sidebar_key == "select_mode":
    target_page_step = "select_mode"
elif new_step_from_sidebar_key == "export_results":
    # When "5. Export Results" is selected from sidebar, 
    # the default landing page within that section is "view_results".
    # The main routing logic further down will handle redirection if, 
    # for example, analysis is not complete.
    target_page_step = "view_results"
else:
    target_page_step = new_step_from_sidebar_key

# Only update and rerun if the current step is actually different from the target page step
if st.session_state.current_step != target_page_step:
    st.session_state.current_step = target_page_step
    st.rerun()
```

**Explanation of the Fix:**
1.  `new_step_from_sidebar_key`: Holds the direct value from `APP_STEPS` (e.g., "export_results").
2.  `target_page_step`: Determines the actual page state. If `new_step_from_sidebar_key` is "export_results", `target_page_step` is set to "view_results".
3.  The final `if st.session_state.current_step != target_page_step:` check correctly compares the current state with the actual target page. If they are the same (e.g., both "view_results"), no `st.rerun()` occurs, breaking the loop.

## 5. Plan Diagram

```mermaid
graph TD
    A[Start: Bug Report - Infinite Loop on View Results] --> B{1. Analyze Current Flow & Identify Root Cause};
    B --> B1[Trace app.py sidebar navigation logic];
    B1 --> B2[Identify: Comparison of 'view_results' (current) vs 'export_results' (sidebar key) causes unnecessary rerun];
    B2 --> C{2. Propose Solution};
    C --> C1[Modify app.py sidebar navigation logic];
    C1 --> C2[Determine 'target_page_step' correctly for 'export_results' key (should be 'view_results')];
    C2 --> C3[Only rerun if 'current_step' differs from actual 'target_page_step'];
    C3 --> D{3. Review Plan with User};
    D -- Approved --> E[Switch to Code Mode to Implement Fix in app.py];
    D -- Needs Changes --> C;